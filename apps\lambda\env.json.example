{"QueryKnowledgeBaseFunction": {"BEDROCK_KNOWLEDGE_BASE_ID": "your-knowledge-base-id-here", "BEDROCK_MODEL_ID": "us.anthropic.claude-sonnet-4-20250514-v1:0", "S3_BUCKET_NAME": "trialynx-clinical-trials-gov", "AWS_REGION": "us-west-2"}, "GetDocumentFunction": {"S3_BUCKET_NAME": "trialynx-clinical-trials-gov", "AWS_REGION": "us-west-2"}, "_comments": {"note1": "Copy this file to env.json and update with your actual values", "note2": "For local testing without AWS Bedrock, set BEDROCK_KNOWLEDGE_BASE_ID to 'SKIP_FOR_LOCAL_TESTING'", "note3": "The S3_BUCKET_NAME should contain your clinical trials documents", "note4": "Ensure AWS_REGION matches where your resources are deployed"}}