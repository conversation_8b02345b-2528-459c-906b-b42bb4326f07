import { APIGatewayTokenAuthorizerEvent, APIGatewayAuthorizerResult } from 'aws-lambda';
import { SecretsManagerClient, GetSecretValueCommand } from '@aws-sdk/client-secrets-manager';

const secretsClient = new SecretsManagerClient({ 
  region: process.env.AWS_REGION || 'us-east-1' 
});

let jwtSecret: string | null = null;

export const handler = async (
  event: APIGatewayTokenAuthorizerEvent
): Promise<APIGatewayAuthorizerResult> => {
  try {
    const token = event.authorizationToken;
    
    if (!token || !token.startsWith('Bearer ')) {
      throw new Error('Unauthorized');
    }
    
    const jwt = token.substring(7);
    
    // Get JWT secret if not cached
    if (!jwtSecret) {
      const command = new GetSecretValueCommand({
        SecretId: process.env.JWT_SECRET_ARN || 'trialynx-jwt-secret'
      });
      const response = await secretsClient.send(command);
      jwtSecret = response.SecretString || '';
    }
    
    // For MVP, we'll do basic validation
    // In production, this would properly validate JWT tokens
    const decoded = validateToken(jwt, jwtSecret);
    
    return generatePolicy(decoded.sub, 'Allow', event.methodArn, decoded);
    
  } catch (error) {
    console.error('Authorization error:', error);
    throw new Error('Unauthorized');
  }
};

function validateToken(token: string, secret: string): any {
  try {
    // For MVP, handle base64 encoded user payload from NextAuth.js
    const decoded = Buffer.from(token, 'base64').toString('utf-8');
    const userPayload = JSON.parse(decoded);
    
    // Basic validation
    if (!userPayload.sub || !userPayload.email) {
      throw new Error('Invalid token payload');
    }
    
    // Check expiry
    if (userPayload.exp && userPayload.exp < Math.floor(Date.now() / 1000)) {
      throw new Error('Token expired');
    }
    
    return userPayload;
  } catch (error) {
    console.error('Token validation error:', error);
    throw new Error('Invalid token');
  }
}

function generatePolicy(
  principalId: string, 
  effect: string, 
  resource: string,
  context: any
): APIGatewayAuthorizerResult {
  const authResponse: APIGatewayAuthorizerResult = {
    principalId: principalId,
    policyDocument: {
      Version: '2012-10-17',
      Statement: [
        {
          Action: 'execute-api:Invoke',
          Effect: effect,
          Resource: resource
        }
      ]
    },
    context: {
      userId: context.sub,
      email: context.email,
      roles: JSON.stringify(context.roles)
    }
  };
  
  return authResponse;
}