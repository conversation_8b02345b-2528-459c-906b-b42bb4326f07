import { 
  BedrockAgentRuntimeClient, 
  RetrieveCommand,
  RetrieveAndGenerateCommand,
  RetrieveAndGenerateCommandInput,
} from '@aws-sdk/client-bedrock-agent-runtime';
import type { BedrockQueryRequest, BedrockQueryResponse } from '../types/index';

// Prompt templates for different insight types
export const PROMPT_TEMPLATES = {
  phase: (context: any) => `You are a clinical trial design expert analyzing similar studies from a knowledge base.

Study Context:
- Type: ${context.studyType || 'drug'}
- Condition: ${context.condition || 'not specified'}
- Drug Class: ${context.drugClass || 'not specified'}
- Compound: ${context.isNewCompound ? 'Novel/First-in-class' : 'Existing/Repurposed'}
- Mechanism: ${context.mechanism || 'not specified'}

Based on the retrieved trials, provide your analysis in this EXACT format:

## PHASE RECOMMENDATION
[State the recommended phase clearly: Phase 1, Phase 1/2, Phase 2, Phase 2/3, or Phase 3]

## KEY DESIGN ELEMENTS
- Patient Population: [Target enrollment number and type]
- Duration: [Expected timeline]
- Primary Objective: [Main goal for this phase]

## RATIONALE
[Explain why this phase is appropriate based on the compound type and similar studies]

## CRITICAL CONSIDERATIONS
[List 2-3 key challenges or requirements specific to this phase]

## SIMILAR SUCCESSFUL STUDIES
[Cite specific NCT numbers of comparable trials in this phase]

Important: The PHASE RECOMMENDATION must be a single, clear statement.

$search_results$

$output_format_instructions$`,

  'primary-endpoint': (context: any) => `You are a clinical trial endpoints expert analyzing similar studies.

Study Context:
- Study Type: ${context.studyType || 'drug'}
- Condition: ${context.condition || 'not specified'}
- Phase: ${context.phase || 'not specified'}
- Drug/Device Class: ${context.drugClass || context.deviceClass || 'not specified'}
- Novel Compound: ${context.isNewCompound ? 'Yes - First-in-class' : 'No - Existing/Repurposed'}
- Mechanism of Action: ${context.mechanism || 'not specified'}

Study Design Parameters (Already Selected):
- Design Type: ${context.designType || 'not specified'}
- Blinding: ${context.blinding || 'not specified'}
- Control Type: ${context.controlType || 'not specified'}
- Randomization Ratio: ${context.randomizationRatio || 'not specified'}

Based on the retrieved similar trials below, provide specific primary endpoint recommendations in the following JSON format:

$search_results$

## YOUR ANALYSIS
Provide your recommendations in the following JSON format:

{
  "primaryEndpoint": {
    "recommendation": "The specific primary endpoint (e.g., 'Change in HbA1c from baseline to Week 12')",
    "description": "Detailed description of what this endpoint measures and why it's appropriate",
    "measurementMethod": "How to measure (e.g., 'Laboratory blood test', 'Clinical assessment scale')",
    "timepoint": "When to measure (e.g., 'Baseline and Week 12', 'Every 4 weeks')",
    "clinicalRelevance": "Why this endpoint is clinically meaningful for this indication"
  },
  "measurementSpecifications": {
    "scale": "If using a scale, specify which one",
    "units": "Units of measurement",
    "minimumClinicallyImportantDifference": "MCID if known",
    "analysisMethod": "Primary analysis method (e.g., 'Change from baseline', 'Responder analysis')"
  },
  "alternatives": [
    {
      "endpoint": "Alternative endpoint option 1",
      "rationale": "Why this could be considered",
      "measurementMethod": "How to measure this endpoint",
      "advantages": "Benefits of this endpoint",
      "disadvantages": "Limitations or challenges"
    },
    {
      "endpoint": "Alternative endpoint option 2",
      "rationale": "Why this could be considered",
      "measurementMethod": "How to measure this endpoint",
      "advantages": "Benefits of this endpoint",
      "disadvantages": "Limitations or challenges"
    }
  ],
  "sampleSize": {
    "estimated": "Estimated sample size (e.g., '200 patients')",
    "perArm": "Sample size per treatment arm",
    "assumptions": "Key assumptions (effect size, power, alpha)",
    "adjustments": "Adjustments for dropout, multiplicity, etc."
  },
  "designConsiderations": {
    "blindingImpact": "How the selected blinding affects endpoint assessment",
    "controlTypeAlignment": "How this endpoint aligns with the chosen control type",
    "crossoverSuitability": "If crossover design, is this endpoint appropriate for repeated measures"
  },
  "citations": [
    {
      "nctNumber": "NCT number",
      "title": "Trial title",
      "endpoint": "Primary endpoint used in this trial",
      "outcome": "Whether the trial met its primary endpoint"
    }
  ]
}

Important: The recommendation should be specific and directly applicable, considering the study design parameters already selected.

$output_format_instructions$`,

  'secondary-endpoints': (context: any) => `You are a clinical trial design expert focusing on comprehensive outcome assessment.

Study Context:
- Condition: ${context.condition || 'not specified'}
- Phase: ${context.phase || 'not specified'}
- Study Type: ${context.studyType || 'not specified'}
- Primary Endpoint: ${context.primaryEndpoint || 'Not yet determined'}

Based on the retrieved clinical trials and studies below, provide specific secondary endpoint recommendations that complement the primary endpoint.

$search_results$

## SECONDARY ENDPOINTS RECOMMENDATION
Provide a concise overview of recommended secondary endpoints that support and complement the primary endpoint.

## EFFICACY ENDPOINTS
List 3-5 efficacy-related secondary endpoints that support the primary efficacy claim:
- Include response rates, remission rates, or time-to-event measures
- Consider clinician-rated and patient-reported outcomes
- Cite specific scales or instruments used in the retrieved trials

## SAFETY ENDPOINTS
List 3-5 safety monitoring endpoints:
- Adverse events and serious adverse events
- Specific safety concerns for this condition/intervention
- Laboratory parameters and vital signs
- Include specific assessment tools (e.g., C-SSRS for suicidality)

## QUALITY OF LIFE ENDPOINTS
List 2-3 QoL or functional measures:
- Disease-specific and general QoL instruments
- Functional assessments relevant to the condition
- Patient-reported outcomes

## EXPLORATORY ENDPOINTS
List 2-3 exploratory endpoints if applicable:
- Biomarkers or pharmacokinetic parameters
- Subgroup analyses
- Novel assessments

## RATIONALE
Explain why these secondary endpoints complement the primary endpoint and support regulatory requirements.

## SIMILAR SUCCESSFUL STUDIES
Cite 2-3 specific NCT numbers from the retrieved studies that used similar endpoint strategies.

$output_format_instructions$`,

  'study-duration': (context: any) => `You are a clinical operations expert analyzing trial timelines.

Study Context:
- Condition: ${context.condition || 'not specified'}
- Phase: ${context.phase || 'not specified'}
- Intervention type: ${context.studyType || 'not specified'}

Based on the retrieved clinical trials below, provide specific timeline recommendations.

$search_results$

Analyze the similar trials and provide your recommendations in the following JSON format:

{
  "treatmentDuration": {
    "recommended": "12 weeks",
    "range": "8-16 weeks",
    "rationale": "Most Phase 2 trials for this condition use 12 weeks of active treatment to demonstrate efficacy"
  },
  "followUpPeriod": {
    "recommended": "4 weeks",
    "range": "2-8 weeks",
    "rationale": "Standard safety follow-up period to monitor for delayed adverse events"
  },
  "keyTimepoints": [
    "Baseline (Week 0)",
    "Week 2 (safety assessment)",
    "Week 4 (early efficacy)",
    "Week 8 (interim analysis)",
    "Week 12 (primary endpoint)",
    "Week 16 (follow-up)"
  ],
  "totalDuration": {
    "estimated": "18 months",
    "breakdown": {
      "recruitment": "6 months",
      "treatment": "12 weeks per patient",
      "followUp": "4 weeks per patient",
      "dataAnalysis": "3 months"
    }
  },
  "citations": ["NCT12345678", "NCT23456789", "NCT34567890"]
}

IMPORTANT: 
- Provide specific durations based on the retrieved studies
- Include NCT numbers from studies that used similar timelines
- Keep duration values in practical units (weeks or months)
- Ensure all fields are populated with relevant data

$output_format_instructions$`,

  'site-selection': (context: any) => `You are a clinical trial site selection expert.

Study Context:
- Phase: ${context.phase || 'not specified'}
- Condition: ${context.condition || 'not specified'}
- Population requirements: General adult population

Based on retrieved trials, recommend:
1. Optimal number of sites
2. Geographic distribution strategy
3. Site capability requirements
4. Typical recruitment rates per site

Consider both efficiency and regulatory requirements.

Retrieved Studies:
$search_results$

$output_format_instructions$`,

  'safety-monitoring': (context: any) => `You are a clinical safety expert designing monitoring strategies.

Study Context:
- Phase: ${context.phase || 'not specified'}
- Drug Class: ${context.drugClass || 'not specified'}
- Novel compound: ${context.isNewCompound ? 'Yes' : 'No'}

Based on retrieved trials with similar interventions, recommend:
1. Safety run-in period requirements
2. DSMB necessity and review frequency
3. Safety reporting timelines
4. Stopping rules and futility boundaries
5. Special safety assessments needed

Focus on regulatory expectations and patient safety.

Retrieved Studies:
$search_results$

$output_format_instructions$`,

  'design-parameters': (context: any) => `You are a clinical trial design expert analyzing optimal study design parameters.

Study Context:
- Study Type: ${context.studyType || 'drug'}
- Condition: ${context.condition || 'not specified'}
- Phase: ${context.phase || 'not specified'}
- Drug/Device Class: ${context.drugClass || context.deviceClass || 'not specified'}
- Novel Compound: ${context.isNewCompound ? 'Yes - First-in-class' : 'No - Existing/Repurposed'}
- Mechanism of Action: ${context.mechanism || 'not specified'}
- Primary Endpoint: ${context.primaryEndpoint || 'To be determined'}

Based on the retrieved similar trials below, provide specific recommendations for study design parameters in the following JSON format:

$search_results$

## YOUR ANALYSIS
Provide your recommendations in the following JSON format:

{
  "designType": {
    "recommendation": "parallel" | "crossover" | "factorial" | "adaptive" | "platform" | "basket" | "umbrella" | "sequential" | "cluster" | "stepped-wedge" | "n-of-1" | "single-arm",
    "rationale": "Explain why this design type is most appropriate based on phase, condition, and similar trials",
    "examples": ["NCT12345678", "NCT87654321"]
  },
  "blinding": {
    "recommendation": "open-label" | "single-blind" | "double-blind" | "triple-blind" | "quadruple-blind",
    "rationale": "Explain the blinding strategy considering feasibility and regulatory expectations",
    "considerations": ["List key factors like safety monitoring needs", "Intervention characteristics that affect blinding"]
  },
  "controlType": {
    "recommendation": "placebo" | "active-comparator" | "standard-of-care" | "dose-comparison" | "sham" | "historical" | "waitlist" | "usual-care" | "no-treatment" | "none",
    "specificComparator": "If active comparator, specify drug and dose",
    "rationale": "Explain why this control type is appropriate for this phase and indication",
    "ethicalConsiderations": "Address any ethical concerns with the chosen control"
  },
  "randomizationRatio": {
    "recommendation": "1:1" | "2:1" | "3:1" | "1:1:1" | other ratio as string,
    "rationale": "Explain the ratio choice based on phase, safety data needs, and power",
    "sampleSizeImpact": "How this ratio affects required sample size"
  },
  "additionalConsiderations": {
    "stratificationFactors": ["List key stratification variables if applicable"],
    "interimAnalyses": "Whether interim analyses are recommended",
    "adaptiveElements": "Any adaptive design elements to consider",
    "regulatoryPrecedent": "Key regulatory considerations from similar approved trials"
  },
  "citations": [
    {
      "nctNumber": "NCT number",
      "title": "Trial title",
      "relevance": "Why this trial supports your recommendations"
    }
  ]
}

$output_format_instructions$`,

  'demographics': (context: any) => `You are a clinical trial population expert analyzing demographic requirements for optimal patient recruitment.

Study Context:
- Study Type: ${context.studyType || 'drug'}
- Condition: ${context.condition || 'not specified'}
- Phase: ${context.phase || 'not specified'}
- Primary Endpoint: ${context.primaryEndpoint || 'not specified'}
- Target Enrollment: ${context.targetEnrollment || 'not specified'}
- Study Design: ${context.designType || 'not specified'}
- Geographic Scope: ${context.geographicScope || 'not specified'}

Current Settings (to refine/validate):
- Current Age Range: ${context.currentAgeMin || 'not set'} - ${context.currentAgeMax || 'not set'} years
- Current Gender: ${context.currentGender || 'not set'}
- Healthy Volunteers: ${context.currentHealthyVolunteers ? 'Currently accepting' : 'Currently not accepting'}

Based on the retrieved similar trials, provide demographic recommendations in this EXACT JSON format:

{
  "ageRange": {
    "recommendedMin": "Minimum age in years as a number (e.g., '18')",
    "recommendedMax": "Maximum age in years as a number (e.g., '65')",
    "rationale": "Why this age range is appropriate for this condition and phase",
    "typicalRange": {
      "min": "Most common minimum age in similar studies",
      "max": "Most common maximum age in similar studies"
    }
  },
  "gender": {
    "recommendation": "all" | "male" | "female",
    "rationale": "Explanation for gender requirement",
    "distribution": "Typical gender distribution in similar studies (e.g., '50% male, 50% female')"
  },
  "specificPopulations": {
    "recommended": ["List of specific populations to consider, e.g., 'Elderly (65+)', 'Treatment-resistant patients'"],
    "avoid": ["Populations typically excluded, e.g., 'Pregnant women', 'Patients with severe renal impairment'"],
    "rationale": "Why these populations are important or should be excluded"
  },
  "healthyVolunteers": {
    "recommendation": true | false,
    "rationale": "Why healthy volunteers should or should not be included",
    "typicalApproach": "What percentage of similar studies include healthy volunteers"
  },
  "demographicConsiderations": [
    "Important demographic factors specific to this condition",
    "Diversity and representation requirements",
    "Special safety considerations for certain populations"
  ],
  "benchmarkStudies": [
    {
      "nctNumber": "Actual NCT identifier (format: NCTxxxxxxxx) - extract from retrieved documents",
      "ageRange": "e.g., '18-65 years'",
      "gender": "all | male | female",
      "enrolledPopulation": "Brief description of who was enrolled"
    }
  ]
}

IMPORTANT: 
1. Base all recommendations on actual demographic profiles from the retrieved similar studies
2. Extract real NCT numbers from retrieved documents for benchmarkStudies
3. Be specific about age ranges using actual numbers, not descriptive terms
4. Consider safety and ethical factors when recommending demographics

$search_results$

$output_format_instructions$`,

  'sample-size': (context: any) => `You are a clinical trial biostatistician and enrollment planning expert analyzing similar studies.

Study Context:
- Study Type: ${context.studyType || 'drug'}
- Condition: ${context.condition || 'not specified'}
- Phase: ${context.phase || 'not specified'}
- Primary Endpoint: ${context.primaryEndpoint || 'not specified'}
- Design Type: ${context.designType || 'parallel group'}
- Control Type: ${context.controlType || 'placebo-controlled'}
- Randomization Ratio: ${context.randomizationRatio || '1:1'}

Based on the retrieved similar trials, provide sample size recommendations in this EXACT JSON format:

{
  "targetEnrollment": {
    "recommended": "Total number of patients (e.g., '120')",
    "range": {
      "min": "Minimum reasonable sample size",
      "max": "Maximum reasonable sample size"
    },
    "rationale": "Why this sample size is appropriate for this study"
  },
  "perArm": {
    "treatmentArm": "Number of patients in treatment arm",
    "controlArm": "Number of patients in control arm",
    "otherArms": "If applicable, describe other arms"
  },
  "statisticalAssumptions": {
    "power": "Statistical power (e.g., '80%', '90%')",
    "alpha": "Significance level (e.g., '0.05')",
    "effectSize": "Expected effect size or minimum clinically important difference",
    "dropoutRate": "Expected dropout rate percentage"
  },
  "adjustments": {
    "screenFailRate": "Expected screen failure rate percentage",
    "totalToScreen": "Total patients needed to screen",
    "enrollmentBuffer": "Recommended buffer percentage for dropouts"
  },
  "enrollmentProjections": {
    "monthlyRate": "Expected patients enrolled per month across all sites",
    "timeToFullEnrollment": "Estimated months to complete enrollment",
    "sitesNeeded": "Approximate number of sites needed"
  },
  "benchmarkStudies": [
    {
      "nctNumber": "The actual NCT identifier from the retrieved study (format: NCTxxxxxxxx, e.g., 'NCT12345678') - extract this from the study documents, NOT a description",
      "enrolledPatients": "Number enrolled (e.g., '120')",
      "completionRate": "Percentage who completed (e.g., '85%')"
    }
  ],
  "considerations": [
    "Special considerations for this population",
    "Recruitment challenges to anticipate"
  ]
}

IMPORTANT: 
1. Base your recommendations on the actual enrollment numbers and completion rates from the retrieved studies
2. For benchmarkStudies, you MUST extract the actual NCT identifiers (NCTxxxxxxxx format) from the retrieved documents
3. Do NOT use descriptive text like "Phase 2 study" or "MDD trial" in the nctNumber field
4. Each NCT number should be in the format NCT followed by 8 digits (e.g., NCT12345678)
5. If you cannot find a valid NCT number for a study, do not include it in benchmarkStudies
6. Be specific and quantitative in all fields

$search_results$

$output_format_instructions$`,

  'inclusion-criteria': (context: any) => `You are a clinical trial eligibility expert analyzing inclusion criteria from similar studies.

Study Context:
- Study Type: ${context.studyType || 'drug'}
- Condition: ${context.condition || 'not specified'}
- Phase: ${context.phase || 'not specified'}
- Drug/Device Class: ${context.drugClass || context.deviceClass || 'not specified'}
- Primary Endpoint: ${context.primaryEndpoint || 'not specified'}

Based on the retrieved similar trials, provide inclusion criteria recommendations in this EXACT JSON format:

{
  "coreInclusionCriteria": {
    "diagnosis": [
      "Primary diagnosis requirement (e.g., 'Confirmed diagnosis of major depressive disorder per DSM-5')",
      "Duration of diagnosis requirement (e.g., 'Symptoms present for at least 3 months')"
    ],
    "clinicalPresentation": [
      "Symptom severity requirements (e.g., 'HAM-D score ≥18 at screening')",
      "Clinical status requirements (e.g., 'Current major depressive episode')"
    ],
    "treatmentHistory": [
      "Prior treatment requirements (e.g., 'Failed at least one prior antidepressant')",
      "Washout period requirements (e.g., 'No antidepressants for 2 weeks prior')"
    ]
  },
  "demographicCriteria": {
    "age": "Age requirements (e.g., '18-65 years')",
    "gender": "Gender-specific requirements if applicable",
    "other": [
      "BMI requirements if applicable",
      "Language or literacy requirements"
    ]
  },
  "laboratoryRequirements": {
    "required": [
      "Essential lab values (e.g., 'Normal liver function tests')",
      "Required test results (e.g., 'Negative pregnancy test')"
    ],
    "optional": [
      "Preferred but not mandatory lab criteria"
    ]
  },
  "consentRequirements": [
    "Ability to provide written informed consent",
    "Willingness to comply with study procedures",
    "Reliable contraception if applicable"
  ],
  "prioritizedList": [
    "Most critical inclusion criterion #1",
    "Most critical inclusion criterion #2",
    "Most critical inclusion criterion #3",
    "Additional important criteria..."
  ],
  "considerations": [
    "Special considerations for this population",
    "Recruitment facilitators to consider"
  ],
  "benchmarkStudies": [
    {
      "nctNumber": "Actual NCT identifier (format: NCTxxxxxxxx) - extract from retrieved documents",
      "keyInclusionCriteria": [
        "Main inclusion criteria from this study"
      ]
    }
  ]
}

IMPORTANT:
1. Base recommendations on actual inclusion criteria from retrieved similar studies
2. Be specific with numerical thresholds and timeframes
3. Extract real NCT numbers from retrieved documents for benchmarkStudies
4. Prioritize criteria that are most critical for patient safety and study validity
5. Consider feasibility of recruitment when recommending criteria

$search_results$

$output_format_instructions$`,

  'inclusion-exclusion': (context: any) => `You are a clinical trial eligibility expert analyzing inclusion criteria from similar studies.

Study Context:
- Study Type: ${context.studyType || 'drug'}
- Condition: ${context.condition || 'not specified'}
- Phase: ${context.phase || 'not specified'}
- Drug/Device Class: ${context.drugClass || context.deviceClass || 'not specified'}
- Primary Endpoint: ${context.primaryEndpoint || 'not specified'}

Current Criteria (to refine/validate):
- Current Inclusion: ${context.currentInclusionCriteria && context.currentInclusionCriteria.length > 0 ? context.currentInclusionCriteria.slice(0, 3).join('; ') + (context.currentInclusionCriteria.length > 3 ? '...' : '') : 'None set'}
- Current Exclusion: ${context.currentExclusionCriteria && context.currentExclusionCriteria.length > 0 ? context.currentExclusionCriteria.slice(0, 3).join('; ') + (context.currentExclusionCriteria.length > 3 ? '...' : '') : 'None set'}

Based on the retrieved similar trials, provide inclusion criteria recommendations in this EXACT JSON format:

{
  "coreInclusionCriteria": {
    "diagnosis": [
      "Primary diagnosis requirement (e.g., 'Confirmed diagnosis of major depressive disorder per DSM-5')",
      "Duration of diagnosis requirement (e.g., 'Symptoms present for at least 3 months')"
    ],
    "clinicalPresentation": [
      "Symptom severity requirements (e.g., 'HAM-D score ≥18 at screening')",
      "Clinical status requirements (e.g., 'Current major depressive episode')"
    ],
    "treatmentHistory": [
      "Prior treatment requirements (e.g., 'Failed at least one prior antidepressant')",
      "Washout period requirements (e.g., 'No antidepressants for 2 weeks prior')"
    ]
  },
  "demographicCriteria": {
    "age": "Age requirements (e.g., '18-65 years')",
    "gender": "Gender-specific requirements if applicable",
    "other": [
      "BMI requirements if applicable",
      "Language or literacy requirements"
    ]
  },
  "laboratoryRequirements": {
    "required": [
      "Essential lab values (e.g., 'Normal liver function tests')",
      "Required test results (e.g., 'Negative pregnancy test')"
    ],
    "optional": [
      "Preferred but not mandatory lab criteria"
    ]
  },
  "consentRequirements": [
    "Ability to provide written informed consent",
    "Willingness to comply with study procedures",
    "Reliable contraception if applicable"
  ],
  "prioritizedList": [
    "Most critical inclusion criterion #1",
    "Most critical inclusion criterion #2",
    "Most critical inclusion criterion #3",
    "Additional important criteria..."
  ],
  "considerations": [
    "Special considerations for this population",
    "Recruitment facilitators to consider"
  ],
  "benchmarkStudies": [
    {
      "nctNumber": "Actual NCT identifier (format: NCTxxxxxxxx) - extract from retrieved documents",
      "keyInclusionCriteria": [
        "Main inclusion criteria from this study"
      ]
    }
  ]
}

IMPORTANT:
1. Base all recommendations on actual inclusion criteria from retrieved similar studies
2. Extract real NCT numbers from retrieved documents for benchmarkStudies
3. Prioritize the most important criteria in the prioritizedList
4. Consider study phase - Phase 1 has different criteria than Phase 3
5. Focus on criteria that will ensure patient safety and data quality

$search_results$

$output_format_instructions$`,

  'exclusion-criteria': (context: any) => `You are a clinical trial safety expert analyzing exclusion criteria from similar studies.

Study Context:
- Study Type: ${context.studyType || 'drug'}
- Condition: ${context.condition || 'not specified'}
- Phase: ${context.phase || 'not specified'}
- Drug/Device Class: ${context.drugClass || context.deviceClass || 'not specified'}
- Primary Endpoint: ${context.primaryEndpoint || 'not specified'}

Based on the retrieved similar trials, provide exclusion criteria recommendations in this EXACT JSON format:

{
  "medicalExclusions": {
    "comorbidities": [
      "Conditions that would exclude (e.g., 'Bipolar disorder or psychotic features')",
      "Serious medical conditions (e.g., 'Unstable cardiovascular disease')"
    ],
    "concomitantMedications": [
      "Prohibited medications (e.g., 'MAO inhibitors within 14 days')",
      "Restricted drug classes (e.g., 'Strong CYP3A4 inhibitors')"
    ],
    "allergiesContraindications": [
      "Drug allergies or hypersensitivities",
      "Contraindications to study drug class"
    ]
  },
  "safetyExclusions": {
    "pregnancy": "Pregnancy and nursing exclusions (e.g., 'Pregnant, nursing, or planning pregnancy')",
    "laboratoryValues": [
      "Unsafe lab ranges (e.g., 'ALT/AST >3x upper limit of normal')",
      "Renal function limits (e.g., 'eGFR <30 mL/min/1.73m²')"
    ],
    "vitalSigns": [
      "Unsafe vital parameters (e.g., 'Uncontrolled hypertension >160/100 mmHg')",
      "ECG abnormalities if applicable"
    ],
    "suicideRisk": "Suicide risk criteria if applicable (e.g., 'Active suicidal ideation with plan')"
  },
  "protocolExclusions": {
    "priorParticipation": "Previous trial restrictions (e.g., 'Participation in investigational drug study within 30 days')",
    "geographicRestrictions": "Location/travel requirements (e.g., 'Unable to attend all study visits')",
    "complianceConcerns": [
      "Factors affecting compliance (e.g., 'History of poor medication adherence')",
      "Substance use restrictions (e.g., 'Alcohol/drug dependence within past year')"
    ]
  },
  "prioritizedList": [
    "Most critical safety exclusion #1",
    "Most critical safety exclusion #2",
    "Most critical safety exclusion #3",
    "Additional important exclusions..."
  ],
  "riskMitigation": [
    "How to effectively screen for these exclusions",
    "Safety monitoring recommendations during the trial"
  ],
  "benchmarkStudies": [
    {
      "nctNumber": "Actual NCT identifier (format: NCTxxxxxxxx) - extract from retrieved documents",
      "keyExclusionCriteria": [
        "Main exclusion criteria from this study"
      ]
    }
  ]
}

IMPORTANT:
1. Base recommendations on actual exclusion criteria from retrieved similar studies
2. Prioritize patient safety above all other considerations
3. Be specific with numerical thresholds and timeframes
4. Extract real NCT numbers from retrieved documents for benchmarkStudies
5. Consider both safety risks and data quality when recommending exclusions
6. Include standard safety exclusions for the drug class/condition

$search_results$

$output_format_instructions$`,

  'study-periods': (context: any) => `You are a clinical trial timeline expert analyzing study duration patterns.

Study Context:
- Study Type: ${context.studyType || 'drug'}
- Condition: ${context.condition || 'not specified'}
- Phase: ${context.phase || 'not specified'}
- Primary Endpoint: ${context.primaryEndpoint || 'not specified'}

Based on the retrieved similar trials, provide study period recommendations in this EXACT JSON format:

{
  "studyPeriods": {
    "screening": {
      "duration": "Duration in weeks/days (e.g., '2-4 weeks')",
      "typicalRange": "Common range seen in similar studies",
      "rationale": "Why this screening duration is appropriate"
    },
    "baseline": {
      "duration": "Duration in weeks/days (e.g., '1 week')",
      "typicalRange": "Common range seen in similar studies",
      "rationale": "Purpose of baseline period"
    },
    "treatment": {
      "duration": "Duration in weeks/months (e.g., '12 weeks')",
      "typicalRange": "Common range seen in similar studies",
      "rationale": "Why this treatment duration is optimal"
    },
    "followUp": {
      "duration": "Duration in weeks/months (e.g., '4 weeks')",
      "typicalRange": "Common range seen in similar studies",
      "rationale": "Importance of follow-up duration"
    },
    "totalDuration": "Total study duration (e.g., '19-21 weeks')"
  },
  "durationFactors": [
    "Key factors affecting study duration",
    "Recruitment challenges to consider",
    "Regulatory requirements"
  ],
  "benchmarkStudies": [
    {
      "nctNumber": "Actual NCT identifier (format: NCTxxxxxxxx)",
      "screeningDuration": "e.g., '4 weeks'",
      "treatmentDuration": "e.g., '12 weeks'",
      "followUpDuration": "e.g., '4 weeks'",
      "totalDuration": "e.g., '20 weeks'"
    }
  ],
  "recommendations": [
    "Consider extending screening if recruitment is challenging",
    "Include run-in period if medication washout needed"
  ]
}

IMPORTANT:
1. Base durations on actual study timelines from retrieved similar trials
2. Use specific time units (weeks, months, days) consistently
3. Extract real NCT numbers from retrieved documents
4. Consider both efficacy assessment needs and practical feasibility
5. Account for the primary endpoint timing requirements

$search_results$

$output_format_instructions$`,


  'site-planning': (context: any) => `You are a clinical trial operations expert specializing in site selection and planning.

Study Context:
- Study Type: ${context.studyType || 'drug'}
- Condition: ${context.condition || 'not specified'}
- Phase: ${context.phase || 'not specified'}
- Target Enrollment: ${context.targetEnrollment || 'not specified'}
- Geographic Scope: ${context.geographicScope || 'not specified'}

Based on the retrieved similar trials, provide site planning recommendations in this EXACT JSON format:

{
  "sitePlanning": {
    "numberOfSites": {
      "recommended": "15-20",
      "minimum": "10",
      "maximum": "25",
      "rationale": "Based on enrollment targets and typical site capacity"
    },
    "recruitmentRate": {
      "expected": "2-3 patients/site/month",
      "range": "1-5 patients/site/month",
      "factors": [
        "Competition from other trials",
        "Disease prevalence in region",
        "Site experience level"
      ]
    },
    "geographicDistribution": {
      "recommended": [
        {
          "region": "North America",
          "countries": ["United States", "Canada"],
          "siteCount": "8-10",
          "rationale": "Strong regulatory infrastructure and patient access"
        },
        {
          "region": "Europe",
          "countries": ["Germany", "United Kingdom", "Spain"],
          "siteCount": "5-7",
          "rationale": "Experienced sites and centralized ethics"
        }
      ],
      "considerations": [
        "Regulatory timeline differences",
        "Language and cultural factors",
        "Cost variations by region"
      ]
    },
    "siteSelectionCriteria": {
      "essential": [
        "Previous experience with condition",
        "Adequate patient population",
        "Required equipment and facilities"
      ],
      "preferred": [
        "Electronic data capture experience",
        "Dedicated research coordinator",
        "Good enrollment track record"
      ]
    }
  },
  "supportingEvidence": [
    {
      "nctNumber": "NCT number",
      "sites": "Number of sites used",
      "enrollmentTime": "Time to full enrollment",
      "relevance": "Why this example is relevant"
    }
  ]
}

IMPORTANT:
1. Base recommendations on actual site numbers from similar successful trials
2. Consider the complexity of the protocol when recommending site numbers
3. Account for regional differences in recruitment rates
4. Be specific about geographic distribution based on the condition
5. Include practical considerations for site selection

$search_results$

$output_format_instructions$`,

  'enrollment-projections': (context: any) => `You are a clinical trial enrollment and retention expert.

Study Context:
- Study Type: ${context.studyType || 'drug'}
- Condition: ${context.condition || 'not specified'}
- Phase: ${context.phase || 'not specified'}
- Target Enrollment: ${context.targetEnrollment || 'not specified'}
- Number of Sites: ${context.numberOfSites || 'not specified'}

Based on the retrieved similar trials, provide enrollment projections in this EXACT JSON format:

{
  "enrollmentProjections": {
    "screenFailureRate": {
      "expected": "20-25%",
      "range": "15-30%",
      "commonReasons": [
        "Does not meet inclusion criteria",
        "Meets exclusion criteria",
        "Withdrawal of consent"
      ],
      "mitigationStrategies": [
        "Pre-screening phone calls",
        "Clear communication of requirements",
        "Streamlined screening process"
      ]
    },
    "dropoutRate": {
      "expected": "10-15%",
      "byPhase": {
        "screening": "5%",
        "treatment": "8%",
        "followUp": "2%"
      },
      "commonReasons": [
        "Adverse events",
        "Lack of efficacy",
        "Lost to follow-up",
        "Withdrawal of consent"
      ],
      "retentionStrategies": [
        "Regular patient engagement",
        "Flexible visit scheduling",
        "Transportation assistance"
      ]
    },
    "enrollmentTimeline": {
      "monthsToFullEnrollment": "6-8",
      "rampUpPeriod": "2 months",
      "peakEnrollmentRate": "20 patients/month",
      "assumptions": [
        "All sites activated within 3 months",
        "Steady recruitment after ramp-up",
        "No major protocol amendments"
      ]
    },
    "overEnrollment": {
      "recommendedBuffer": "5-10%",
      "rationale": "Account for dropouts before primary endpoint",
      "strategy": "Continue screening until last patient completes"
    }
  },
  "benchmarkStudies": [
    {
      "nctNumber": "NCT number",
      "actualScreenFailure": "Actual rate achieved",
      "actualDropout": "Actual dropout rate",
      "enrollmentDuration": "Time to full enrollment",
      "lessons": "Key learnings from this trial"
    }
  ]
}

IMPORTANT:
1. Use actual screen failure and dropout rates from similar trials
2. Consider disease severity when projecting dropout rates
3. Account for seasonal variations in enrollment if applicable
4. Be realistic about site activation timelines
5. Include buffer for unexpected delays

$search_results$

$output_format_instructions$`,

  'data-monitoring': (context: any) => `You are a clinical trial data management and monitoring expert.

Study Context:
- Study Type: ${context.studyType || 'drug'}
- Phase: ${context.phase || 'not specified'}
- Condition: ${context.condition || 'not specified'}
- Number of Sites: ${context.numberOfSites || 'not specified'}
- Geographic Scope: ${context.geographicScope || 'not specified'}

Based on the retrieved similar trials, provide data management and monitoring recommendations in this EXACT JSON format:

{
  "dataMonitoring": {
    "dataManagement": {
      "recommendedSystem": "edc",
      "systemOptions": {
        "edc": {
          "advantages": [
            "Real-time data access",
            "Built-in edit checks",
            "Reduced transcription errors"
          ],
          "considerations": [
            "Training requirements",
            "Internet connectivity needs",
            "System validation"
          ]
        },
        "paper": {
          "when": "Low-resource settings or simple protocols",
          "requirements": "Double data entry for verification"
        },
        "hybrid": {
          "when": "Mixed site capabilities",
          "approach": "Core data in EDC, source documents on paper"
        }
      },
      "rationale": "EDC standard for Phase 2/3 trials with multiple sites"
    },
    "monitoringApproach": {
      "recommended": "[MUST BE ONE OF: 'on-site', 'remote', 'risk-based', or 'hybrid']",
      "rationale": "Why this approach is recommended for this study",
      "monitoringPlan": {
        "onSite": {
          "frequency": "Quarterly or after every 5 patients",
          "focus": "Consent verification, drug accountability, safety"
        },
        "remote": {
          "frequency": "Monthly",
          "focus": "Data review, query resolution, enrollment tracking"
        },
        "centralized": {
          "frequency": "Continuous",
          "focus": "Statistical monitoring, trend analysis, risk indicators"
        }
      },
      "riskIndicators": [
        "High screen failure rate",
        "Protocol deviations",
        "Data query rate > 10%",
        "Safety signal detection"
      ]
    },
    "dataQuality": {
      "targetMetrics": {
        "queryRate": "< 5% of data points",
        "resolveTime": "< 7 days average",
        "completeness": "> 98% at database lock"
      },
      "qualityControls": [
        "Programmatic edit checks",
        "Medical review of safety data",
        "Statistical monitoring for outliers"
      ]
    },
    "technology": {
      "recommended": [
        "21 CFR Part 11 compliant EDC system",
        "Central randomization system",
        "Drug supply management system",
        "Safety database integration"
      ],
      "optional": [
        "ePRO for patient-reported outcomes",
        "eConsent platform",
        "Risk-based monitoring platform"
      ]
    }
  },
  "industryBenchmarks": [
    {
      "nctNumber": "NCT number",
      "dataSystem": "System used",
      "monitoringApproach": "Approach taken",
      "outcomes": "Quality metrics achieved",
      "relevance": "Why this example applies"
    }
  ]
}

IMPORTANT:
1. Align monitoring intensity with trial risk level
2. Consider regulatory requirements for the phase
3. Balance quality with cost-effectiveness
4. Account for site experience with technology
5. Base on successful approaches from similar trials

$search_results$

$output_format_instructions$`,

  'competitive-landscape': (context: any) => `You are an expert clinical researcher analyzing medical treatments and competitive landscapes.

Study Context:
- Study Type: ${context.studyType || 'drug'}
- Condition: ${context.condition || 'not specified'}
- Phase: ${context.phase || 'not specified'}
- Drug Name: ${context.drugName || 'investigational product'}
- Drug Class: ${context.drugClass || 'not specified'}
- Route/Formulation: ${context.category || 'not specified'}
- Mechanism of Action: ${context.mechanism || 'not specified'}
- Novel Compound: ${context.isNewCompound ? 'Yes - First-in-class' : 'No - Existing/Repurposed'}

Based on the retrieved similar trials below, provide comprehensive medical problem and competitive landscape analysis in this EXACT JSON format:

$search_results$

## YOUR ANALYSIS
Provide your analysis in the following JSON format:

{
  "medicalProblem": {
    "description": "Clear description of the medical condition and patient impact",
    "epidemiology": "Prevalence data and disease burden (if available from retrieved documents)",
    "unmetNeed": "Current limitations and gaps in treatment"
  },
  "existingTreatments": [
    {
      "treatment": "Current therapy name",
      "mechanism": "How it works",
      "efficacy": "Effectiveness data",
      "limitations": "Safety issues, side effects, or patient burden",
      "marketPosition": "Standard of care, first-line, second-line, etc."
    }
  ],
  "competitiveAdvantages": {
    "primaryBenefit": "Main advantage over existing treatments",
    "safetyProfile": "Expected safety improvements",
    "efficacyExpectation": "Anticipated efficacy benefits",
    "patientBurden": "How this reduces patient burden (dosing, administration, etc.)",
    "differentiatingFactors": "Unique mechanisms or approaches"
  },
  "benchmarkStudies": [
    {
      "nctNumber": "Actual NCT identifier (format: NCTxxxxxxxx) - extract from retrieved documents",
      "condition": "Medical condition studied",
      "intervention": "Treatment investigated",
      "phase": "Study phase",
      "keyFindings": "Relevant efficacy or safety findings",
      "relevance": "Why this study is relevant for comparison"
    }
  ]
}

IMPORTANT:
1. Extract actual NCT numbers from retrieved documents
2. Be specific and quantitative when data is available
3. Base all recommendations on evidence from retrieved studies
4. Focus on patient impact and clinical meaningfulness
5. Do not include asterisks in your response

$output_format_instructions$`,

  'study-design': (context: any) => `You are a clinical trial design expert analyzing optimal study design fundamentals.

Study Context:
- Study Type: ${context.studyType || 'drug'}
- Condition: ${context.condition || 'not specified'}
- Phase: ${context.phase || 'not specified'}
- Drug/Device Class: ${context.drugClass || context.deviceClass || 'not specified'}
- Novel Compound: ${context.isNewCompound ? 'Yes - First-in-class' : 'No - Existing/Repurposed'}
- Mechanism of Action: ${context.mechanism || 'not specified'}
- Primary Endpoint: ${context.primaryEndpoint || 'To be determined'}

Current Design Selections:
- Design Type: ${context.designType || 'Not yet selected'}
- Blinding: ${context.blinding || 'Not yet selected'}
- Control Type: ${context.controlType || 'Not yet selected'}
- Randomization Ratio: ${context.randomizationRatio || 'Not yet selected'}

Based on the retrieved similar trials below, provide specific recommendations for the 4 core study design parameters in this EXACT JSON format:

$search_results$

## YOUR ANALYSIS
Provide your recommendations in the following JSON format:

{
  "designType": {
    "recommendation": "parallel",
    "rationale": "Evidence-based justification for this design type"
  },
  "blinding": {
    "recommendation": "double-blind",
    "rationale": "Evidence-based justification for this blinding level"
  },
  "controlType": {
    "recommendation": "placebo",
    "rationale": "Evidence-based justification for this control strategy"
  },
  "randomizationRatio": {
    "recommendation": "1:1",
    "rationale": "Evidence-based justification for this randomization ratio"
  },
  "benchmarkStudies": [
    {
      "nctNumber": "Actual NCT identifier (format: NCTxxxxxxxx) - extract from retrieved documents",
      "designType": "Design approach used",
      "blinding": "Blinding approach used",
      "controlType": "Control strategy used",
      "randomizationRatio": "Randomization ratio used",
      "relevance": "Why this study's design is relevant to your study"
    }
  ]
}

IMPORTANT:
1. Only recommend values that exactly match the UI form options
2. Design Type options: parallel, crossover, factorial, adaptive
3. Blinding options: open-label, single-blind, double-blind, triple-blind
4. Control Type options: placebo, active-comparator, standard-of-care, historical-control
5. Randomization Ratio: format as "1:1", "2:1", "3:1", etc.
6. Extract actual NCT numbers from retrieved documents
7. Base all recommendations on evidence from similar successful studies

$output_format_instructions$`,

  'design-descriptors': (context: any) => `You are a clinical trial design expert providing detailed study design descriptors based on the selected design parameters.

Study Context:
- Study Type: ${context.studyType || 'drug'}
- Condition: ${context.condition || 'not specified'}
- Phase: ${context.phase || 'not specified'}
- Drug/Device Class: ${context.drugClass || context.deviceClass || 'not specified'}
- Novel Compound: ${context.isNewCompound ? 'Yes - First-in-class' : 'No - Existing/Repurposed'}
- Mechanism of Action: ${context.mechanism || 'not specified'}
- Primary Endpoint: ${context.primaryEndpoint || 'To be determined'}

Selected Design Parameters:
- Design Type: ${context.designType || 'Not yet selected'}
- Blinding: ${context.blinding || 'Not yet selected'}
- Control Type: ${context.controlType || 'Not yet selected'}
- Randomization Ratio: ${context.randomizationRatio || 'Not yet selected'}

Based on the retrieved similar trials and the selected design parameters above, provide appropriate study design descriptors in this EXACT JSON format:

$search_results$

## YOUR ANALYSIS
Provide your recommendations in the following JSON format:

{
  "descriptors": [
    "Efficacy Study",
    "Parallel Assignment",
    "Randomized",
    "Treatment Study",
    "Phase II Clinical Trial",
    "Double-Blind Method",
    "Placebo-Controlled"
  ],
  "rationale": {
    "Efficacy Study": "Primary focus on measuring treatment effectiveness",
    "Parallel Assignment": "Based on selected parallel design type",
    "Randomized": "Randomization ratio indicates randomized allocation",
    "Treatment Study": "Therapeutic intervention study",
    "Phase II Clinical Trial": "Appropriate phase descriptor",
    "Double-Blind Method": "Based on selected blinding level",
    "Placebo-Controlled": "Based on selected control type"
  },
  "benchmarkStudies": [
    {
      "nctNumber": "Actual NCT identifier (format: NCTxxxxxxxx) - extract from retrieved documents",
      "descriptors": ["List", "of", "descriptors", "used", "in", "this", "study"],
      "relevance": "Why this study's descriptors are relevant"
    }
  ]
}

IMPORTANT:
1. Base descriptors on the SELECTED design parameters (designType, blinding, controlType, phase)
2. Common descriptor categories include:
   - Study Purpose: Efficacy Study, Safety Study, Pharmacokinetics Study, Bioavailability Study
   - Assignment: Parallel Assignment, Crossover Assignment, Single Group Assignment, Factorial Assignment
   - Allocation: Randomized, Non-Randomized
   - Intervention Model: Treatment, Prevention, Diagnostic, Supportive Care
   - Masking: Open Label, Single Blind, Double Blind, Triple Blind
   - Control: Placebo Controlled, Active Comparator, No Control
   - Phase: Phase I Clinical Trial, Phase II Clinical Trial, Phase III Clinical Trial, Phase IV Clinical Trial
3. Extract actual NCT numbers from retrieved documents
4. Ensure descriptors are consistent with the selected parameters
5. Provide clear rationale for each suggested descriptor

$output_format_instructions$`,

  'assignment-method': (context: any) => `You are a clinical trial randomization and assignment expert analyzing optimal assignment strategies for clinical studies.

Study Context:
- Study Type: ${context.studyType || 'drug'}
- Condition: ${context.condition || 'not specified'}
- Phase: ${context.phase || 'not specified'}
- Primary Endpoint: ${context.primaryEndpoint || 'not specified'}
- Design Type: ${context.designType || 'not specified'}
- Target Enrollment: ${context.targetEnrollment || 'not specified'}

Current Settings (to refine/validate):
- Current Assignment Method: ${context.currentAssignmentMethod || 'not set'}

Based on the retrieved similar trials, provide assignment method recommendations in this EXACT JSON format:

{
  "recommendedMethod": {
    "value": "randomization" | "stratification" | "randomization-stratification" | "no-assignment",
    "rationale": "Why this assignment method is optimal for this study type and phase",
    "typicalUse": "How often this method is used in similar studies"
  },
  "stratificationFactors": {
    "recommended": ["List of factors to stratify by if applicable, e.g., 'Age group', 'Disease severity', 'Site', 'Prior therapy'"],
    "rationale": "Why these stratification factors are important",
    "typical": "Most common stratification factors in similar studies"
  },
  "randomizationDetails": {
    "blockSize": "Recommended block size (e.g., '4', '6', 'variable')",
    "allocationRatio": "Recommended allocation ratio (e.g., '1:1', '2:1')",
    "method": "Specific randomization approach (e.g., 'Permuted block', 'Simple randomization', 'Adaptive randomization')",
    "rationale": "Technical justification for randomization approach"
  },
  "specialConsiderations": [
    "Important factors specific to this condition and phase",
    "Bias prevention strategies", 
    "Regulatory or safety considerations"
  ],
  "benchmarkStudies": [
    {
      "nctNumber": "Actual NCT identifier (format: NCTxxxxxxxx) - extract from retrieved documents",
      "assignmentMethod": "Assignment method used",
      "stratificationFactors": "Stratification factors if any",
      "enrolledParticipants": "Number enrolled"
    }
  ]
}

IMPORTANT:
1. Base recommendations on actual assignment strategies from retrieved similar studies
2. Consider study phase - Phase 1 often uses different methods than Phase 3
3. Extract real NCT numbers from retrieved documents for benchmarkStudies
4. Ensure recommended method value matches exactly one of: "randomization", "stratification", "randomization-stratification", "no-assignment"
5. Consider statistical power and bias prevention

$search_results$

$output_format_instructions$`,

  'site-strategy': (context: any) => `You are a clinical trial site selection and geographic distribution expert analyzing optimal site strategies for clinical studies.

Study Context:
- Study Type: ${context.studyType || 'drug'}
- Condition: ${context.condition || 'not specified'}
- Phase: ${context.phase || 'not specified'}
- Primary Endpoint: ${context.primaryEndpoint || 'not specified'}
- Target Enrollment: ${context.targetEnrollment || 'not specified'}
- Geographic Scope: ${context.geographicScope || 'not specified'}

Current Settings (to refine/validate):
- Current Number of Sites: ${context.numberOfSites || 'not set'}
- Current Countries: ${context.countriesEngaged && context.countriesEngaged.length > 0 ? context.countriesEngaged.slice(0, 3).join(', ') + (context.countriesEngaged.length > 3 ? '...' : '') : 'None set'}
- Current Site Distribution: ${context.siteDistribution || 'not set'}

Based on the retrieved similar trials, provide site strategy and geographic distribution recommendations in this EXACT JSON format:

{
  "recommendedStrategy": {
    "numberOfSites": "Optimal range (e.g., '15-20', '8-12')",
    "rationale": "Why this site count is optimal for this study type and enrollment target",
    "siteTypes": "Recommended mix (e.g., 'Mix of academic and community sites', 'Academic medical centers only')"
  },
  "geographicDistribution": {
    "primaryRegions": ["List of primary regions, e.g., 'North America', 'Europe', 'Asia-Pacific'"],
    "secondaryRegions": ["Additional regions if applicable"],
    "distributionRationale": "Why this geographic distribution is optimal",
    "urbanRuralMix": "Recommended urban vs rural distribution"
  },
  "countryRecommendations": [
    {
      "country": "Country name",
      "sites": "Number of sites for this country (integer)",
      "rationale": "Why this country and site count",
      "siteTypes": "Academic vs community recommendation for this country",
      "recruitmentAdvantages": "Key recruitment benefits in this country"
    }
  ],
  "siteSelectionCriteria": [
    "Key criterion for site selection #1",
    "Important site qualification requirement #2",
    "Critical operational consideration #3"
  ],
  "siteDistributionStrategy": "Comprehensive strategy description covering site types, geographic spread, and recruitment approach - this will be applied to the site distribution field",
  "prioritizedList": [
    "Most critical site strategy recommendation #1",
    "Key geographic consideration #2",
    "Important site selection criterion #3",
    "Additional strategic recommendations..."
  ],
  "operationalConsiderations": [
    "Site monitoring and oversight considerations",
    "Regulatory considerations by region",
    "Logistics and supply chain factors"
  ],
  "benchmarkStudies": [
    {
      "nctNumber": "Actual NCT identifier (format: NCTxxxxxxxx) - extract from retrieved documents",
      "numberOfSites": "Number of sites used",
      "countries": ["Countries involved"],
      "enrollmentPerSite": "Average enrollment per site",
      "siteStrategy": "Brief description of their site approach"
    }
  ]
}

IMPORTANT:
1. Base recommendations on actual site strategies from retrieved similar studies
2. Consider regulatory pathways - US/EU typically easier than emerging markets
3. Balance enrollment speed vs operational complexity
4. Extract real NCT numbers and site data from retrieved documents
5. Ensure numberOfSites is a realistic range based on target enrollment
6. Consider patient population density when recommending countries
7. Provide actionable siteDistributionStrategy that can be directly applied to form

$search_results$

$output_format_instructions$`,

  'ae-collection': (context: any) => `You are a clinical safety monitoring expert analyzing adverse event collection strategies.

Study Context:
- Study Type: ${context.studyType || 'drug'}
- Condition: ${context.condition || 'not specified'}
- Phase: ${context.phase || 'not specified'}
- Drug Class: ${context.drugClass || 'not specified'}
- Drug Name: ${context.drugName || 'not specified'}
- Treatment Duration: ${context.treatmentDuration || 'not specified'}

Based on the retrieved similar trials and regulatory guidelines, provide AE/SAE collection recommendations in this EXACT JSON format:

{
  "recommendation": {
    "collectAESAE": true,
    "rationale": "Detailed explanation of why AE/SAE collection is or is not necessary"
  },
  "monitoringStrategy": {
    "frequency": "How often AEs should be assessed (e.g., 'Weekly during treatment', 'At each visit')",
    "methods": ["Patient diary", "Direct questioning", "Laboratory monitoring", "ECG monitoring"],
    "grading": "CTCAE v5.0 or other grading system",
    "specialMonitoring": "Any special monitoring requirements for this drug class"
  },
  "regulatoryRequirements": {
    "fdaRequirements": "FDA-specific requirements for this phase/drug type",
    "expeditedReporting": "SAE reporting timelines (e.g., '24 hours for fatal/life-threatening')",
    "dsmb": "Whether DSMB is required/recommended"
  },
  "expectedCategories": {
    "systemOrganClass": ["List of expected SOCs based on drug class"],
    "commonTerms": ["Specific expected AE terms"],
    "specialInterest": ["AEs of special interest for this drug class"]
  },
  "benchmarkStudies": [
    {
      "nctNumber": "Actual NCT number from retrieved documents",
      "safetyApproach": "How they handled safety monitoring",
      "keyFindings": "Important safety outcomes"
    }
  ]
}

IMPORTANT:
1. Always recommend collecting AEs/SAEs for interventional drug trials
2. Base monitoring frequency on phase and drug novelty
3. Consider regulatory requirements for the specific phase
4. Extract real NCT numbers from retrieved documents

$search_results$

$output_format_instructions$`,

  'safety-profile': (context: any) => `You are a clinical pharmacology and safety expert analyzing expected adverse event profiles.

Study Context:
- Condition: ${context.condition || 'not specified'}
- Drug Class: ${context.drugClass || 'not specified'}
- Drug Name: ${context.drugName || 'not specified'}
- Mechanism of Action: ${context.mechanismOfAction || 'not specified'}
- Phase: ${context.phase || 'not specified'}
- Similar Compounds: ${context.similarCompounds || 'not specified'}

Based on the retrieved clinical trials and safety data, categorize expected adverse events by frequency in this EXACT JSON format:

{
  "likelySideEffects": {
    "effects": ["List of common AEs (≥10% incidence)"],
    "rationale": "Why these are likely based on drug class/mechanism",
    "managementStrategies": "How to monitor and manage these effects"
  },
  "lessLikelySideEffects": {
    "effects": ["List of uncommon AEs (1-10% incidence)"],
    "rationale": "Why these may occur less frequently",
    "monitoringRequirements": "Special monitoring if needed"
  },
  "rareButSeriousSideEffects": {
    "effects": ["List of rare but serious AEs (<1% but clinically significant)"],
    "rationale": "Why these are rare but important to monitor",
    "riskMitigation": "Specific risk mitigation strategies",
    "stoppingRules": "When to discontinue treatment"
  },
  "classEffects": {
    "knownClassEffects": ["AEs common to this drug class"],
    "mechanismRelated": ["AEs related to mechanism of action"],
    "offTargetEffects": ["Potential off-target effects"]
  },
  "demographicConsiderations": {
    "elderly": "Special considerations for elderly patients",
    "pediatric": "If applicable, pediatric considerations",
    "renalImpairment": "Considerations for renal impairment",
    "hepaticImpairment": "Considerations for hepatic impairment"
  },
  "benchmarkStudies": [
    {
      "nctNumber": "Actual NCT number from retrieved documents",
      "drugName": "Drug studied",
      "keyFindings": "Main safety findings",
      "discontinuationRate": "% discontinued due to AEs"
    }
  ]
}

IMPORTANT:
1. Base frequency categories on actual incidence data from similar trials
2. Use medical terminology for adverse events (e.g., 'nausea' not 'upset stomach')
3. Consider both class effects and compound-specific effects
4. Extract real safety data from retrieved trials
5. Include management strategies for common AEs

$search_results$

$output_format_instructions$`,

  'reproductive-risks': (context: any) => `You are a reproductive toxicology and clinical safety expert analyzing pregnancy and reproductive risks.

Study Context:
- Condition: ${context.condition || 'not specified'}
- Drug Class: ${context.drugClass || 'not specified'}
- Drug Name: ${context.drugName || 'not specified'}
- Mechanism of Action: ${context.mechanismOfAction || 'not specified'}
- Phase: ${context.phase || 'not specified'}
- Patient Population: ${context.patientPopulation || 'not specified'}

Based on the retrieved trials and reproductive safety data, provide reproductive risk assessment in this EXACT JSON format:

{
  "hasReproductiveRisks": {
    "assessment": true,
    "pregnancyCategory": "Former FDA category or current PLLR assessment",
    "rationale": "Scientific basis for reproductive risk determination"
  },
  "reproductiveRiskDetails": {
    "animalData": "Summary of reproductive toxicity studies in animals",
    "humanData": "Any available human pregnancy/fertility data",
    "mechanismConcerns": "Mechanism-based reproductive concerns",
    "teratogenicity": "Risk of birth defects",
    "fertilityEffects": "Effects on male/female fertility",
    "lactationRisk": "Risk during breastfeeding"
  },
  "riskMitigation": {
    "contraceptionRequirements": {
      "females": "Required contraception for females of childbearing potential",
      "males": "Required contraception for male participants",
      "duration": "How long after last dose"
    },
    "pregnancyTesting": {
      "frequency": "How often to test (e.g., 'Monthly', 'At screening and every visit')",
      "type": "Serum or urine beta-hCG",
      "timing": "Within X hours/days of first dose"
    },
    "counseling": "Required reproductive counseling points",
    "registry": "If pregnancy registry exists or is required"
  },
  "regulatoryGuidance": {
    "fdaRequirements": "FDA-specific reproductive safety requirements",
    "ichGuidelines": "ICH M3(R2) or other relevant guidelines",
    "rems": "If REMS with ETASU required for pregnancy prevention"
  },
  "comparatorData": [
    {
      "drugName": "Similar drug in class",
      "pregnancyCategory": "Its pregnancy category/risk",
      "keyFindings": "Reproductive safety findings"
    }
  ],
  "benchmarkStudies": [
    {
      "nctNumber": "Actual NCT number from retrieved documents",
      "reproductiveStrategy": "How they handled reproductive risks",
      "exclusions": "Reproductive-related exclusion criteria used"
    }
  ]
}

IMPORTANT:
1. Default to hasReproductiveRisks = true for novel compounds without data
2. Consider mechanism of action for reproductive risk assessment
3. Base on similar drugs in class when compound-specific data unavailable
4. Include specific contraception requirements and duration
5. Extract real reproductive safety strategies from retrieved trials

$search_results$

$output_format_instructions$`,

  default: (context: any) => `You are a clinical trial design expert.

Analyze the retrieved clinical trials and provide specific, actionable insights for the user's query.
Consider the study context and cite specific examples from successful trials.

Study Context:
${JSON.stringify(context, null, 2)}

$search_results$

$output_format_instructions$`,

  'study-timeline': (context: any) => `You are a clinical trial protocol design expert analyzing optimal study timelines and duration.

Study Context:
- Study Type: ${context.studyType || 'drug'}
- Condition: ${context.condition || 'not specified'}
- Phase: ${context.phase || 'not specified'}
- Drug Name: ${context.drugName || 'not specified'}
- Target Enrollment: ${context.targetEnrollment || 'not specified'}

Based on the retrieved similar trials, provide study timeline recommendations in this EXACT JSON format:

{
  "timeline": {
    "screeningPeriod": {
      "duration": "Recommended duration (e.g., '28 days', '4 weeks')",
      "rationale": "Why this screening period is optimal"
    },
    "baselinePeriod": {
      "duration": "Recommended baseline period (e.g., '7 days', 'Day -7 to Day 0')",
      "rationale": "Baseline assessment window justification"
    },
    "treatmentPeriod": {
      "duration": "Recommended treatment duration",
      "rationale": "Clinical justification for this duration"
    },
    "followUpPeriod": {
      "duration": "Recommended follow-up duration",
      "rationale": "Safety and efficacy monitoring rationale"
    },
    "totalDuration": "Total study duration from screening to last follow-up",
    "durationWithDates": "Expected timeline with dates (e.g., 'Q1 2024 - Q3 2024', 'Jan 2024 - Sep 2024')"
  },
  "benchmarkStudies": [
    {
      "nctNumber": "Actual NCT identifier from retrieved documents",
      "screeningDuration": "Their screening period",
      "treatmentDuration": "Their treatment duration",
      "followUpDuration": "Their follow-up period",
      "totalDuration": "Their total study duration"
    }
  ]
}

IMPORTANT:
1. Extract actual study durations from retrieved similar trials
2. Consider regulatory requirements for the phase
3. Include baseline period between screening and treatment
4. Provide realistic timeline with calendar dates
5. Balance scientific rigor with patient retention

$search_results$

$output_format_instructions$`,

  'visit-schedule': (context: any) => `You are a clinical trial visit schedule expert analyzing optimal visit frequencies and procedures.

Study Context:
- Study Type: ${context.studyType || 'drug'}
- Condition: ${context.condition || 'not specified'}
- Phase: ${context.phase || 'not specified'}
- Drug Name: ${context.drugName || 'not specified'}
- Treatment Duration: ${context.treatmentPeriod || 'not specified'}

Based on the retrieved similar trials, provide visit schedule recommendations in this EXACT JSON format:

{
  "visitSchedule": [
    {
      "visitName": "Visit name (e.g., 'Screening', 'Baseline/Day 0', 'Week 2', 'Week 4')",
      "timepoint": "Specific timepoint with window (e.g., 'Day -28 to -1', 'Day 0', 'Week 2 ± 3 days')",
      "procedures": ["Informed consent", "Medical history", "Vital signs", "Laboratory tests", "ECG", "Drug dispensing"],
      "critical": true,
      "rationale": "Why this visit is important (e.g., 'Primary endpoint assessment', 'Safety monitoring')"
    }
  ],
  "visitFrequency": {
    "duringTreatment": "Typical visit frequency during active treatment",
    "duringFollowUp": "Typical visit frequency during follow-up",
    "rationale": "Justification for visit frequency"
  },
  "benchmarkStudies": [
    {
      "nctNumber": "Actual NCT identifier from retrieved documents",
      "visitCount": "Total number of visits",
      "visitFrequency": "Description of their visit schedule",
      "keyProcedures": "Notable procedures or assessments"
    }
  ]
}

IMPORTANT:
1. Extract actual visit schedules from retrieved similar trials
2. Include all critical visits (screening, baseline, safety, efficacy endpoints)
3. Specify visit windows for flexibility (± days)
4. Mark primary/secondary endpoint visits as critical
5. Balance data collection needs with patient burden

$search_results$

$output_format_instructions$`,

  'intervention-details': (context: any) => `You are a clinical trial protocol expert specializing in intervention design and study event planning.

Study Context:
- Study Type: ${context.studyType || 'drug'}
- Condition: ${context.condition || 'not specified'}
- Phase: ${context.phase || 'not specified'}
- Drug Name: ${context.drugName || 'not specified'}
- Mechanism of Action: ${context.mechanismOfAction || 'not specified'}
- Treatment Duration: ${context.treatmentPeriod || 'not specified'}

Based on the retrieved similar trials, provide intervention and study event recommendations in this EXACT JSON format:

{
  "interventionDetails": {
    "dosing": {
      "recommendation": "Recommended dose and schedule (e.g., '10mg once daily')",
      "rationale": "Clinical justification for dosing",
      "alternatives": ["Alternative dosing option 1", "Alternative dosing option 2"]
    },
    "administration": {
      "route": "Route of administration (e.g., 'Oral tablet', 'IV infusion')",
      "frequency": "Dosing frequency and timing (e.g., 'Once daily with food')",
      "duration": "Treatment duration (e.g., '24 weeks continuous dosing')"
    },
    "packaging": "How drug will be packaged (e.g., 'Individual patient kits with 4-week supply')",
    "supplyChain": "Distribution method (e.g., 'Central pharmacy with direct-to-site shipment')",
    "storageRequirements": "Storage conditions (e.g., 'Room temperature 15-25°C', 'Refrigerated 2-8°C')"
  },
  "studyEvents": {
    "keyMilestones": [
      {
        "event": "Milestone name (e.g., 'First Patient First Visit')",
        "targetTiming": "When it should occur (e.g., 'Month 1')",
        "description": "Brief description of milestone"
      }
    ],
    "criticalAssessments": [
      "Baseline disease severity assessment",
      "Safety monitoring schedule",
      "Efficacy assessment timepoints",
      "Pharmacokinetic sampling"
    ],
    "operationalEvents": [
      "Site initiation visits",
      "Data Safety Monitoring Board reviews",
      "Interim analyses",
      "Database lock milestones"
    ]
  },
  "benchmarkStudies": [
    {
      "nctNumber": "Actual NCT identifier from retrieved documents",
      "interventionApproach": "How they administered the intervention",
      "keyEvents": "Major milestones or events they tracked"
    }
  ]
}

IMPORTANT:
1. Base dosing recommendations on similar trials in the same indication
2. Consider phase-appropriate dosing (dose-finding vs confirmatory)
3. Include practical operational details for implementation
4. Align milestones with typical industry timelines
5. Extract real intervention strategies from retrieved trials

$search_results$

$output_format_instructions$`,

  'laboratory-studies': (context: any) => `You are a clinical laboratory and biomarker strategy expert analyzing optimal biological sample collection and specialized testing.

Study Context:
- Study Type: ${context.studyType || 'drug'}
- Condition: ${context.condition || 'not specified'}
- Phase: ${context.phase || 'not specified'}
- Drug Name: ${context.drugName || 'not specified'}
- Mechanism of Action: ${context.mechanismOfAction || 'not specified'}

Based on the retrieved similar trials, provide laboratory and biomarker recommendations in this EXACT JSON format:

{
  "specimenCollection": {
    "recommendedSpecimens": [
      {
        "type": "Specimen type (e.g., 'Blood', 'Urine', 'Tissue biopsy')",
        "volume": "Collection volume/amount",
        "frequency": "Collection frequency",
        "rationale": "Why this specimen is needed"
      }
    ],
    "totalBloodVolume": "Total blood volume over study duration",
    "processingRequirements": "Key processing and storage requirements"
  },
  "specializedStudies": {
    "pharmacokinetics": {
      "recommended": true,
      "samplingSchedule": "PK sampling timepoints",
      "rationale": "Why PK is important for this drug"
    },
    "biomarkers": {
      "recommended": true,
      "types": ["Efficacy biomarkers", "Safety biomarkers", "Predictive biomarkers"],
      "rationale": "Clinical value of biomarker testing"
    },
    "immunogenicity": {
      "recommended": false,
      "frequency": "Testing schedule if applicable",
      "rationale": "Why or why not needed"
    },
    "geneticTesting": {
      "recommended": false,
      "purpose": "Pharmacogenomics, disease susceptibility, etc.",
      "rationale": "Clinical utility of genetic testing"
    }
  },
  "benchmarkStudies": [
    {
      "nctNumber": "Actual NCT identifier from retrieved documents",
      "specimensCollected": "Their specimen collection approach",
      "specializedTests": "What specialized tests they performed"
    }
  ]
}

IMPORTANT:
1. Base recommendations on actual laboratory practices from similar trials
2. Consider phase-appropriate testing (more extensive in early phases)
3. Balance scientific value with patient burden
4. Include regulatory requirements for the indication
5. Consider cost-effectiveness of specialized testing

$search_results$

$output_format_instructions$`,

  'operational-strategy': (context: any) => `You are a clinical trial operations and recruitment strategy expert analyzing optimal operational approaches.

Study Context:
- Study Type: ${context.studyType || 'drug'}
- Condition: ${context.condition || 'not specified'}
- Phase: ${context.phase || 'not specified'}
- Target Enrollment: ${context.targetEnrollment || 'not specified'}
- Number of Sites: ${context.numberOfSites || 'not specified'}

Based on the retrieved similar trials, provide operational strategy recommendations in this EXACT JSON format:

{
  "recruitmentStrategy": {
    "expectedRate": {
      "value": "Expected recruitment rate (e.g., '10 participants/month/site')",
      "range": { "min": "8", "max": "12" },
      "rationale": "Based on similar trials in this indication"
    },
    "screenFailureRate": {
      "value": "Expected screen failure rate (e.g., '30%')",
      "range": { "min": "25%", "max": "35%" },
      "rationale": "Common exclusion criteria impact"
    },
    "dropoutRate": {
      "value": "Expected dropout rate (e.g., '15%')",
      "range": { "min": "10%", "max": "20%" },
      "rationale": "Based on treatment duration and burden"
    },
    "overage": "Recommended enrollment overage percentage"
  },
  "dataManagement": {
    "recommendedSystem": "EDC",
    "specificPlatforms": ["Medidata Rave", "Veeva Vault CDMS", "REDCap"],
    "rationale": "Why these systems are optimal for this study"
  },
  "monitoringStrategy": {
    "approach": "risk-based",
    "onSiteFrequency": "Recommended on-site monitoring frequency",
    "remoteMonitoring": "Remote monitoring percentage",
    "rationale": "Why this monitoring approach is appropriate"
  },
  "benchmarkStudies": [
    {
      "nctNumber": "Actual NCT identifier from retrieved documents",
      "recruitmentRate": "Their actual recruitment performance",
      "operationalApproach": "Their operational strategy"
    }
  ]
}

IMPORTANT:
1. Extract actual recruitment metrics from similar completed trials
2. Consider indication-specific recruitment challenges
3. Account for competitive enrollment landscape
4. Include phase-appropriate monitoring intensity
5. Factor in geographic and site-type variations

$search_results$

$output_format_instructions$`,

  'financial-planning': (context: any) => `You are a clinical trial financial planning and patient compensation expert analyzing appropriate payment structures and reimbursement strategies.

Study Context:
- Study Type: ${context.studyType || 'drug'}  
- Condition: ${context.condition || 'not specified'}
- Phase: ${context.phase || 'not specified'}
- Study Duration: ${context.studyDuration || 'not specified'}
- Treatment Period: ${context.treatmentPeriod || 'not specified'}
- Follow-up Period: ${context.followupPeriod || 'not specified'}
- Number of Visits: ${context.numberOfVisits || 'not specified'}
- Target Enrollment: ${context.targetEnrollment || 'not specified'}
- Number of Sites: ${context.numberOfSites || 'not specified'}
- Healthy Volunteers: ${context.healthyVolunteers || 'no'}
- Procedure Burden: ${context.procedureBurden || 'standard'}

Based on the retrieved similar trials, provide financial planning recommendations in this EXACT JSON format:

{
  "participantCompensation": {
    "perVisitAmount": {
      "value": "Recommended per-visit payment (e.g., '$100-150')",
      "range": { "min": "$75", "max": "$200" },
      "rationale": "Based on visit duration and procedure burden"
    },
    "completionBonus": {
      "value": "Completion bonus amount (e.g., '$500')",
      "range": { "min": "$300", "max": "$800" },
      "rationale": "To incentivize study completion"
    },
    "totalCompensation": {
      "value": "Total expected compensation per participant",
      "breakdown": "How the total is calculated"
    },
    "paymentSchedule": "Recommended payment timing (e.g., 'After each visit' or 'Monthly')"
  },
  "travelReimbursement": {
    "mileageRate": "Recommended mileage reimbursement rate",
    "parkingCap": "Maximum parking reimbursement per visit",
    "publicTransport": "Public transportation reimbursement approach",
    "overnight": {
      "hotelCap": "Maximum hotel reimbursement if applicable",
      "mealAllowance": "Per diem for meals if overnight stays needed"
    }
  },
  "additionalSupport": {
    "childcare": "Childcare reimbursement recommendation",
    "lostWages": "Lost wages compensation if applicable",
    "specialAccommodations": "Special needs accommodations budget"
  },
  "billingScenarios": [
    {
      "scenario": "Standard of care",
      "billing": "How standard care procedures are billed",
      "coverage": "Insurance vs sponsor coverage"
    },
    {
      "scenario": "Research-only procedures",
      "billing": "Sponsor coverage requirements",
      "coverage": "Full sponsor coverage"
    },
    {
      "scenario": "Adverse events",
      "billing": "AE-related care billing approach",
      "coverage": "Sponsor vs insurance responsibilities"
    }
  ],
  "benchmarkStudies": [
    {
      "nctNumber": "Actual NCT identifier from retrieved documents",
      "compensationModel": "Their compensation structure",
      "totalCompensation": "Their total participant payment"
    }
  ]
}

IMPORTANT:
1. Consider fair market value guidelines for the geographic region
2. Account for socioeconomic factors of target population
3. Balance adequate compensation without undue inducement
4. Consider phase-specific compensation norms
5. Account for healthy volunteer vs patient population differences

$search_results$

$output_format_instructions$`,
};

export class BedrockKnowledgeBaseClient {
  private client: BedrockAgentRuntimeClient;
  private knowledgeBaseId: string;
  
  constructor() {
    this.client = new BedrockAgentRuntimeClient({
      region: process.env.AWS_REGION || 'us-west-2',
    });
    
    this.knowledgeBaseId = process.env.BEDROCK_KNOWLEDGE_BASE_ID || '';
    
    if (!this.knowledgeBaseId) {
      console.warn('BEDROCK_KNOWLEDGE_BASE_ID not configured');
    }
  }
  
  async query(request: BedrockQueryRequest & { field?: string }): Promise<BedrockQueryResponse> {
    const startTime = Date.now();
    
    try {
      // If retrieveOnly is true, just get documents without generation
      if (request.retrieveOnly) {
        return await this.retrieveDocuments(request, startTime);
      }
      
      // Otherwise, retrieve and generate insights
      return await this.retrieveAndGenerate(request, startTime);
    } catch (error) {
      console.error('Knowledge Base query error:', error);
      throw error;
    }
  }
  
  private async retrieveDocuments(
    request: BedrockQueryRequest,
    startTime: number
  ): Promise<BedrockQueryResponse> {
    const command = new RetrieveCommand({
      knowledgeBaseId: this.knowledgeBaseId,
      retrievalQuery: {
        text: request.query,
      },
      retrievalConfiguration: {
        vectorSearchConfiguration: {
          numberOfResults: request.maxResults || 10,
          overrideSearchType: "HYBRID",
        }
      }
    });
    
    const response = await this.client.send(command);
    
    return {
      results: response.retrievalResults?.map(result => ({
        content: result.content?.text || '',
        location: result.location,
        score: result.score || 0,
        metadata: result.metadata,
      })) || [],
      metadata: {
        queryTime: Date.now() - startTime,
        nextToken: response.nextToken,
      }
    };
  }
  
  private async retrieveAndGenerate(
    request: BedrockQueryRequest & { field?: string },
    startTime: number
  ): Promise<BedrockQueryResponse> {
    // Check if we're in local testing mode
    const isLocalTesting = process.env.BEDROCK_KNOWLEDGE_BASE_ID === 'SKIP_FOR_LOCAL_TESTING';
    
    if (isLocalTesting) {
      console.log('=== LOCAL TESTING MODE - Returning mock data ===');
      return this.getMockResponse(request, startTime);
    }
    
    // Get the appropriate prompt template
    const templateKey = request.field || 'default';
    console.log('=== TEMPLATE SELECTION DEBUG ===');
    console.log('Requested field:', request.field);
    console.log('Template key:', templateKey);
    console.log('Available template keys:', Object.keys(PROMPT_TEMPLATES));
    console.log('Template exists for key:', templateKey in PROMPT_TEMPLATES);
    
    const promptTemplate = PROMPT_TEMPLATES[templateKey as keyof typeof PROMPT_TEMPLATES] 
      || PROMPT_TEMPLATES.default;
    
    const isUsingDefault = promptTemplate === PROMPT_TEMPLATES.default;
    console.log('Using default template:', isUsingDefault);
    console.log('================================');
    
    const formattedPrompt = promptTemplate(request.context || {});
    
    // Log the full prompt for debugging
    console.log('\n=== BEDROCK KNOWLEDGE BASE PROMPT ===');
    console.log('Field:', request.field);
    console.log('Query:', request.query);
    console.log('Context:', JSON.stringify(request.context, null, 2));
    console.log('Full Prompt Template:');
    console.log('----------------------------------------');
    console.log(formattedPrompt);
    console.log('========================================\n');
    
    const command: RetrieveAndGenerateCommandInput = {
      input: {
        text: request.query,
      },
      retrieveAndGenerateConfiguration: {
        type: "KNOWLEDGE_BASE",
        knowledgeBaseConfiguration: {
          knowledgeBaseId: this.knowledgeBaseId,
          // Use the model ID directly - AWS accepts both full ARN and model ID
          modelArn: process.env.BEDROCK_MODEL_ID || 'anthropic.claude-3-5-haiku-20241022-v1:0',
          generationConfiguration: {
            promptTemplate: {
              textPromptTemplate: formattedPrompt,
            },
            inferenceConfig: {
              textInferenceConfig: {
                maxTokens: request.maxTokens || 2048,
                temperature: request.temperature || 0.3,
                topP: 0.9,
              }
            }
          },
          retrievalConfiguration: {
            vectorSearchConfiguration: {
              numberOfResults: 15,
              overrideSearchType: "HYBRID",
            }
          }
        }
      }
    };
    
    const response = await this.client.send(new RetrieveAndGenerateCommand(command));
    
    // Log the full raw response from Bedrock
    console.log('\n=== RAW BEDROCK RESPONSE ===');
    console.log('Full response keys:', Object.keys(response));
    console.log('Output text:', response.output?.text ? `${response.output.text.substring(0, 200)}...` : 'None');
    console.log('Session ID:', response.sessionId);
    console.log('Citations array length:', response.citations?.length || 0);
    
    // Log the entire response object as JSON for deep inspection
    console.log('\nFull response object (stringified):');
    console.log(JSON.stringify(response, null, 2).substring(0, 2000) + '...');
    
    if (response.citations && response.citations.length > 0) {
      console.log('Citations detail:');
      response.citations.forEach((citation, idx) => {
        console.log(`  Citation ${idx + 1}:`);
        console.log('    Text:', citation.generatedResponsePart?.textResponsePart?.text?.substring(0, 100));
        console.log('    References count:', citation.retrievedReferences?.length || 0);
        
        if (citation.retrievedReferences) {
          citation.retrievedReferences.forEach((ref, refIdx) => {
            console.log(`      Reference ${refIdx + 1}:`);
            console.log('        S3 URI:', ref.location?.s3Location?.uri);
            console.log('        Score:', ref.score);
            console.log('        Content preview:', ref.content?.text?.substring(0, 100));
          });
        }
      });
    } else {
      console.log('No citations in response');
    }
    
    console.log('========================================\n');
    
    // Filter out error citations
    const validCitations = response.citations?.filter(c => {
      const citationText = c.generatedResponsePart?.textResponsePart?.text || '';
      // Filter out error messages from Bedrock
      if (citationText.toLowerCase().includes('sorry') || 
          citationText.toLowerCase().includes('unable to assist') ||
          citationText.toLowerCase().includes('cannot help')) {
        console.log('Filtered out error citation:', citationText);
        return false;
      }
      return true;
    }) || [];
    
    // Format the response with field-specific parsing
    const sections = this.parseFieldSpecificResponse(
      response.output?.text || '',
      validCitations,
      request.field || ''
    );
    
    // Extract source documents from valid citations only
    const sources = this.extractSourceDocuments(validCitations);
    
    return {
      output: response.output?.text || '',
      citations: validCitations.map(c => ({
        text: c.generatedResponsePart?.textResponsePart?.text || '',
        references: c.retrievedReferences?.map(ref => ({
          content: ref.content?.text || '',
          location: ref.location,
          metadata: ref.metadata,
          score: ref.score || 0,
        })) || [],
      })),
      sections,
      sources, // Add the extracted sources
      sessionId: response.sessionId,
      metadata: {
        queryTime: Date.now() - startTime,
        modelUsed: process.env.BEDROCK_MODEL_ID,
      }
    };
  }
  
  private parseFieldSpecificResponse(text: string, citations: any[], field: string): any[] {
    // Use field-specific parsing for competitive landscape (JSON format)
    if (field === 'competitive-landscape') {
      return this.parseCompetitiveLandscapeResponse(text, citations);
    }
    
    // Use field-specific parsing for primary endpoint (JSON format)
    if (field === 'primary-endpoint') {
      return this.parsePrimaryEndpointResponse(text, citations);
    }
    
    // Use field-specific parsing for secondary endpoints
    if (field === 'secondary-endpoints') {
      return this.parseSecondaryEndpointsResponse(text, citations);
    }
    
    // Use field-specific parsing for study duration (JSON format)
    if (field === 'study-duration') {
      return this.parseStudyDurationResponse(text, citations);
    }
    
    // Use field-specific parsing for design parameters (JSON format)
    if (field === 'design-parameters') {
      return this.parseDesignParametersResponse(text, citations);
    }
    
    // Use field-specific parsing for study design (JSON format)
    if (field === 'study-design') {
      return this.parseStudyDesignResponse(text, citations);
    }
    
    // Use field-specific parsing for design descriptors (JSON format)
    if (field === 'design-descriptors') {
      return this.parseDesignDescriptorsResponse(text, citations);
    }
    
    // Use field-specific parsing for sample size (JSON format)
    if (field === 'sample-size') {
      return this.parseSampleSizeResponse(text, citations);
    }
    
    // Use field-specific parsing for demographics (JSON format)
    if (field === 'demographics') {
      return this.parseDemographicsResponse(text, citations);
    }
    
    // Use field-specific parsing for assignment method (JSON format)
    if (field === 'assignment-method') {
      return this.parseAssignmentMethodResponse(text, citations);
    }
    
    // Use field-specific parsing for inclusion criteria (JSON format)
    if (field === 'inclusion-criteria' || field === 'inclusion-exclusion') {
      return this.parseInclusionCriteriaResponse(text, citations);
    }
    
    // Use field-specific parsing for exclusion criteria (JSON format)
    if (field === 'exclusion-criteria') {
      return this.parseExclusionCriteriaResponse(text, citations);
    }
    
    // Use field-specific parsing for site strategy (JSON format)
    if (field === 'site-strategy') {
      return this.parseSiteStrategyResponse(text, citations);
    }
    
    // Use field-specific parsing for study periods (JSON format)
    if (field === 'study-periods') {
      return this.parseStudyPeriodsResponse(text, citations);
    }
    
    // Use field-specific parsing for visit schedule (JSON format)
    if (field === 'visit-schedule') {
      return this.parseVisitScheduleResponse(text, citations);
    }
    
    // Use field-specific parsing for operational fields (JSON format)
    if (field === 'site-planning') {
      return this.parseSitePlanningResponse(text, citations);
    }
    
    if (field === 'enrollment-projections') {
      return this.parseEnrollmentProjectionsResponse(text, citations);
    }
    
    if (field === 'data-monitoring') {
      return this.parseDataMonitoringResponse(text, citations);
    }
    
    // Safety Assessment specific parsers
    if (field === 'ae-collection') {
      return this.parseAECollectionResponse(text, citations);
    }
    
    if (field === 'safety-profile') {
      return this.parseSafetyProfileResponse(text, citations);
    }
    
    if (field === 'reproductive-risks') {
      return this.parseReproductiveRisksResponse(text, citations);
    }
    
    // Study Procedures & Operations specific parsers
    if (field === 'study-timeline') {
      return this.parseStudyTimelineResponse(text, citations);
    }
    
    if (field === 'visit-schedule') {
      return this.parseVisitScheduleResponse(text, citations);
    }
    
    if (field === 'intervention-details') {
      return this.parseInterventionDetailsResponse(text, citations);
    }
    
    if (field === 'laboratory-studies') {
      return this.parseLaboratoryStudiesResponse(text, citations);
    }
    
    if (field === 'operational-strategy') {
      return this.parseOperationalStrategyResponse(text, citations);
    }
    
    if (field === 'financial-planning') {
      return this.parseFinancialPlanningResponse(text, citations);
    }
    
    // Default to standard parsing for other fields
    return this.parseResponseIntoSections(text, citations, field);
  }
  
  private parseSecondaryEndpointsResponse(text: string, citations: any[]): any[] {
    const sections = [];
    
    console.log('\n=== PARSING SECONDARY ENDPOINTS RESPONSE ===');
    console.log('Text length:', text.length);
    
    // Split the text by ## headers to get all sections
    const parts = text.split(/(?=##\s)/);
    console.log('Split text into', parts.length, 'parts');
    
    // Debug: show what headers we found
    const headers = text.match(/##\s+[A-Z\s]+/g);
    console.log('Headers found in text:', headers ? headers.join(' | ') : 'none');
    
    for (const part of parts) {
      if (!part.trim()) continue;
      
      // Extract the header and content
      const headerMatch = part.match(/^##\s+(.+?)(?:\n|$)/);
      if (!headerMatch) continue;
      
      const rawTitle = headerMatch[1].trim();
      const title = rawTitle.replace(/\*+/g, '').trim();
      // Get content after the header
      const content = part.substring(headerMatch[0].length).trim();
      
      console.log(`Processing section: ${title}`);
      console.log(`  Content length: ${content.length}`);
      
      // Skip empty sections
      if (!content) {
        console.log('  Skipping empty section');
        continue;
      }
      
      // Handle the overview section specially
      if (title.includes('SECONDARY ENDPOINTS RECOMMENDATION')) {
        // Extract only the first paragraph as overview
        const paragraphs = content.split(/\n\n+/);
        const overviewContent = paragraphs[0]?.trim() || content.substring(0, 300).trim();
        
        console.log('  -> Found SECONDARY ENDPOINTS RECOMMENDATION overview');
        console.log('     Overview length:', overviewContent.length);
        
        sections.push({
          title: 'Overview',
          content: overviewContent,
          type: 'overview',
          actionable: false,
          citations: this.extractCitations(overviewContent, citations),
          confidence: 0.85,
        });
        continue;
      }
      
      // Only process endpoint sections
      const upperTitle = title.toUpperCase();
      if (upperTitle.includes('EFFICACY ENDPOINTS') || 
          upperTitle.includes('SAFETY ENDPOINTS') || 
          upperTitle.includes('QUALITY OF LIFE ENDPOINTS') || 
          upperTitle.includes('EXPLORATORY ENDPOINTS')) {
        
        // Determine section type
        let sectionType = 'endpoints';
        if (upperTitle.includes('EFFICACY')) sectionType = 'efficacy-endpoints';
        else if (upperTitle.includes('SAFETY')) sectionType = 'safety-endpoints';
        else if (upperTitle.includes('QUALITY OF LIFE')) sectionType = 'qol-endpoints';
        else if (upperTitle.includes('EXPLORATORY')) sectionType = 'exploratory-endpoints';
        
        // Parse numbered endpoints
        const endpoints = this.parseEndpointsList(content);
        
        sections.push({
          title: this.formatSectionTitle(title, sectionType),
          content: content,
          type: sectionType,
          actionable: true,
          endpoints: endpoints,
          citations: this.extractCitations(content, citations),
          confidence: 0.85,
        });
      }
    }
    
    console.log('Parsed sections count:', sections.length);
    sections.forEach((s, i) => {
      console.log(`  Section ${i + 1}: ${s.title} (type: ${s.type}, actionable: ${s.actionable})`);
      if (s.endpoints) {
        console.log(`    - Contains ${s.endpoints.length} endpoints`);
      }
    });
    console.log('===========================================\n');
    
    return sections;
  }
  
  private parseStudyDurationResponse(text: string, citations: any[]): any[] {
    const sections = [];
    
    console.log('\n=== PARSING STUDY DURATION RESPONSE ===');
    console.log('Text length:', text.length);
    
    try {
      // Try to extract JSON from the response
      // Look for JSON object in the text (it might be wrapped in other text)
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON object found in response');
      }
      
      const jsonData = JSON.parse(jsonMatch[0]);
      console.log('Successfully parsed JSON response');
      
      // Create main recommendation section with actionable items
      const recommendationContent = [];
      
      // Add treatment duration as an actionable item
      if (jsonData.treatmentDuration) {
        sections.push({
          title: 'Treatment Duration',
          content: `Recommended: ${jsonData.treatmentDuration.recommended}\nRange: ${jsonData.treatmentDuration.range}\n\n${jsonData.treatmentDuration.rationale || ''}`,
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'studyDuration',
            value: jsonData.treatmentDuration.recommended
          },
          citations: this.extractCitations(jsonData.treatmentDuration.rationale || '', citations),
          confidence: 0.85,
        });
      }
      
      // Add follow-up period as an actionable item
      if (jsonData.followUpPeriod) {
        sections.push({
          title: 'Follow-up Period',
          content: `Recommended: ${jsonData.followUpPeriod.recommended}\nRange: ${jsonData.followUpPeriod.range}\n\n${jsonData.followUpPeriod.rationale || ''}`,
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'followUpPeriod',
            value: jsonData.followUpPeriod.recommended
          },
          citations: this.extractCitations(jsonData.followUpPeriod.rationale || '', citations),
          confidence: 0.85,
        });
      }
      
      // Add key timepoints as information
      if (jsonData.keyTimepoints && jsonData.keyTimepoints.length > 0) {
        sections.push({
          title: 'Key Assessment Timepoints',
          content: jsonData.keyTimepoints.join('\n'),
          type: 'details',
          actionable: false,
          citations: [],
          confidence: 0.80,
        });
      }
      
      // Add total duration breakdown as information
      if (jsonData.totalDuration) {
        let totalContent = `Estimated Total Duration: ${jsonData.totalDuration.estimated}`;
        if (jsonData.totalDuration.breakdown) {
          totalContent += '\n\nBreakdown:';
          for (const [key, value] of Object.entries(jsonData.totalDuration.breakdown)) {
            const label = key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1');
            totalContent += `\n• ${label}: ${value}`;
          }
        }
        sections.push({
          title: 'Total Study Duration',
          content: totalContent,
          type: 'calculation',
          actionable: false,
          citations: [],
          confidence: 0.75,
        });
      }
      
      // Add citations if available
      if (jsonData.citations && jsonData.citations.length > 0) {
        // Create synthetic citations from the NCT numbers
        const citationList = jsonData.citations
          .filter((nct: string) => nct.match(/NCT\d{8}/))
          .map((nct: string) => ({
            id: nct,
            title: `Referenced study ${nct}`,
            url: `s3://trialynx-clinical-trials-gov/text-documents/${nct.substring(0, 6)}/${nct}.txt`,
            relevance: 0.80,
          }));
        
        // Add citations to the first section
        if (sections.length > 0 && citationList.length > 0) {
          sections[0].citations = citationList;
        }
      }
      
    } catch (error) {
      console.log('Failed to parse JSON, falling back to text parsing:', error);
      
      // Fallback to text parsing if JSON parsing fails
      return this.parseResponseIntoSections(text, citations, 'study-duration');
    }
    
    console.log('Parsed sections count:', sections.length);
    sections.forEach((s, i) => {
      console.log(`  Section ${i + 1}: ${s.title} (type: ${s.type}, actionable: ${s.actionable})`);
      if (s.actionableData) {
        console.log(`    - Actionable data: field=${s.actionableData.field}, value=${s.actionableData.value}`);
      }
    });
    console.log('===========================================\n');
    
    return sections;
  }
  
  private parsePrimaryEndpointResponse(text: string, citations: any[]): any[] {
    const sections = [];
    
    console.log('\n=== PARSING PRIMARY ENDPOINT RESPONSE ===');
    console.log('Text length:', text.length);
    
    try {
      // Try to extract JSON from the response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON object found in response');
      }
      
      const jsonData = JSON.parse(jsonMatch[0]);
      console.log('Successfully parsed JSON response');
      
      // 1. Main Primary Endpoint Recommendation
      if (jsonData.primaryEndpoint) {
        const pe = jsonData.primaryEndpoint;
        let content = `**Recommendation:** **${pe.recommendation}**\n\n${pe.description || ''}`;
        
        if (pe.measurementMethod) {
          content += `\n\n**Measurement Method:** ${pe.measurementMethod}`;
        }
        if (pe.timepoint) {
          content += `\n**Timepoint:** ${pe.timepoint}`;
        }
        if (pe.clinicalRelevance) {
          content += `\n\n**Clinical Relevance:** ${pe.clinicalRelevance}`;
        }
        
        sections.push({
          title: 'Primary Endpoint Recommendation',
          content: content,
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'primaryEndpoint',
            value: pe.recommendation,
            measurementMethod: pe.measurementMethod,
            timepoint: pe.timepoint
          },
          citations: [],
          confidence: 0.85,
        });
      }
      
      // 2. Measurement Specifications
      if (jsonData.measurementSpecifications) {
        const specs = jsonData.measurementSpecifications;
        const specDetails = [];
        
        if (specs.scale) specDetails.push(`Scale: ${specs.scale}`);
        if (specs.units) specDetails.push(`Units: ${specs.units}`);
        if (specs.minimumClinicallyImportantDifference) {
          specDetails.push(`MCID: ${specs.minimumClinicallyImportantDifference}`);
        }
        if (specs.analysisMethod) {
          specDetails.push(`Analysis Method: ${specs.analysisMethod}`);
        }
        
        if (specDetails.length > 0) {
          sections.push({
            title: 'Measurement Specifications',
            content: specDetails.join('\n'),
            type: 'details',
            actionable: false,
            citations: [],
            confidence: 0.80,
          });
        }
      }
      
      // 3. Alternative Endpoints
      if (jsonData.alternatives && jsonData.alternatives.length > 0) {
        const altContent = jsonData.alternatives.map((alt: any, idx: number) => {
          let altText = `${idx + 1}. **${alt.endpoint}**\n`;
          if (alt.rationale) altText += `   *Rationale:* ${alt.rationale}\n`;
          if (alt.advantages) altText += `   *Advantages:* ${alt.advantages}\n`;
          if (alt.disadvantages) altText += `   *Disadvantages:* ${alt.disadvantages}`;
          return altText;
        }).join('\n\n');
        
        sections.push({
          title: 'Alternative Endpoints',
          content: altContent,
          type: 'alternatives',
          actionable: true,
          alternatives: jsonData.alternatives.map((alt: any) => {
            return {
              endpoint: alt.endpoint,
              rationale: alt.rationale || '',
              measurementMethod: alt.measurementMethod || '',
              actionableData: {
                field: 'primaryEndpoint',
                value: alt.endpoint,  // Use the full endpoint text
                measurementMethod: alt.measurementMethod || ''
              }
            };
          }),
          citations: [],
          confidence: 0.75,
        });
      }
      
      // 4. Sample Size Calculation
      if (jsonData.sampleSize) {
        const ss = jsonData.sampleSize;
        let sampleSizeContent = `Estimated Total: ${ss.estimated}`;
        
        if (ss.perArm) {
          sampleSizeContent += `\nPer Treatment Arm: ${ss.perArm}`;
        }
        if (ss.assumptions) {
          sampleSizeContent += `\n\nAssumptions: ${ss.assumptions}`;
        }
        if (ss.adjustments) {
          sampleSizeContent += `\nAdjustments: ${ss.adjustments}`;
        }
        
        sections.push({
          title: 'Sample Size Calculation',
          content: sampleSizeContent,
          type: 'calculation',
          actionable: false,
          citations: [],
          confidence: 0.70,
        });
      }
      
      // 5. Design Considerations
      if (jsonData.designConsiderations) {
        const dc = jsonData.designConsiderations;
        const considerations = [];
        
        if (dc.blindingImpact) {
          considerations.push(`Blinding Impact: ${dc.blindingImpact}`);
        }
        if (dc.controlTypeAlignment) {
          considerations.push(`Control Type Alignment: ${dc.controlTypeAlignment}`);
        }
        if (dc.crossoverSuitability) {
          considerations.push(`Crossover Suitability: ${dc.crossoverSuitability}`);
        }
        
        if (considerations.length > 0) {
          sections.push({
            title: 'Design-Specific Considerations',
            content: considerations.join('\n\n'),
            type: 'considerations',
            actionable: false,
            citations: [],
            confidence: 0.80,
          });
        }
      }
      
      // Add citations if available
      if (jsonData.citations && jsonData.citations.length > 0) {
        const citationList = jsonData.citations.map((cit: any) => ({
          id: cit.nctNumber || cit,
          title: cit.title || `Referenced study ${cit.nctNumber || cit}`,
          url: `s3://trialynx-clinical-trials-gov/text-documents/${(cit.nctNumber || cit).substring(0, 6)}/${cit.nctNumber || cit}.txt`,
          relevance: 0.80,
          endpoint: cit.endpoint,
          outcome: cit.outcome,
        }));
        
        // Add citations to the first section
        if (sections.length > 0 && citationList.length > 0) {
          sections[0].citations = citationList;
        }
      }
      
    } catch (error) {
      console.log('Failed to parse JSON, falling back to text parsing:', error);
      
      // Fallback to text parsing if JSON parsing fails
      return this.parseResponseIntoSections(text, citations, 'primary-endpoint');
    }
    
    console.log('Parsed sections count:', sections.length);
    sections.forEach((s, i) => {
      console.log(`  Section ${i + 1}: ${s.title} (type: ${s.type}, actionable: ${s.actionable})`);
      if (s.actionableData) {
        console.log(`    - Actionable data: field=${s.actionableData.field}, value=${s.actionableData.value}`);
      }
    });
    console.log('===========================================\n');
    
    return sections;
  }
  
  private parseCompetitiveLandscapeResponse(text: string, citations: any[]): any[] {
    const sections = [];
    
    console.log('\n=== PARSING COMPETITIVE LANDSCAPE RESPONSE ===');
    console.log('Text length:', text.length);
    
    // Extract NCT numbers from citations for validation
    const citationNCTNumbers: string[] = [];
    citations.forEach(citation => {
      if (citation.references) {
        citation.references.forEach((ref: any) => {
          if (ref.location?.s3Location?.uri) {
            const nctMatch = ref.location.s3Location.uri.match(/NCT\d+/i);
            if (nctMatch) {
              citationNCTNumbers.push(nctMatch[0].toUpperCase());
            }
          }
        });
      }
    });
    console.log('NCT numbers found in citations:', citationNCTNumbers);
    
    try {
      // Extract JSON from response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }
      
      const jsonData = JSON.parse(jsonMatch[0]);
      console.log('Successfully parsed JSON for competitive landscape');
      
      // 1. Medical Problem Description
      if (jsonData.medicalProblem) {
        const mp = jsonData.medicalProblem;
        let content = `**Medical Condition:** ${mp.description}`;
        
        if (mp.epidemiology) {
          content += `\n\n**Disease Burden:** ${mp.epidemiology}`;
        }
        
        if (mp.unmetNeed) {
          content += `\n\n**Unmet Medical Need:** ${mp.unmetNeed}`;
        }
        
        sections.push({
          title: 'Medical Problem Addressed',
          content,
          type: 'information',
          actionable: true,
          actionableData: {
            field: 'medicalProblem',
            value: mp.description,
            epidemiology: mp.epidemiology,
            unmetNeed: mp.unmetNeed
          },
          citations: [],
          confidence: 0.85,
        });
      }
      
      // 2. Existing Treatments
      if (jsonData.existingTreatments && jsonData.existingTreatments.length > 0) {
        const treatments = jsonData.existingTreatments.map((treatment: any) => 
          `**${treatment.treatment}**\n` +
          `- **Mechanism:** ${treatment.mechanism}\n` +
          `- **Efficacy:** ${treatment.efficacy}\n` +
          `- **Limitations:** ${treatment.limitations}\n` +
          `- **Market Position:** ${treatment.marketPosition}`
        ).join('\n\n');
        
        sections.push({
          title: 'Current Treatment Landscape',
          content: `**Existing Therapies:**\n\n${treatments}`,
          type: 'information',
          actionable: false,
          citations: [],
          confidence: 0.80,
        });
      }
      
      // 3. Competitive Advantages
      if (jsonData.competitiveAdvantages) {
        const ca = jsonData.competitiveAdvantages;
        let content = `**Primary Advantage:** **${ca.primaryBenefit}**`;
        
        if (ca.safetyProfile) {
          content += `\n\n**Safety Improvements:** ${ca.safetyProfile}`;
        }
        
        if (ca.efficacyExpectation) {
          content += `\n\n**Efficacy Benefits:** ${ca.efficacyExpectation}`;
        }
        
        if (ca.patientBurden) {
          content += `\n\n**Patient Burden Reduction:** ${ca.patientBurden}`;
        }
        
        if (ca.differentiatingFactors) {
          content += `\n\n**Key Differentiators:** ${ca.differentiatingFactors}`;
        }
        
        sections.push({
          title: 'Comparison to Existing Treatments',
          content,
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'comparisonToExistingTreatments',
            value: `${ca.primaryBenefit}. ${ca.safetyProfile ? ca.safetyProfile + ' ' : ''}${ca.efficacyExpectation ? ca.efficacyExpectation + ' ' : ''}${ca.patientBurden ? ca.patientBurden : ''}`.trim(),
            primaryBenefit: ca.primaryBenefit
          },
          citations: [],
          confidence: 0.85,
        });
      }
      
      // 4. Benchmark Studies
      if (jsonData.benchmarkStudies && jsonData.benchmarkStudies.length > 0) {
        const studiesList: any[] = [];
        const validCitations: any[] = [];
        
        jsonData.benchmarkStudies.forEach((study: any, index: number) => {
          let nctNumber = study.nctNumber;
          
          // Validate NCT format
          const isValidNCT = /^NCT\d+$/i.test(nctNumber);
          if (!isValidNCT && citationNCTNumbers[index]) {
            // Use citation NCT as fallback
            nctNumber = citationNCTNumbers[index];
          }
          
          const studyInfo = `**${nctNumber}**\n` +
            `- **Condition:** ${study.condition}\n` +
            `- **Intervention:** ${study.intervention}\n` +
            `- **Phase:** ${study.phase}\n` +
            `- **Key Findings:** ${study.keyFindings}\n` +
            `- **Relevance:** ${study.relevance}`;
          
          studiesList.push(studyInfo);
          
          // Create citation if NCT is valid
          if (/^NCT\d+$/i.test(nctNumber)) {
            const nctUpper = nctNumber.toUpperCase();
            validCitations.push({
              id: nctUpper,
              title: `${nctUpper} - ${study.condition}`,
              url: `s3://trialynx-clinical-trials-gov/text-documents/${nctUpper.substring(0, 6)}/${nctUpper}.txt`,
              relevance: 0.85,
            });
          }
        });
        
        sections.push({
          title: 'Benchmark Studies',
          content: `**Relevant Clinical Trials:**\n\n${studiesList.join('\n\n')}`,
          type: 'references',
          actionable: false,
          citations: validCitations,
          confidence: 0.80,
        });
      }
      
    } catch (error) {
      console.log('Failed to parse JSON, falling back to text parsing:', error);
      return this.parseResponseIntoSections(text, citations, 'competitive-landscape');
    }
    
    console.log('Competitive landscape sections created:', sections.length);
    sections.forEach((section, idx) => {
      console.log(`Section ${idx + 1}:`, {
        title: section.title,
        type: section.type,
        actionable: section.actionable,
        hasActionableData: !!section.actionableData,
        citationsCount: section.citations?.length || 0
      });
    });
    console.log('===============================================\n');
    
    return sections;
  }
  
  private parseSampleSizeResponse(text: string, citations: any[]): any[] {
    const sections = [];
    
    console.log('\n=== PARSING SAMPLE SIZE RESPONSE ===');
    console.log('Text length:', text.length);
    
    // Extract NCT numbers from citations for fallback
    const citationNCTNumbers: string[] = [];
    citations.forEach(citation => {
      if (citation.references) {
        citation.references.forEach((ref: any) => {
          if (ref.location?.s3Location?.uri) {
            const nctMatch = ref.location.s3Location.uri.match(/NCT\d+/i);
            if (nctMatch) {
              citationNCTNumbers.push(nctMatch[0].toUpperCase());
            }
          }
        });
      }
    });
    console.log('NCT numbers found in citations:', citationNCTNumbers);
    
    try {
      // Try to parse as JSON
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }
      
      const jsonData = JSON.parse(jsonMatch[0]);
      console.log('Successfully parsed JSON for sample size');
      
      // 1. Primary Recommendation
      if (jsonData.targetEnrollment) {
        const te = jsonData.targetEnrollment;
        let content = `**Recommended Sample Size:** **${te.recommended}**`;
        
        if (te.range) {
          content += `\n\n**Range:** ${te.range.min} - ${te.range.max} patients`;
        }
        
        if (te.rationale) {
          content += `\n\n**Rationale:** ${te.rationale}`;
        }
        
        sections.push({
          title: 'Target Enrollment',
          content,
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'targetEnrollment',
            value: te.recommended,
            range: te.range
          },
          citations: [],
          confidence: 0.85,
        });
      }
      
      // 2. Per-Arm Breakdown
      if (jsonData.perArm) {
        const pa = jsonData.perArm;
        const armDetails = [];
        
        if (pa.treatmentArm) armDetails.push(`Treatment Arm: ${pa.treatmentArm} patients`);
        if (pa.controlArm) armDetails.push(`Control Arm: ${pa.controlArm} patients`);
        if (pa.otherArms) armDetails.push(`Other Arms: ${pa.otherArms}`);
        
        if (armDetails.length > 0) {
          sections.push({
            title: 'Allocation by Treatment Arm',
            content: armDetails.join('\n'),
            type: 'details',
            actionable: false,
            citations: [],
            confidence: 0.80,
          });
        }
      }
      
      // 3. Statistical Assumptions
      if (jsonData.statisticalAssumptions) {
        const sa = jsonData.statisticalAssumptions;
        const assumptions = [];
        
        if (sa.power) assumptions.push(`Statistical Power: ${sa.power}`);
        if (sa.alpha) assumptions.push(`Significance Level: ${sa.alpha}`);
        if (sa.effectSize) assumptions.push(`Effect Size: ${sa.effectSize}`);
        if (sa.dropoutRate) assumptions.push(`Expected Dropout Rate: ${sa.dropoutRate}`);
        
        if (assumptions.length > 0) {
          sections.push({
            title: 'Statistical Assumptions',
            content: assumptions.join('\n'),
            type: 'calculation',
            actionable: false,
            citations: [],
            confidence: 0.75,
          });
        }
      }
      
      // 4. Enrollment Adjustments
      if (jsonData.adjustments) {
        const adj = jsonData.adjustments;
        const adjustmentDetails = [];
        
        if (adj.screenFailRate) adjustmentDetails.push(`Screen Failure Rate: ${adj.screenFailRate}`);
        if (adj.totalToScreen) adjustmentDetails.push(`Total to Screen: ${adj.totalToScreen} patients`);
        if (adj.enrollmentBuffer) adjustmentDetails.push(`Enrollment Buffer: ${adj.enrollmentBuffer}`);
        
        if (adjustmentDetails.length > 0) {
          sections.push({
            title: 'Screening & Enrollment Adjustments',
            content: adjustmentDetails.join('\n'),
            type: 'information',
            actionable: false,
            citations: [],
            confidence: 0.70,
          });
        }
      }
      
      // 5. Enrollment Projections
      if (jsonData.enrollmentProjections) {
        const ep = jsonData.enrollmentProjections;
        const projections = [];
        
        if (ep.monthlyRate) projections.push(`Monthly Enrollment Rate: ${ep.monthlyRate}`);
        if (ep.timeToFullEnrollment) projections.push(`Time to Full Enrollment: ${ep.timeToFullEnrollment}`);
        if (ep.sitesNeeded) projections.push(`Sites Needed: ${ep.sitesNeeded}`);
        
        if (projections.length > 0) {
          sections.push({
            title: 'Enrollment Projections',
            content: projections.join('\n'),
            type: 'information',
            actionable: false,
            citations: [],
            confidence: 0.70,
          });
        }
      }
      
      // 6. Benchmark Studies
      if (jsonData.benchmarkStudies && jsonData.benchmarkStudies.length > 0) {
        // Process benchmark studies and fix NCT numbers
        const processedStudies = jsonData.benchmarkStudies.map((study: any, index: number) => {
          let nctNumber = study.nctNumber;
          
          // Check if the provided NCT number is valid
          const isValidNCT = nctNumber && /^NCT\d+$/i.test(nctNumber);
          
          if (!isValidNCT) {
            // Try to use a citation NCT number as fallback
            if (citationNCTNumbers[index]) {
              console.log(`Replacing invalid NCT "${nctNumber}" with citation NCT: ${citationNCTNumbers[index]}`);
              nctNumber = citationNCTNumbers[index];
            } else {
              console.log(`No valid NCT number for benchmark study: ${nctNumber}`);
              nctNumber = null;
            }
          }
          
          return {
            ...study,
            nctNumber: nctNumber ? nctNumber.toUpperCase() : study.nctNumber,
            hasValidNCT: !!nctNumber && /^NCT\d+$/i.test(nctNumber)
          };
        });
        
        // Create display text including all studies
        const benchmarks = processedStudies.map((study: any) => {
          let studyText = study.hasValidNCT 
            ? `${study.nctNumber}: ${study.enrolledPatients} patients`
            : `${study.nctNumber || 'Study'}: ${study.enrolledPatients} patients`;
          if (study.completionRate) {
            studyText += ` (${study.completionRate} completion)`;
          }
          return studyText;
        }).join('\n');
        
        // Only create citations for studies with valid NCT numbers
        const validCitations = processedStudies
          .filter((study: any) => study.hasValidNCT)
          .map((study: any) => ({
            id: study.nctNumber,
            title: `${study.nctNumber} - ${study.enrolledPatients} patients enrolled`,
            url: `s3://trialynx-clinical-trials-gov/text-documents/${study.nctNumber.substring(0, 6)}/${study.nctNumber}.txt`,
            relevance: 0.80,
          }));
        
        sections.push({
          title: 'Benchmark Studies',
          content: benchmarks,
          type: 'references',
          actionable: false,
          citations: validCitations,
          confidence: 0.80,
        });
      }
      
      // 7. Special Considerations
      if (jsonData.considerations && jsonData.considerations.length > 0) {
        sections.push({
          title: 'Special Considerations',
          content: jsonData.considerations.map((c: string, i: number) => `${i + 1}. ${c}`).join('\n'),
          type: 'considerations',
          actionable: false,
          citations: [],
          confidence: 0.75,
        });
      }
      
    } catch (error) {
      console.log('Failed to parse JSON, falling back to text parsing:', error);
      
      // Fallback to text parsing if JSON parsing fails
      return this.parseResponseIntoSections(text, citations, 'sample-size');
    }
    
    console.log('Parsed sections count:', sections.length);
    sections.forEach((s, i) => {
      console.log(`  Section ${i + 1}: ${s.title} (type: ${s.type}, actionable: ${s.actionable})`);
    });
    console.log('===========================================\n');
    
    return sections;
  }
  
  private parseDemographicsResponse(text: string, citations: any[]): any[] {
    const sections = [];
    
    console.log('\n=== PARSING DEMOGRAPHICS RESPONSE ===');
    console.log('Text length:', text.length);
    
    try {
      // Try to parse as JSON
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }
      
      const jsonData = JSON.parse(jsonMatch[0]);
      console.log('Successfully parsed JSON for demographics');
      
      // 1. Age Range Recommendation
      if (jsonData.ageRange) {
        const ar = jsonData.ageRange;
        let content = `**Recommended Age Range:** **${ar.recommendedMin} - ${ar.recommendedMax} years**`;
        
        if (ar.typicalRange) {
          content += `\n\n**Typical Range in Similar Studies:** ${ar.typicalRange.min} - ${ar.typicalRange.max} years`;
        }
        
        if (ar.rationale) {
          content += `\n\n**Rationale:** ${ar.rationale}`;
        }
        
        sections.push({
          title: 'Age Range',
          content,
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'ageRange',
            min: ar.recommendedMin,
            max: ar.recommendedMax
          },
          citations: [],
          confidence: 0.85,
        });
      }
      
      // 2. Gender Recommendation
      if (jsonData.gender) {
        const g = jsonData.gender;
        let content = `**Recommendation:** **${g.recommendation === 'all' ? 'All genders' : g.recommendation === 'male' ? 'Male only' : 'Female only'}**`;
        
        if (g.distribution) {
          content += `\n\n**Typical Distribution:** ${g.distribution}`;
        }
        
        if (g.rationale) {
          content += `\n\n**Rationale:** ${g.rationale}`;
        }
        
        sections.push({
          title: 'Gender Requirements',
          content,
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'gender',
            value: g.recommendation
          },
          citations: [],
          confidence: 0.80,
        });
      }
      
      // 3. Specific Populations
      if (jsonData.specificPopulations) {
        const sp = jsonData.specificPopulations;
        let content = '';
        
        if (sp.recommended && sp.recommended.length > 0) {
          content += `**Recommended Populations:**\n${sp.recommended.map((p: string) => `• ${p}`).join('\n')}`;
        }
        
        if (sp.avoid && sp.avoid.length > 0) {
          if (content) content += '\n\n';
          content += `**Populations to Exclude:**\n${sp.avoid.map((p: string) => `• ${p}`).join('\n')}`;
        }
        
        if (sp.rationale) {
          if (content) content += '\n\n';
          content += `**Rationale:** ${sp.rationale}`;
        }
        
        // Extract the first recommended population as actionable
        const specificPop = sp.recommended && sp.recommended.length > 0 
          ? sp.recommended[0].replace(/[()]/g, '').trim()
          : '';
        
        sections.push({
          title: 'Specific Populations',
          content,
          type: 'information',
          actionable: specificPop !== '',
          actionableData: specificPop ? {
            field: 'specificPopulation',
            value: specificPop
          } : undefined,
          citations: [],
          confidence: 0.75,
        });
      }
      
      // 4. Healthy Volunteers
      if (jsonData.healthyVolunteers) {
        const hv = jsonData.healthyVolunteers;
        let content = `**Recommendation:** **${hv.recommendation ? 'Accept healthy volunteers' : 'Patients only (no healthy volunteers)'}**`;
        
        if (hv.typicalApproach) {
          content += `\n\n**Typical Approach:** ${hv.typicalApproach}`;
        }
        
        if (hv.rationale) {
          content += `\n\n**Rationale:** ${hv.rationale}`;
        }
        
        sections.push({
          title: 'Healthy Volunteers',
          content,
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'healthyVolunteers',
            value: hv.recommendation
          },
          citations: [],
          confidence: 0.80,
        });
      }
      
      // 5. Demographic Considerations
      if (jsonData.demographicConsiderations && jsonData.demographicConsiderations.length > 0) {
        sections.push({
          title: 'Key Demographic Considerations',
          content: jsonData.demographicConsiderations.map((c: string, i: number) => `${i + 1}. ${c}`).join('\n'),
          type: 'considerations',
          actionable: false,
          citations: [],
          confidence: 0.75,
        });
      }
      
      // 6. Benchmark Studies
      if (jsonData.benchmarkStudies && jsonData.benchmarkStudies.length > 0) {
        const benchmarks = jsonData.benchmarkStudies.map((study: any) => {
          let studyText = `${study.nctNumber}: ${study.ageRange}, ${study.gender === 'all' ? 'all genders' : study.gender}`;
          if (study.enrolledPopulation) {
            studyText += ` - ${study.enrolledPopulation}`;
          }
          return studyText;
        }).join('\n');
        
        // Only create citations for valid NCT numbers
        const validCitations = jsonData.benchmarkStudies
          .filter((study: any) => study.nctNumber && /^NCT\d+$/i.test(study.nctNumber))
          .map((study: any) => {
            const nctNumber = study.nctNumber.toUpperCase();
            return {
              id: nctNumber,
              title: `${nctNumber} - ${study.ageRange}, ${study.gender}`,
              url: `s3://trialynx-clinical-trials-gov/text-documents/${nctNumber.substring(0, 6)}/${nctNumber}.txt`,
              relevance: 0.80,
            };
          });
        
        sections.push({
          title: 'Similar Study Demographics',
          content: benchmarks,
          type: 'references',
          actionable: false,
          citations: validCitations,
          confidence: 0.80,
        });
      }
      
    } catch (error) {
      console.log('Failed to parse JSON, falling back to text parsing:', error);
      
      // Fallback to text parsing if JSON parsing fails
      return this.parseResponseIntoSections(text, citations, 'demographics');
    }
    
    console.log('Parsed sections count:', sections.length);
    sections.forEach((s, i) => {
      console.log(`  Section ${i + 1}: ${s.title} (type: ${s.type}, actionable: ${s.actionable})`);
    });
    console.log('===========================================\n');
    
    return sections;
  }
  
  private parseAssignmentMethodResponse(text: string, citations: any[]): any[] {
    const sections = [];
    
    console.log('\n=== PARSING ASSIGNMENT METHOD RESPONSE ===');
    console.log('Text length:', text.length);
    
    try {
      // Try to parse as JSON
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }
      
      const jsonData = JSON.parse(jsonMatch[0]);
      console.log('Successfully parsed JSON for assignment method');
      
      // 1. Recommended Assignment Method
      if (jsonData.recommendedMethod) {
        const rm = jsonData.recommendedMethod;
        let content = `**Recommended Method:** **${this.formatAssignmentMethod(rm.value)}**`;
        
        if (rm.typicalUse) {
          content += `\n\n**Typical Usage:** ${rm.typicalUse}`;
        }
        
        if (rm.rationale) {
          content += `\n\n**Rationale:** ${rm.rationale}`;
        }
        
        sections.push({
          title: 'Assignment Method',
          content,
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'trialAssignmentMethod',
            value: rm.value
          },
          citations: [],
          confidence: 0.85,
        });
      }
      
      // 2. Stratification Factors (informational)
      if (jsonData.stratificationFactors) {
        const sf = jsonData.stratificationFactors;
        let content = '';
        
        if (sf.recommended && sf.recommended.length > 0) {
          content += `**Recommended Factors:**\n${sf.recommended.map((f: string) => `• ${f}`).join('\n')}`;
        }
        
        if (sf.typical) {
          if (content) content += '\n\n';
          content += `**Typical Factors:** ${sf.typical}`;
        }
        
        if (sf.rationale) {
          if (content) content += '\n\n';
          content += `**Rationale:** ${sf.rationale}`;
        }
        
        sections.push({
          title: 'Stratification Factors',
          content,
          type: 'information',
          actionable: false,
          citations: [],
          confidence: 0.80,
        });
      }
      
      // 3. Randomization Details (informational)
      if (jsonData.randomizationDetails) {
        const rd = jsonData.randomizationDetails;
        let content = '';
        
        if (rd.method) {
          content += `**Method:** ${rd.method}`;
        }
        
        if (rd.blockSize) {
          if (content) content += '\n';
          content += `**Block Size:** ${rd.blockSize}`;
        }
        
        if (rd.allocationRatio) {
          if (content) content += '\n';
          content += `**Allocation Ratio:** ${rd.allocationRatio}`;
        }
        
        if (rd.rationale) {
          if (content) content += '\n\n';
          content += `**Technical Rationale:** ${rd.rationale}`;
        }
        
        sections.push({
          title: 'Randomization Technical Details',
          content,
          type: 'details',
          actionable: false,
          citations: [],
          confidence: 0.75,
        });
      }
      
      // 4. Special Considerations
      if (jsonData.specialConsiderations && jsonData.specialConsiderations.length > 0) {
        sections.push({
          title: 'Special Considerations',
          content: jsonData.specialConsiderations.map((c: string, i: number) => `${i + 1}. ${c}`).join('\n'),
          type: 'considerations',
          actionable: false,
          citations: [],
          confidence: 0.75,
        });
      }
      
      // 5. Benchmark Studies
      if (jsonData.benchmarkStudies && jsonData.benchmarkStudies.length > 0) {
        // Extract NCT numbers from citations for validation
        const citationNCTNumbers = citations.flatMap(c => 
          c.references?.map(r => r.location?.s3Location?.uri?.match(/NCT\d+/i)?.[0])
        ).filter(Boolean);
        
        const studyEntries = jsonData.benchmarkStudies.map((study: any, index: number) => {
          let nctNumber = study.nctNumber;
          let url = '';
          
          // Validate NCT number format
          const validNCT = /^NCT\d+$/i.test(nctNumber);
          if (!validNCT && citationNCTNumbers[index]) {
            nctNumber = citationNCTNumbers[index];
          }
          
          // Create S3 URL only for valid NCT numbers
          if (/^NCT\d+$/i.test(nctNumber)) {
            url = `s3://trialynx-clinical-trials-gov/text-documents/${nctNumber.substring(0,6)}/${nctNumber}.txt`;
          }
          
          return `**${nctNumber}**\n• Assignment: ${study.assignmentMethod || 'Not specified'}\n• Stratification: ${study.stratificationFactors || 'None'}\n• Enrolled: ${study.enrolledParticipants || 'Not specified'}`;
        }).join('\n\n');
        
        sections.push({
          title: 'Benchmark Studies',
          content: studyEntries,
          type: 'references',
          actionable: false,
          citations: [],
          confidence: 0.70,
        });
      }
      
    } catch (error) {
      console.log('Failed to parse JSON, falling back to text parsing:', error);
      return this.parseResponseIntoSections(text, citations, field);
    }
    
    console.log('Parsed sections count:', sections.length);
    sections.forEach((s, i) => {
      console.log(`  Section ${i + 1}: ${s.title} (type: ${s.type}, actionable: ${s.actionable})`);
    });
    console.log('===========================================\n');
    
    return sections;
  }
  
  private formatAssignmentMethod(value: string): string {
    switch (value) {
      case 'randomization': return 'Randomization';
      case 'stratification': return 'Stratification';
      case 'randomization-stratification': return 'Randomization with Stratification';
      case 'no-assignment': return 'No Assignment (Single Arm)';
      default: return value;
    }
  }
  
  private parseDesignParametersResponse(text: string, citations: any[]): any[] {
    const sections = [];
    
    console.log('\n=== PARSING DESIGN PARAMETERS RESPONSE ===');
    console.log('Text length:', text.length);
    
    try {
      // Try to extract JSON from the response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON object found in response');
      }
      
      const jsonData = JSON.parse(jsonMatch[0]);
      console.log('Successfully parsed JSON response');
      
      // Create sections for each design parameter with actionable data
      
      // 1. Design Type Section
      if (jsonData.designType) {
        sections.push({
          title: 'Study Design Type',
          content: `Recommendation: ${jsonData.designType.recommendation}\n\n${jsonData.designType.rationale || ''}`,
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'designType',
            value: jsonData.designType.recommendation,
            examples: jsonData.designType.examples || []
          },
          citations: this.extractNCTCitations(jsonData.designType.examples || [], citations),
          confidence: 0.85,
        });
      }
      
      // 2. Blinding Section
      if (jsonData.blinding) {
        sections.push({
          title: 'Blinding Strategy',
          content: `Recommendation: ${jsonData.blinding.recommendation}\n\n${jsonData.blinding.rationale || ''}\n\nKey Considerations:\n${(jsonData.blinding.considerations || []).map((c: string) => `• ${c}`).join('\n')}`,
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'blinding',
            value: jsonData.blinding.recommendation
          },
          citations: [],
          confidence: 0.85,
        });
      }
      
      // 3. Control Type Section
      if (jsonData.controlType) {
        let controlContent = `Recommendation: ${jsonData.controlType.recommendation}`;
        if (jsonData.controlType.specificComparator) {
          controlContent += `\nSpecific Comparator: ${jsonData.controlType.specificComparator}`;
        }
        controlContent += `\n\n${jsonData.controlType.rationale || ''}`;
        if (jsonData.controlType.ethicalConsiderations) {
          controlContent += `\n\nEthical Considerations: ${jsonData.controlType.ethicalConsiderations}`;
        }
        
        sections.push({
          title: 'Control Type',
          content: controlContent,
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'controlType',
            value: jsonData.controlType.recommendation,
            specificComparator: jsonData.controlType.specificComparator
          },
          citations: [],
          confidence: 0.85,
        });
      }
      
      // 4. Randomization Ratio Section
      if (jsonData.randomizationRatio) {
        sections.push({
          title: 'Randomization Ratio',
          content: `Recommendation: ${jsonData.randomizationRatio.recommendation}\n\n${jsonData.randomizationRatio.rationale || ''}\n\nSample Size Impact: ${jsonData.randomizationRatio.sampleSizeImpact || 'Not specified'}`,
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'randomizationRatio',
            value: jsonData.randomizationRatio.recommendation
          },
          citations: [],
          confidence: 0.85,
        });
      }
      
      // 5. Additional Considerations Section (non-actionable)
      if (jsonData.additionalConsiderations) {
        const considerations = [];
        if (jsonData.additionalConsiderations.stratificationFactors?.length > 0) {
          considerations.push(`Stratification Factors:\n${jsonData.additionalConsiderations.stratificationFactors.map((f: string) => `• ${f}`).join('\n')}`);
        }
        if (jsonData.additionalConsiderations.interimAnalyses) {
          considerations.push(`Interim Analyses: ${jsonData.additionalConsiderations.interimAnalyses}`);
        }
        if (jsonData.additionalConsiderations.adaptiveElements) {
          considerations.push(`Adaptive Elements: ${jsonData.additionalConsiderations.adaptiveElements}`);
        }
        if (jsonData.additionalConsiderations.regulatoryPrecedent) {
          considerations.push(`Regulatory Precedent: ${jsonData.additionalConsiderations.regulatoryPrecedent}`);
        }
        
        if (considerations.length > 0) {
          sections.push({
            title: 'Additional Considerations',
            content: considerations.join('\n\n'),
            type: 'considerations',
            actionable: false,
            citations: [],
            confidence: 0.75,
          });
        }
      }
      
      // Add citations if available
      if (jsonData.citations && jsonData.citations.length > 0) {
        const citationList = jsonData.citations.map((cit: any) => ({
          id: cit.nctNumber || cit,
          title: cit.title || `Referenced study ${cit.nctNumber || cit}`,
          url: `s3://trialynx-clinical-trials-gov/text-documents/${(cit.nctNumber || cit).substring(0, 6)}/${cit.nctNumber || cit}.txt`,
          relevance: cit.relevance || 0.80,
        }));
        
        // Add citations to the first section
        if (sections.length > 0 && citationList.length > 0) {
          sections[0].citations = citationList;
        }
      }
      
    } catch (error) {
      console.log('Failed to parse JSON, falling back to text parsing:', error);
      
      // Fallback to text parsing if JSON parsing fails
      return this.parseResponseIntoSections(text, citations, 'design-parameters');
    }
    
    console.log('Parsed sections count:', sections.length);
    sections.forEach((s, i) => {
      console.log(`  Section ${i + 1}: ${s.title} (type: ${s.type}, actionable: ${s.actionable})`);
      if (s.actionableData) {
        console.log(`    - Actionable data: field=${s.actionableData.field}, value=${s.actionableData.value}`);
      }
    });
    console.log('===========================================\n');
    
    return sections;
  }
  
  private parseStudyDesignResponse(text: string, citations: any[]): any[] {
    const sections = [];
    
    console.log('\n=== PARSING STUDY DESIGN RESPONSE ===');
    console.log('Text length:', text.length);
    
    try {
      // Try to extract JSON from the response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON object found in response');
      }
      
      const jsonData = JSON.parse(jsonMatch[0]);
      console.log('Successfully parsed JSON for study design');
      
      // 1. Design Type Section
      if (jsonData.designType) {
        sections.push({
          title: 'Design Type Recommendation',
          content: `**Recommendation:** ${jsonData.designType.recommendation}\n\n${jsonData.designType.rationale}`,
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'designType',
            value: jsonData.designType.recommendation
          },
          citations: [],
          confidence: 0.85,
        });
      }
      
      // 2. Blinding Section  
      if (jsonData.blinding) {
        sections.push({
          title: 'Blinding Strategy',
          content: `**Recommendation:** ${jsonData.blinding.recommendation}\n\n${jsonData.blinding.rationale}`,
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'blinding',
            value: jsonData.blinding.recommendation
          },
          citations: [],
          confidence: 0.85,
        });
      }
      
      // 3. Control Type Section
      if (jsonData.controlType) {
        sections.push({
          title: 'Control Strategy',
          content: `**Recommendation:** ${jsonData.controlType.recommendation}\n\n${jsonData.controlType.rationale}`,
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'controlType',
            value: jsonData.controlType.recommendation
          },
          citations: [],
          confidence: 0.85,
        });
      }
      
      // 4. Randomization Ratio Section
      if (jsonData.randomizationRatio) {
        sections.push({
          title: 'Randomization Ratio',
          content: `**Recommendation:** ${jsonData.randomizationRatio.recommendation}\n\n${jsonData.randomizationRatio.rationale}`,
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'randomizationRatio',
            value: jsonData.randomizationRatio.recommendation
          },
          citations: [],
          confidence: 0.85,
        });
      }
      
      // 5. Benchmark Studies Section
      if (jsonData.benchmarkStudies && jsonData.benchmarkStudies.length > 0) {
        const benchmarkContent = jsonData.benchmarkStudies.map((study: any) => {
          return `**${study.nctNumber}**\n` +
                 `Design: ${study.designType}, Blinding: ${study.blinding}, Control: ${study.controlType}\n` +
                 `Randomization: ${study.randomizationRatio}\n` +
                 `Relevance: ${study.relevance}`;
        }).join('\n\n---\n\n');
        
        const benchmarkCitations = jsonData.benchmarkStudies
          .filter((study: any) => study.nctNumber && study.nctNumber.match(/NCT\d{8}/))
          .map((study: any) => ({
            id: study.nctNumber,
            title: `Referenced benchmark study`,
            url: `s3://trialynx-clinical-trials-gov/text-documents/${study.nctNumber.substring(0, 6)}/${study.nctNumber}.txt`,
            relevance: 0.85,
          }));
        
        sections.push({
          title: 'Benchmark Studies',
          content: benchmarkContent,
          type: 'references',
          actionable: false,
          citations: benchmarkCitations,
          confidence: 0.80,
        });
      }
      
    } catch (error) {
      console.log('Failed to parse JSON, falling back to text parsing:', error);
      return this.parseResponseIntoSections(text, citations, 'study-design');
    }
    
    console.log('Parsed study design sections count:', sections.length);
    sections.forEach((s, i) => {
      console.log(`  Section ${i + 1}: ${s.title} (type: ${s.type}, actionable: ${s.actionable})`);
      if (s.actionableData) {
        console.log(`    - Actionable data: field=${s.actionableData.field}, value=${s.actionableData.value}`);
      }
    });
    console.log('=======================================\n');
    
    return sections;
  }
  
  private parseDesignDescriptorsResponse(text: string, citations: any[]): any[] {
    const sections = [];
    
    console.log('\n=== PARSING DESIGN DESCRIPTORS RESPONSE ===');
    console.log('Text length:', text.length);
    
    try {
      // Try to extract JSON from the response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON object found in response');
      }
      
      const jsonData = JSON.parse(jsonMatch[0]);
      console.log('Successfully parsed JSON for design descriptors');
      
      // 1. Main descriptors section
      if (jsonData.descriptors && Array.isArray(jsonData.descriptors)) {
        // Create a section for each descriptor with its rationale
        jsonData.descriptors.forEach((descriptor: string) => {
          const rationale = jsonData.rationale && jsonData.rationale[descriptor] 
            ? jsonData.rationale[descriptor] 
            : 'Recommended based on study design parameters';
            
          sections.push({
            title: descriptor,
            content: rationale,
            type: 'recommendation',
            actionable: true,
            actionableData: {
              field: 'designDescriptor',
              value: descriptor
            },
            citations: [],
            confidence: 0.85,
          });
        });
      }
      
      // 2. Benchmark Studies Section
      if (jsonData.benchmarkStudies && jsonData.benchmarkStudies.length > 0) {
        const benchmarkContent = jsonData.benchmarkStudies.map((study: any) => {
          const descriptorsList = study.descriptors && Array.isArray(study.descriptors) 
            ? study.descriptors.join(', ') 
            : 'No descriptors specified';
          return `**${study.nctNumber}**\n` +
                 `Descriptors: ${descriptorsList}\n` +
                 `Relevance: ${study.relevance}`;
        }).join('\n\n---\n\n');
        
        const benchmarkCitations = jsonData.benchmarkStudies
          .filter((study: any) => study.nctNumber && study.nctNumber.match(/NCT\d{8}/))
          .map((study: any) => ({
            id: study.nctNumber,
            title: `Referenced benchmark study`,
            url: `s3://trialynx-clinical-trials-gov/text-documents/${study.nctNumber.substring(0, 6)}/${study.nctNumber}.txt`,
            relevance: 0.85,
          }));
        
        sections.push({
          title: 'Benchmark Studies',
          content: benchmarkContent,
          type: 'references',
          actionable: false,
          citations: benchmarkCitations,
          confidence: 0.80,
        });
      }
      
    } catch (error) {
      console.log('Failed to parse JSON, falling back to text parsing:', error);
      return this.parseResponseIntoSections(text, citations, 'design-descriptors');
    }
    
    console.log('Parsed design descriptors sections count:', sections.length);
    sections.forEach((s, i) => {
      console.log(`  Section ${i + 1}: ${s.title} (type: ${s.type}, actionable: ${s.actionable})`);
      if (s.actionableData) {
        console.log(`    - Actionable data: field=${s.actionableData.field}, value=${s.actionableData.value}`);
      }
    });
    console.log('=======================================\n');
    
    return sections;
  }
  
  private extractNCTCitations(nctNumbers: string[], allCitations: any[]): any[] {
    // Create synthetic citations from NCT numbers
    return nctNumbers
      .filter((nct: string) => nct.match(/NCT\d{8}/))
      .map((nct: string) => ({
        id: nct,
        title: `Referenced study ${nct}`,
        url: `s3://trialynx-clinical-trials-gov/text-documents/${nct.substring(0, 6)}/${nct}.txt`,
        relevance: 0.80,
      }));
  }
  
  private parseInclusionCriteriaResponse(text: string, citations: any[]): any[] {
    const sections = [];
    
    console.log('\n=== PARSING INCLUSION CRITERIA RESPONSE ===');
    console.log('Text length:', text.length);
    
    try {
      // Try to parse as JSON
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }
      
      const jsonData = JSON.parse(jsonMatch[0]);
      console.log('Successfully parsed JSON for inclusion criteria');
      
      // 1. Prioritized List (Main actionable recommendation) - MOVED TO TOP
      if (jsonData.prioritizedList && jsonData.prioritizedList.length > 0) {
        sections.push({
          title: 'Recommended Inclusion Criteria',
          content: `**Priority Criteria:**\n${jsonData.prioritizedList.map((c: string, i: number) => `${i + 1}. ${c}`).join('\n')}`,
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'bulkInclusionCriteria',
            criteria: jsonData.prioritizedList,
            category: 'all'
          },
          criteriaItems: jsonData.prioritizedList.map(criterion => ({
            text: criterion,
            actionableData: {
              field: 'singleInclusionCriterion',
              value: criterion,
              category: 'priority'
            }
          })),
          citations: [],
          confidence: 0.95,
        });
      }
      
      // 2. Core Inclusion Criteria - Medical
      if (jsonData.coreInclusionCriteria) {
        const core = jsonData.coreInclusionCriteria;
        let criteriaList: string[] = [];
        
        if (core.diagnosis && core.diagnosis.length > 0) {
          criteriaList.push(...core.diagnosis);
        }
        if (core.clinicalPresentation && core.clinicalPresentation.length > 0) {
          criteriaList.push(...core.clinicalPresentation);
        }
        if (core.treatmentHistory && core.treatmentHistory.length > 0) {
          criteriaList.push(...core.treatmentHistory);
        }
        
        if (criteriaList.length > 0) {
          sections.push({
            title: 'Core Medical Criteria',
            content: criteriaList.map(c => `• ${c}`).join('\n'),
            type: 'recommendation',
            actionable: true,
            actionableData: {
              field: 'bulkInclusionCriteria',
              criteria: criteriaList,
              category: 'medical'
            },
            criteriaItems: criteriaList.map(criterion => ({
              text: criterion,
              actionableData: {
                field: 'singleInclusionCriterion',
                value: criterion,
                category: 'medical'
              }
            })),
            citations: [],
            confidence: 0.90,
          });
        }
      }
      
      // 3. Demographic Criteria
      if (jsonData.demographicCriteria) {
        const demo = jsonData.demographicCriteria;
        let criteriaList: string[] = [];
        
        if (demo.age) {
          criteriaList.push(demo.age);
        }
        if (demo.gender && demo.gender !== 'Not specified') {
          criteriaList.push(demo.gender);
        }
        if (demo.other && demo.other.length > 0) {
          criteriaList.push(...demo.other.filter((o: string) => o && o !== 'Not specified'));
        }
        
        if (criteriaList.length > 0) {
          sections.push({
            title: 'Demographic Requirements',
            content: criteriaList.map(c => `• ${c}`).join('\n'),
            type: 'information',
            actionable: true,
            actionableData: {
              field: 'bulkInclusionCriteria',
              criteria: criteriaList,
              category: 'demographic'
            },
            criteriaItems: criteriaList.map(criterion => ({
              text: criterion,
              actionableData: {
                field: 'singleInclusionCriterion',
                value: criterion,
                category: 'demographic'
              }
            })),
            citations: [],
            confidence: 0.85,
          });
        }
      }
      
      // 4. Laboratory Requirements
      if (jsonData.laboratoryRequirements) {
        const lab = jsonData.laboratoryRequirements;
        let criteriaList: string[] = [];
        
        if (lab.required && lab.required.length > 0) {
          criteriaList.push(...lab.required);
        }
        
        if (criteriaList.length > 0) {
          sections.push({
            title: 'Laboratory Requirements',
            content: `**Required Tests:**\n${criteriaList.map(c => `• ${c}`).join('\n')}`,
            type: 'requirements',
            actionable: true,
            actionableData: {
              field: 'bulkInclusionCriteria',
              criteria: criteriaList,
              category: 'laboratory'
            },
            criteriaItems: criteriaList.map(criterion => ({
              text: criterion,
              actionableData: {
                field: 'singleInclusionCriterion',
                value: criterion,
                category: 'laboratory'
              }
            })),
            citations: [],
            confidence: 0.85,
          });
        }
      }
      
      // 5. Benchmark Studies
      if (jsonData.benchmarkStudies && jsonData.benchmarkStudies.length > 0) {
        const validStudies = jsonData.benchmarkStudies.filter((s: any) => 
          s.nctNumber && /^NCT\d+$/i.test(s.nctNumber)
        );
        
        if (validStudies.length > 0) {
          const content = validStudies.map((study: any) => 
            `**${study.nctNumber}**\n${study.keyInclusionCriteria?.join(', ') || 'Criteria not specified'}`
          ).join('\n\n');
          
          sections.push({
            title: 'Benchmark Studies',
            content,
            type: 'references',
            citations: validStudies.map((s: any) => ({
              id: s.nctNumber,
              title: `Study ${s.nctNumber}`,
              url: `https://clinicaltrials.gov/study/${s.nctNumber}`
            })),
            confidence: 0.80,
          });
        }
      }
      
      // 6. Considerations
      if (jsonData.considerations && jsonData.considerations.length > 0) {
        sections.push({
          title: 'Special Considerations',
          content: jsonData.considerations.map((c: string) => `• ${c}`).join('\n'),
          type: 'considerations',
          citations: [],
          confidence: 0.75,
        });
      }
      
    } catch (error) {
      console.error('Error parsing inclusion criteria JSON:', error);
      // Fallback to text parsing
      sections.push({
        title: 'Inclusion Criteria Recommendations',
        content: text,
        type: 'information',
        citations: [],
        confidence: 0.50,
      });
    }
    
    console.log('Parsed sections count:', sections.length);
    console.log('===========================================\n');
    
    return sections;
  }
  
  private parseExclusionCriteriaResponse(text: string, citations: any[]): any[] {
    const sections = [];
    
    console.log('\n=== PARSING EXCLUSION CRITERIA RESPONSE ===');
    console.log('Text length:', text.length);
    
    try {
      // Try to parse as JSON
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }
      
      const jsonData = JSON.parse(jsonMatch[0]);
      console.log('Successfully parsed JSON for exclusion criteria');
      
      // 1. Prioritized List (Main actionable recommendation) - MOVED TO TOP
      if (jsonData.prioritizedList && jsonData.prioritizedList.length > 0) {
        sections.push({
          title: 'Recommended Exclusion Criteria',
          content: `**Priority Exclusions:**\n${jsonData.prioritizedList.map((c: string, i: number) => `${i + 1}. ${c}`).join('\n')}`,
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'bulkExclusionCriteria',
            criteria: jsonData.prioritizedList,
            category: 'all'
          },
          criteriaItems: jsonData.prioritizedList.map(criterion => ({
            text: criterion,
            actionableData: {
              field: 'singleExclusionCriterion',
              value: criterion,
              category: 'priority'
            }
          })),
          citations: [],
          confidence: 0.95,
        });
      }
      
      // 2. Medical Exclusions
      if (jsonData.medicalExclusions) {
        const medical = jsonData.medicalExclusions;
        let criteriaList: string[] = [];
        
        if (medical.comorbidities && medical.comorbidities.length > 0) {
          criteriaList.push(...medical.comorbidities);
        }
        if (medical.concomitantMedications && medical.concomitantMedications.length > 0) {
          criteriaList.push(...medical.concomitantMedications);
        }
        if (medical.allergiesContraindications && medical.allergiesContraindications.length > 0) {
          criteriaList.push(...medical.allergiesContraindications);
        }
        
        if (criteriaList.length > 0) {
          sections.push({
            title: 'Medical Exclusions',
            content: criteriaList.map(c => `• ${c}`).join('\n'),
            type: 'safety',
            actionable: true,
            actionableData: {
              field: 'bulkExclusionCriteria',
              criteria: criteriaList,
              category: 'medical'
            },
            criteriaItems: criteriaList.map(criterion => ({
              text: criterion,
              actionableData: {
                field: 'singleExclusionCriterion',
                value: criterion,
                category: 'medical'
              }
            })),
            citations: [],
            confidence: 0.95,
          });
        }
      }
      
      // 3. Safety Exclusions
      if (jsonData.safetyExclusions) {
        const safety = jsonData.safetyExclusions;
        let criteriaList: string[] = [];
        
        if (safety.pregnancy) {
          criteriaList.push(safety.pregnancy);
        }
        if (safety.laboratoryValues && safety.laboratoryValues.length > 0) {
          criteriaList.push(...safety.laboratoryValues);
        }
        if (safety.vitalSigns && safety.vitalSigns.length > 0) {
          criteriaList.push(...safety.vitalSigns);
        }
        if (safety.suicideRisk) {
          criteriaList.push(safety.suicideRisk);
        }
        
        if (criteriaList.length > 0) {
          sections.push({
            title: 'Safety Exclusions',
            content: `**Critical Safety Criteria:**\n${criteriaList.map(c => `• ${c}`).join('\n')}`,
            type: 'safety',
            actionable: true,
            actionableData: {
              field: 'bulkExclusionCriteria',
              criteria: criteriaList,
              category: 'safety'
            },
            criteriaItems: criteriaList.map(criterion => ({
              text: criterion,
              actionableData: {
                field: 'singleExclusionCriterion',
                value: criterion,
                category: 'safety'
              }
            })),
            citations: [],
            confidence: 0.95,
          });
        }
      }
      
      // 4. Protocol Exclusions
      if (jsonData.protocolExclusions) {
        const protocol = jsonData.protocolExclusions;
        let criteriaList: string[] = [];
        
        if (protocol.priorParticipation) {
          criteriaList.push(protocol.priorParticipation);
        }
        if (protocol.geographicRestrictions) {
          criteriaList.push(protocol.geographicRestrictions);
        }
        if (protocol.complianceConcerns && protocol.complianceConcerns.length > 0) {
          criteriaList.push(...protocol.complianceConcerns);
        }
        
        if (criteriaList.length > 0) {
          sections.push({
            title: 'Protocol & Compliance Exclusions',
            content: criteriaList.map(c => `• ${c}`).join('\n'),
            type: 'protocol',
            actionable: true,
            actionableData: {
              field: 'bulkExclusionCriteria',
              criteria: criteriaList,
              category: 'protocol'
            },
            criteriaItems: criteriaList.map(criterion => ({
              text: criterion,
              actionableData: {
                field: 'singleExclusionCriterion',
                value: criterion,
                category: 'protocol'
              }
            })),
            citations: [],
            confidence: 0.85,
          });
        }
      }
      
      // 5. Risk Mitigation
      if (jsonData.riskMitigation && jsonData.riskMitigation.length > 0) {
        sections.push({
          title: 'Risk Mitigation Strategies',
          content: jsonData.riskMitigation.map((r: string) => `• ${r}`).join('\n'),
          type: 'guidance',
          citations: [],
          confidence: 0.80,
        });
      }
      
      // 6. Benchmark Studies
      if (jsonData.benchmarkStudies && jsonData.benchmarkStudies.length > 0) {
        const validStudies = jsonData.benchmarkStudies.filter((s: any) => 
          s.nctNumber && /^NCT\d+$/i.test(s.nctNumber)
        );
        
        if (validStudies.length > 0) {
          const content = validStudies.map((study: any) => 
            `**${study.nctNumber}**\n${study.keyExclusionCriteria?.join(', ') || 'Criteria not specified'}`
          ).join('\n\n');
          
          sections.push({
            title: 'Benchmark Studies',
            content,
            type: 'references',
            citations: validStudies.map((s: any) => ({
              id: s.nctNumber,
              title: `Study ${s.nctNumber}`,
              url: `https://clinicaltrials.gov/study/${s.nctNumber}`
            })),
            confidence: 0.80,
          });
        }
      }
      
    } catch (error) {
      console.error('Error parsing exclusion criteria JSON:', error);
      // Fallback to text parsing
      sections.push({
        title: 'Exclusion Criteria Recommendations',
        content: text,
        type: 'information',
        citations: [],
        confidence: 0.50,
      });
    }
    
    console.log('Parsed sections count:', sections.length);
    console.log('===========================================\n');
    
    return sections;
  }
  
  private parseSiteStrategyResponse(text: string, citations: any[]): any[] {
    const sections = [];
    
    console.log('\n=== PARSING SITE STRATEGY RESPONSE ===');
    console.log('Text length:', text.length);
    
    try {
      // Try to parse as JSON
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }
      
      const jsonData = JSON.parse(jsonMatch[0]);
      console.log('Successfully parsed JSON for site strategy');
      
      // 1. Prioritized List (Main actionable recommendation) - AT TOP
      if (jsonData.prioritizedList && jsonData.prioritizedList.length > 0) {
        sections.push({
          title: 'Recommended Site Strategy',
          content: `**Priority Recommendations:**\n${jsonData.prioritizedList.map((r: string, i: number) => `${i + 1}. ${r}`).join('\n')}`,
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'bulkSiteStrategy',
            recommendations: jsonData.prioritizedList,
            category: 'all'
          },
          strategicItems: jsonData.prioritizedList.map(recommendation => ({
            text: recommendation,
            actionableData: {
              field: 'singleSiteRecommendation',
              value: recommendation,
              category: 'priority'
            }
          })),
          citations: [],
          confidence: 0.95,
        });
      }
      
      // 2. Site Count & Distribution Strategy
      if (jsonData.recommendedStrategy) {
        const strategy = jsonData.recommendedStrategy;
        let content = '';
        const actionableData: any = {};
        
        if (strategy.numberOfSites) {
          content += `**Recommended Sites:** ${strategy.numberOfSites}\n`;
          actionableData.numberOfSites = strategy.numberOfSites;
        }
        if (strategy.siteTypes) {
          content += `**Site Types:** ${strategy.siteTypes}\n`;
        }
        if (strategy.rationale) {
          content += `**Rationale:** ${strategy.rationale}\n`;
        }
        
        // Add site distribution strategy if available
        if (jsonData.siteDistributionStrategy) {
          content += `\n**Distribution Strategy:**\n${jsonData.siteDistributionStrategy}`;
          actionableData.siteDistribution = jsonData.siteDistributionStrategy;
        }
        
        if (content) {
          sections.push({
            title: 'Site Count & Distribution Strategy',
            content,
            type: 'recommendation',
            actionable: true,
            actionableData: {
              field: 'siteCountStrategy',
              ...actionableData
            },
            citations: [],
            confidence: 0.90,
          });
        }
      }
      
      // 3. Geographic Distribution
      if (jsonData.geographicDistribution) {
        const geo = jsonData.geographicDistribution;
        let content = '';
        
        if (geo.primaryRegions && geo.primaryRegions.length > 0) {
          content += `**Primary Regions:** ${geo.primaryRegions.join(', ')}\n`;
        }
        if (geo.secondaryRegions && geo.secondaryRegions.length > 0) {
          content += `**Secondary Regions:** ${geo.secondaryRegions.join(', ')}\n`;
        }
        if (geo.distributionRationale) {
          content += `**Geographic Rationale:** ${geo.distributionRationale}\n`;
        }
        if (geo.urbanRuralMix) {
          content += `**Urban/Rural Mix:** ${geo.urbanRuralMix}\n`;
        }
        
        if (content) {
          sections.push({
            title: 'Geographic Distribution',
            content,
            type: 'information',
            citations: [],
            confidence: 0.85,
          });
        }
      }
      
      // 4. Country Recommendations with Individual Selection
      if (jsonData.countryRecommendations && jsonData.countryRecommendations.length > 0) {
        const countries = jsonData.countryRecommendations;
        let content = '';
        
        countries.forEach((country: any, index: number) => {
          content += `**${country.country}** (${country.sites} sites)\n`;
          if (country.rationale) {
            content += `• ${country.rationale}\n`;
          }
          if (country.siteTypes) {
            content += `• Site Types: ${country.siteTypes}\n`;
          }
          if (country.recruitmentAdvantages) {
            content += `• Recruitment: ${country.recruitmentAdvantages}\n`;
          }
          content += '\n';
        });
        
        sections.push({
          title: 'Country-Specific Recommendations',
          content,
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'bulkCountries',
            countries: countries.map((c: any) => c.country),
            sitesPerCountry: countries.reduce((acc: any, c: any) => {
              acc[c.country] = parseInt(c.sites) || 1;
              return acc;
            }, {})
          },
          countryItems: countries.map((country: any) => ({
            text: `${country.country} (${country.sites} sites) - ${country.rationale}`,
            actionableData: {
              field: 'singleCountryRecommendation',
              country: country.country,
              sites: parseInt(country.sites) || 1,
              rationale: country.rationale,
              category: 'geographic'
            }
          })),
          citations: [],
          confidence: 0.90,
        });
      }
      
      // 5. Site Selection Criteria
      if (jsonData.siteSelectionCriteria && jsonData.siteSelectionCriteria.length > 0) {
        sections.push({
          title: 'Site Selection Criteria',
          content: jsonData.siteSelectionCriteria.map((c: string) => `• ${c}`).join('\n'),
          type: 'guidance',
          citations: [],
          confidence: 0.80,
        });
      }
      
      // 6. Operational Considerations
      if (jsonData.operationalConsiderations && jsonData.operationalConsiderations.length > 0) {
        sections.push({
          title: 'Operational Considerations',
          content: jsonData.operationalConsiderations.map((o: string) => `• ${o}`).join('\n'),
          type: 'considerations',
          citations: [],
          confidence: 0.75,
        });
      }
      
      // 7. Benchmark Studies
      if (jsonData.benchmarkStudies && jsonData.benchmarkStudies.length > 0) {
        const validStudies = jsonData.benchmarkStudies.filter((s: any) => 
          s.nctNumber && /^NCT\d+$/i.test(s.nctNumber)
        );
        
        if (validStudies.length > 0) {
          const content = validStudies.map((study: any) => 
            `**${study.nctNumber}** (${study.numberOfSites || 'N/A'} sites)\n` +
            `Countries: ${study.countries?.join(', ') || 'Not specified'}\n` +
            `${study.siteStrategy || 'Strategy not specified'}`
          ).join('\n\n');
          
          sections.push({
            title: 'Benchmark Studies',
            content,
            type: 'references',
            citations: validStudies.map((s: any) => ({
              id: s.nctNumber,
              title: `Study ${s.nctNumber}`,
              url: `https://clinicaltrials.gov/study/${s.nctNumber}`
            })),
            confidence: 0.80,
          });
        }
      }
      
    } catch (error) {
      console.error('Error parsing site strategy JSON:', error);
      // Fallback to text parsing
      sections.push({
        title: 'Site Strategy Recommendations',
        content: text,
        type: 'information',
        citations: [],
        confidence: 0.50,
      });
    }
    
    console.log('Parsed sections count:', sections.length);
    console.log('===========================================\n');
    
    return sections;
  }
  
  private parseStudyPeriodsResponse(text: string, citations: any[]): any[] {
    const sections = [];
    
    console.log('\n=== PARSING STUDY PERIODS RESPONSE ===');
    console.log('Text length:', text.length);
    
    try {
      // Try to parse as JSON
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }
      
      const jsonData = JSON.parse(jsonMatch[0]);
      console.log('Successfully parsed JSON for study periods');
      
      // 1. Study Periods Recommendation
      if (jsonData.studyPeriods) {
        const periods = jsonData.studyPeriods;
        let content = '';
        const actionableData: any = {};
        
        if (periods.screening) {
          content += `**Screening Period:** ${periods.screening.duration}`;
          if (periods.screening.typicalRange) {
            content += ` (typical: ${periods.screening.typicalRange})`;
          }
          content += '\n';
          if (periods.screening.rationale) {
            content += `Rationale: ${periods.screening.rationale}\n`;
          }
          content += '\n';
          actionableData.screeningPeriod = periods.screening.duration;
        }
        
        if (periods.baseline) {
          content += `**Baseline Period:** ${periods.baseline.duration}`;
          if (periods.baseline.typicalRange) {
            content += ` (typical: ${periods.baseline.typicalRange})`;
          }
          content += '\n';
          if (periods.baseline.rationale) {
            content += `Rationale: ${periods.baseline.rationale}\n`;
          }
          content += '\n';
          actionableData.baselinePeriod = periods.baseline.duration;
        }
        
        if (periods.treatment) {
          content += `**Treatment Period:** ${periods.treatment.duration}`;
          if (periods.treatment.typicalRange) {
            content += ` (typical: ${periods.treatment.typicalRange})`;
          }
          content += '\n';
          if (periods.treatment.rationale) {
            content += `Rationale: ${periods.treatment.rationale}\n`;
          }
          content += '\n';
          actionableData.treatmentPeriod = periods.treatment.duration;
        }
        
        if (periods.followUp) {
          content += `**Follow-up Period:** ${periods.followUp.duration}`;
          if (periods.followUp.typicalRange) {
            content += ` (typical: ${periods.followUp.typicalRange})`;
          }
          content += '\n';
          if (periods.followUp.rationale) {
            content += `Rationale: ${periods.followUp.rationale}\n`;
          }
          content += '\n';
          actionableData.followUpPeriod = periods.followUp.duration;
        }
        
        if (periods.totalDuration) {
          content += `\n**Total Study Duration:** ${periods.totalDuration}`;
        }
        
        sections.push({
          title: 'Recommended Study Periods',
          content: content.trim(),
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'studyPeriods',
            ...actionableData
          },
          citations: [],
          confidence: 0.90,
        });
      }
      
      // 2. Duration Factors
      if (jsonData.durationFactors && jsonData.durationFactors.length > 0) {
        sections.push({
          title: 'Duration Considerations',
          content: jsonData.durationFactors.map((f: string) => `• ${f}`).join('\n'),
          type: 'considerations',
          citations: [],
          confidence: 0.80,
        });
      }
      
      // 3. Benchmark Studies
      if (jsonData.benchmarkStudies && jsonData.benchmarkStudies.length > 0) {
        const validStudies = jsonData.benchmarkStudies.filter((s: any) => 
          s.nctNumber && /^NCT\d+$/i.test(s.nctNumber)
        );
        
        if (validStudies.length > 0) {
          const content = validStudies.map((study: any) => 
            `**${study.nctNumber}**\n` +
            `• Screening: ${study.screeningDuration || 'N/A'}\n` +
            `• Treatment: ${study.treatmentDuration || 'N/A'}\n` +
            `• Follow-up: ${study.followUpDuration || 'N/A'}\n` +
            `• Total: ${study.totalDuration || 'N/A'}`
          ).join('\n\n');
          
          sections.push({
            title: 'Benchmark Studies',
            content,
            type: 'references',
            citations: validStudies.map((s: any) => ({
              id: s.nctNumber,
              title: `Study ${s.nctNumber}`,
              url: `https://clinicaltrials.gov/study/${s.nctNumber}`
            })),
            confidence: 0.85,
          });
        }
      }
      
      // 4. Recommendations
      if (jsonData.recommendations && jsonData.recommendations.length > 0) {
        sections.push({
          title: 'Additional Recommendations',
          content: jsonData.recommendations.map((r: string) => `• ${r}`).join('\n'),
          type: 'guidance',
          citations: [],
          confidence: 0.75,
        });
      }
      
    } catch (error) {
      console.error('Error parsing study periods JSON:', error);
      // Fallback to text parsing
      sections.push({
        title: 'Study Period Recommendations',
        content: text,
        type: 'information',
        citations: [],
        confidence: 0.50,
      });
    }
    
    console.log('Parsed sections count:', sections.length);
    console.log('===========================================\n');
    
    return sections;
  }
  
  private parseSitePlanningResponse(text: string, citations: any[]): any[] {
    const sections = [];
    
    console.log('\n=== PARSING SITE PLANNING RESPONSE ===');
    console.log('Text length:', text.length);
    
    try {
      // Try to parse as JSON
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }
      
      const jsonData = JSON.parse(jsonMatch[0]);
      console.log('Successfully parsed JSON for site planning');
      
      // 1. Site Planning Recommendations
      if (jsonData.sitePlanning) {
        const planning = jsonData.sitePlanning;
        
        // Number of Sites
        if (planning.numberOfSites) {
          const sites = planning.numberOfSites;
          let content = `**Recommended:** ${sites.recommended} sites\n`;
          if (sites.minimum && sites.maximum) {
            content += `**Range:** ${sites.minimum} to ${sites.maximum} sites\n`;
          }
          if (sites.rationale) {
            content += `**Rationale:** ${sites.rationale}`;
          }
          
          sections.push({
            title: 'Number of Sites',
            content: content.trim(),
            type: 'recommendation',
            actionable: true,
            actionableData: {
              field: 'numberOfSites',
              value: sites.recommended
            },
            citations: [],
            confidence: 0.90,
          });
        }
        
        // Recruitment Rate
        if (planning.recruitmentRate) {
          const rate = planning.recruitmentRate;
          let content = `**Expected:** ${rate.expected}\n`;
          if (rate.range) {
            content += `**Range:** ${rate.range}\n`;
          }
          if (rate.factors && rate.factors.length > 0) {
            content += `\n**Key Factors:**\n${rate.factors.map((f: string) => `• ${f}`).join('\n')}`;
          }
          
          sections.push({
            title: 'Recruitment Rate',
            content: content.trim(),
            type: 'recommendation',
            actionable: true,
            actionableData: {
              field: 'recruitmentRate',
              value: rate.expected
            },
            citations: [],
            confidence: 0.85,
          });
        }
        
        // Geographic Distribution
        if (planning.geographicDistribution) {
          const geo = planning.geographicDistribution;
          
          if (geo.recommended && geo.recommended.length > 0) {
            const regions = geo.recommended.map((r: any) => 
              `**${r.region}:** ${r.siteCount} sites\n` +
              `Countries: ${r.countries.join(', ')}\n` +
              `${r.rationale ? `Rationale: ${r.rationale}` : ''}`
            ).join('\n\n');
            
            const countries: Record<string, number> = {};
            geo.recommended.forEach((r: any) => {
              const sitesPerCountry = Math.ceil(parseInt(r.siteCount) / r.countries.length);
              r.countries.forEach((country: string) => {
                countries[country] = sitesPerCountry;
              });
            });
            
            sections.push({
              title: 'Geographic Distribution',
              content: regions,
              type: 'recommendation',
              actionable: true,
              actionableData: {
                field: 'sitesPerCountry',
                value: countries
              },
              citations: [],
              confidence: 0.85,
            });
          }
          
          if (geo.considerations && geo.considerations.length > 0) {
            sections.push({
              title: 'Geographic Considerations',
              content: geo.considerations.map((c: string) => `• ${c}`).join('\n'),
              type: 'information',
              citations: [],
              confidence: 0.80,
            });
          }
        }
        
        // Site Selection Criteria
        if (planning.siteSelectionCriteria) {
          const criteria = planning.siteSelectionCriteria;
          let content = '';
          
          if (criteria.essential && criteria.essential.length > 0) {
            content += `**Essential Requirements:**\n${criteria.essential.map((c: string) => `• ${c}`).join('\n')}\n\n`;
          }
          if (criteria.preferred && criteria.preferred.length > 0) {
            content += `**Preferred Qualifications:**\n${criteria.preferred.map((c: string) => `• ${c}`).join('\n')}`;
          }
          
          if (content) {
            sections.push({
              title: 'Site Selection Criteria',
              content: content.trim(),
              type: 'guidance',
              citations: [],
              confidence: 0.85,
            });
          }
        }
      }
      
      // Supporting Evidence
      if (jsonData.supportingEvidence && jsonData.supportingEvidence.length > 0) {
        const validEvidence = jsonData.supportingEvidence.filter((e: any) => 
          e.nctNumber && e.nctNumber.match(/NCT\d+/)
        );
        
        if (validEvidence.length > 0) {
          const evidenceContent = validEvidence.map((e: any) => 
            `• **${e.nctNumber}**: ${e.sites || 'N/A'} sites, enrolled in ${e.enrollmentTime || 'N/A'}`
          ).join('\n');
          
          sections.push({
            title: 'Supporting Evidence',
            content: evidenceContent,
            type: 'evidence',
            citations: [], // Only use real citations from Bedrock
            confidence: 0.75,
          });
        }
      }
      
    } catch (error) {
      console.error('Failed to parse site planning JSON:', error);
      // Fallback to text parsing
      return this.parseResponseIntoSections(text, citations, 'site-planning');
    }
    
    console.log('Parsed sections count:', sections.length);
    console.log('===========================================\n');
    
    return sections;
  }
  
  private parseEnrollmentProjectionsResponse(text: string, citations: any[]): any[] {
    const sections = [];
    
    console.log('\n=== PARSING ENROLLMENT PROJECTIONS RESPONSE ===');
    console.log('Text length:', text.length);
    
    try {
      // Try to parse as JSON
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }
      
      const jsonData = JSON.parse(jsonMatch[0]);
      console.log('Successfully parsed JSON for enrollment projections');
      
      // 1. Enrollment Projections
      if (jsonData.enrollmentProjections) {
        const projections = jsonData.enrollmentProjections;
        
        // Screen Failure Rate
        if (projections.screenFailureRate) {
          const failure = projections.screenFailureRate;
          let content = `**Expected Rate:** ${failure.expected}\n`;
          if (failure.range) {
            content += `**Typical Range:** ${failure.range}\n`;
          }
          if (failure.commonReasons && failure.commonReasons.length > 0) {
            content += `\n**Common Reasons:**\n${failure.commonReasons.map((r: string) => `• ${r}`).join('\n')}`;
          }
          if (failure.mitigationStrategies && failure.mitigationStrategies.length > 0) {
            content += `\n\n**Mitigation Strategies:**\n${failure.mitigationStrategies.map((s: string) => `• ${s}`).join('\n')}`;
          }
          
          sections.push({
            title: 'Screen Failure Rate',
            content: content.trim(),
            type: 'recommendation',
            actionable: true,
            actionableData: {
              field: 'screenFailureRate',
              value: failure.expected
            },
            citations: [],
            confidence: 0.90,
          });
        }
        
        // Dropout Rate
        if (projections.dropoutRate) {
          const dropout = projections.dropoutRate;
          let content = `**Expected Rate:** ${dropout.expected}\n`;
          
          if (dropout.byPhase) {
            content += `\n**By Study Phase:**\n`;
            if (dropout.byPhase.screening) content += `• Screening: ${dropout.byPhase.screening}\n`;
            if (dropout.byPhase.treatment) content += `• Treatment: ${dropout.byPhase.treatment}\n`;
            if (dropout.byPhase.followUp) content += `• Follow-up: ${dropout.byPhase.followUp}\n`;
          }
          
          if (dropout.commonReasons && dropout.commonReasons.length > 0) {
            content += `\n**Common Reasons:**\n${dropout.commonReasons.map((r: string) => `• ${r}`).join('\n')}`;
          }
          
          if (dropout.retentionStrategies && dropout.retentionStrategies.length > 0) {
            content += `\n\n**Retention Strategies:**\n${dropout.retentionStrategies.map((s: string) => `• ${s}`).join('\n')}`;
          }
          
          sections.push({
            title: 'Dropout Rate',
            content: content.trim(),
            type: 'recommendation',
            actionable: true,
            actionableData: {
              field: 'dropoutRate',
              value: dropout.expected
            },
            citations: [],
            confidence: 0.90,
          });
        }
        
        // Enrollment Timeline
        if (projections.enrollmentTimeline) {
          const timeline = projections.enrollmentTimeline;
          let content = `**Time to Full Enrollment:** ${timeline.monthsToFullEnrollment}\n`;
          if (timeline.rampUpPeriod) {
            content += `**Ramp-up Period:** ${timeline.rampUpPeriod}\n`;
          }
          if (timeline.peakEnrollmentRate) {
            content += `**Peak Enrollment Rate:** ${timeline.peakEnrollmentRate}\n`;
          }
          if (timeline.assumptions && timeline.assumptions.length > 0) {
            content += `\n**Key Assumptions:**\n${timeline.assumptions.map((a: string) => `• ${a}`).join('\n')}`;
          }
          
          sections.push({
            title: 'Enrollment Timeline',
            content: content.trim(),
            type: 'projection',
            citations: [],
            confidence: 0.85,
          });
        }
        
        // Over-enrollment Buffer
        if (projections.overEnrollment) {
          const over = projections.overEnrollment;
          let content = `**Recommended Buffer:** ${over.recommendedBuffer}\n`;
          if (over.rationale) {
            content += `**Rationale:** ${over.rationale}\n`;
          }
          if (over.strategy) {
            content += `**Strategy:** ${over.strategy}`;
          }
          
          sections.push({
            title: 'Over-enrollment Planning',
            content: content.trim(),
            type: 'guidance',
            citations: [],
            confidence: 0.80,
          });
        }
      }
      
      // Benchmark Studies
      if (jsonData.benchmarkStudies && jsonData.benchmarkStudies.length > 0) {
        const validBenchmarks = jsonData.benchmarkStudies.filter((b: any) => 
          b.nctNumber && b.nctNumber.match(/NCT\d+/)
        );
        
        if (validBenchmarks.length > 0) {
          const benchmarkContent = validBenchmarks.map((b: any) => 
            `• **${b.nctNumber}**:\n` +
            `  Screen Failure: ${b.actualScreenFailure || 'N/A'}\n` +
            `  Dropout: ${b.actualDropout || 'N/A'}\n` +
            `  Enrollment Duration: ${b.enrollmentDuration || 'N/A'}\n` +
            `  ${b.lessons ? `Lessons: ${b.lessons}` : ''}`
          ).join('\n\n');
          
          sections.push({
            title: 'Benchmark Studies',
            content: benchmarkContent,
            type: 'evidence',
            citations: validBenchmarks.map((b: any) => ({
              nctNumber: b.nctNumber,
              title: b.lessons || 'Benchmark study',
              relevance: b.lessons
            })),
            confidence: 0.75,
          });
        }
      }
      
    } catch (error) {
      console.error('Failed to parse enrollment projections JSON:', error);
      // Fallback to text parsing
      return this.parseResponseIntoSections(text, citations, 'enrollment-projections');
    }
    
    console.log('Parsed sections count:', sections.length);
    console.log('===========================================\n');
    
    return sections;
  }
  
  private parseDataMonitoringResponse(text: string, citations: any[]): any[] {
    const sections = [];
    
    console.log('\n=== PARSING DATA MONITORING RESPONSE ===');
    console.log('Text length:', text.length);
    
    try {
      // Try to parse as JSON
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }
      
      const jsonData = JSON.parse(jsonMatch[0]);
      console.log('Successfully parsed JSON for data monitoring');
      
      // 1. Data Monitoring Recommendations
      if (jsonData.dataMonitoring) {
        const monitoring = jsonData.dataMonitoring;
        
        // Data Management System
        if (monitoring.dataManagement) {
          const dm = monitoring.dataManagement;
          let content = `**Recommended System:** ${dm.recommendedSystem?.toUpperCase() || 'EDC'}\n`;
          
          if (dm.rationale) {
            content += `**Rationale:** ${dm.rationale}\n`;
          }
          
          if (dm.systemOptions) {
            if (dm.systemOptions.edc) {
              const edc = dm.systemOptions.edc;
              content += `\n**EDC System:**\n`;
              if (edc.advantages && edc.advantages.length > 0) {
                content += `Advantages:\n${edc.advantages.map((a: string) => `• ${a}`).join('\n')}\n`;
              }
              if (edc.considerations && edc.considerations.length > 0) {
                content += `Considerations:\n${edc.considerations.map((c: string) => `• ${c}`).join('\n')}\n`;
              }
            }
          }
          
          sections.push({
            title: 'Data Management System',
            content: content.trim(),
            type: 'recommendation',
            actionable: true,
            actionableData: {
              field: 'dataManagement',
              value: dm.recommendedSystem || 'edc'
            },
            citations: [],
            confidence: 0.90,
          });
        }
        
        // Monitoring Approach
        if (monitoring.monitoringApproach) {
          const approach = monitoring.monitoringApproach;
          let content = `**Recommended Approach:** ${approach.recommended?.replace('-', ' ').replace(/\b\w/g, (l: string) => l.toUpperCase()) || 'Risk-Based Monitoring'}\n`;
          
          if (approach.monitoringPlan) {
            const plan = approach.monitoringPlan;
            content += `\n**Monitoring Plan:**\n`;
            
            if (plan.onSite) {
              content += `\n**On-Site Monitoring:**\n`;
              content += `• Frequency: ${plan.onSite.frequency}\n`;
              content += `• Focus: ${plan.onSite.focus}\n`;
            }
            
            if (plan.remote) {
              content += `\n**Remote Monitoring:**\n`;
              content += `• Frequency: ${plan.remote.frequency}\n`;
              content += `• Focus: ${plan.remote.focus}\n`;
            }
            
            if (plan.centralized) {
              content += `\n**Centralized Monitoring:**\n`;
              content += `• Frequency: ${plan.centralized.frequency}\n`;
              content += `• Focus: ${plan.centralized.focus}\n`;
            }
          }
          
          if (approach.riskIndicators && approach.riskIndicators.length > 0) {
            content += `\n**Risk Indicators to Monitor:**\n${approach.riskIndicators.map((r: string) => `• ${r}`).join('\n')}`;
          }
          
          sections.push({
            title: 'Monitoring Approach',
            content: content.trim(),
            type: 'recommendation',
            actionable: true,
            actionableData: {
              field: 'monitoringApproach',
              value: approach.recommended || 'risk-based'
            },
            citations: [],
            confidence: 0.90,
          });
        }
        
        // Data Quality Metrics
        if (monitoring.dataQuality) {
          const quality = monitoring.dataQuality;
          let content = '';
          
          if (quality.targetMetrics) {
            const metrics = quality.targetMetrics;
            content += `**Target Metrics:**\n`;
            if (metrics.queryRate) content += `• Query Rate: ${metrics.queryRate}\n`;
            if (metrics.resolveTime) content += `• Query Resolution Time: ${metrics.resolveTime}\n`;
            if (metrics.completeness) content += `• Data Completeness: ${metrics.completeness}\n`;
          }
          
          if (quality.qualityControls && quality.qualityControls.length > 0) {
            content += `\n**Quality Control Measures:**\n${quality.qualityControls.map((c: string) => `• ${c}`).join('\n')}`;
          }
          
          sections.push({
            title: 'Data Quality Standards',
            content: content.trim(),
            type: 'guidance',
            citations: [],
            confidence: 0.85,
          });
        }
        
        // Technology Stack
        if (monitoring.technology) {
          const tech = monitoring.technology;
          let content = '';
          
          if (tech.recommended && tech.recommended.length > 0) {
            content += `**Recommended Systems:**\n${tech.recommended.map((t: string) => `• ${t}`).join('\n')}`;
          }
          
          if (tech.optional && tech.optional.length > 0) {
            content += `\n\n**Optional Enhancements:**\n${tech.optional.map((t: string) => `• ${t}`).join('\n')}`;
          }
          
          if (content) {
            sections.push({
              title: 'Technology Requirements',
              content: content.trim(),
              type: 'information',
              citations: [],
              confidence: 0.80,
            });
          }
        }
      }
      
      // Industry Benchmarks
      if (jsonData.industryBenchmarks && jsonData.industryBenchmarks.length > 0) {
        const validBenchmarks = jsonData.industryBenchmarks.filter((b: any) => 
          b.nctNumber && b.nctNumber.match(/NCT\d+/)
        );
        
        if (validBenchmarks.length > 0) {
          const benchmarkContent = validBenchmarks.map((b: any) => 
            `• **${b.nctNumber}**:\n` +
            `  System: ${b.dataSystem || 'N/A'}\n` +
            `  Monitoring: ${b.monitoringApproach || 'N/A'}\n` +
            `  ${b.outcomes ? `Outcomes: ${b.outcomes}` : ''}`
          ).join('\n\n');
          
          sections.push({
            title: 'Industry Benchmarks',
            content: benchmarkContent,
            type: 'evidence',
            citations: [], // Only use real citations from Bedrock
            confidence: 0.75,
          });
        }
      }
      
    } catch (error) {
      console.error('Failed to parse data monitoring JSON:', error);
      // Fallback to text parsing
      return this.parseResponseIntoSections(text, citations, 'data-monitoring');
    }
    
    console.log('Parsed sections count:', sections.length);
    console.log('===========================================\n');
    
    return sections;
  }
  
  private parseAECollectionResponse(text: string, citations: any[]): any[] {
    const sections = [];
    
    console.log('\n=== PARSING AE COLLECTION RESPONSE ===');
    console.log('Text length:', text.length);
    
    try {
      // Extract JSON from response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in AE collection response');
      }
      
      const jsonData = JSON.parse(jsonMatch[0]);
      console.log('Successfully parsed JSON for AE collection');
      
      // 1. Primary Recommendation
      if (jsonData.recommendation) {
        const rec = jsonData.recommendation;
        let content = `**Recommendation:** ${rec.collectAESAE ? 'Yes - Collect AEs/SAEs' : 'No - AE/SAE collection not required'}\n\n`;
        content += `**Rationale:** ${rec.rationale || ''}`;
        
        sections.push({
          title: 'AE/SAE Collection Recommendation',
          content: content,
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'willCollectAESAE',
            value: rec.collectAESAE ? 'yes' : 'no'
          },
          citations: [],
          confidence: 0.90,
        });
      }
      
      // 2. Monitoring Strategy
      if (jsonData.monitoringStrategy) {
        const strategy = jsonData.monitoringStrategy;
        let content = `**Frequency:** ${strategy.frequency || 'At each visit'}\n\n`;
        
        if (strategy.methods && strategy.methods.length > 0) {
          content += `**Collection Methods:**\n${strategy.methods.map((m: string) => `• ${m}`).join('\n')}\n\n`;
        }
        
        if (strategy.grading) {
          content += `**Grading System:** ${strategy.grading}\n\n`;
        }
        
        if (strategy.specialMonitoring) {
          content += `**Special Monitoring:** ${strategy.specialMonitoring}`;
        }
        
        sections.push({
          title: 'Monitoring Strategy',
          content: content.trim(),
          type: 'information',
          actionable: false,
          citations: [],
          confidence: 0.85,
        });
      }
      
      // 3. Regulatory Requirements
      if (jsonData.regulatoryRequirements) {
        const req = jsonData.regulatoryRequirements;
        let content = '';
        
        if (req.fdaRequirements) {
          content += `**FDA Requirements:** ${req.fdaRequirements}\n\n`;
        }
        
        if (req.expeditedReporting) {
          content += `**Expedited Reporting:** ${req.expeditedReporting}\n\n`;
        }
        
        if (req.dsmb) {
          content += `**DSMB:** ${req.dsmb}`;
        }
        
        if (content) {
          sections.push({
            title: 'Regulatory Requirements',
            content: content.trim(),
            type: 'details',
            actionable: false,
            citations: [],
            confidence: 0.85,
          });
        }
      }
      
      // 4. Expected Categories
      if (jsonData.expectedCategories) {
        const categories = jsonData.expectedCategories;
        let content = '';
        
        if (categories.systemOrganClass && categories.systemOrganClass.length > 0) {
          content += `**System Organ Classes:**\n${categories.systemOrganClass.map((s: string) => `• ${s}`).join('\n')}\n\n`;
        }
        
        if (categories.commonTerms && categories.commonTerms.length > 0) {
          content += `**Common AE Terms:**\n${categories.commonTerms.map((t: string) => `• ${t}`).join('\n')}\n\n`;
        }
        
        if (categories.specialInterest && categories.specialInterest.length > 0) {
          content += `**AEs of Special Interest:**\n${categories.specialInterest.map((s: string) => `• ${s}`).join('\n')}`;
        }
        
        if (content) {
          sections.push({
            title: 'Expected Adverse Events',
            content: content.trim(),
            type: 'information',
            actionable: false,
            citations: [],
            confidence: 0.80,
          });
        }
      }
      
      // 5. Benchmark Studies
      if (jsonData.benchmarkStudies && jsonData.benchmarkStudies.length > 0) {
        const validStudies = jsonData.benchmarkStudies.filter((s: any) => 
          s.nctNumber && s.nctNumber.match(/NCT\d+/i)
        );
        
        if (validStudies.length > 0) {
          const benchmarkContent = validStudies.map((s: any) => 
            `• **${s.nctNumber}**:\n` +
            `  Approach: ${s.safetyApproach || 'Not specified'}\n` +
            `  ${s.keyFindings ? `Key Findings: ${s.keyFindings}` : ''}`
          ).join('\n\n');
          
          sections.push({
            title: 'Similar Studies Reference',
            content: benchmarkContent,
            type: 'references',
            actionable: false,
            citations: [], // Only use real citations from Bedrock
            confidence: 0.75,
          });
        }
      }
      
    } catch (error) {
      console.log('Failed to parse AE collection JSON, falling back to text parsing:', error);
      return this.parseResponseIntoSections(text, citations, 'ae-collection');
    }
    
    console.log('Parsed sections count:', sections.length);
    console.log('===========================================\n');
    
    return sections;
  }
  
  private parseSafetyProfileResponse(text: string, citations: any[]): any[] {
    const sections = [];
    
    console.log('\n=== PARSING SAFETY PROFILE RESPONSE ===');
    console.log('Text length:', text.length);
    
    try {
      // Extract JSON from response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in safety profile response');
      }
      
      const jsonData = JSON.parse(jsonMatch[0]);
      console.log('Successfully parsed JSON for safety profile');
      
      // 1. Likely Side Effects (>10% incidence) - Updated to handle both old and new format
      if (jsonData.likelySideEffects) {
        let effectsList: string[] = [];
        let effectsDetails: any[] = [];
        
        // Handle new format with nested structure
        if (jsonData.likelySideEffects.effects && Array.isArray(jsonData.likelySideEffects.effects)) {
          effectsList = jsonData.likelySideEffects.effects;
          effectsDetails = effectsList.map(effect => ({ effect }));
        } 
        // Handle old format with direct array
        else if (Array.isArray(jsonData.likelySideEffects)) {
          effectsDetails = jsonData.likelySideEffects;
          effectsList = jsonData.likelySideEffects.map((se: any) => 
            typeof se === 'string' ? se : se.effect
          );
        }
        
        if (effectsList.length > 0) {
          // Build content string
          const likelyContent = effectsDetails.map((se: any) => {
            if (typeof se === 'string') {
              return `• ${se}`;
            } else {
              return `• **${se.effect}**${se.expectedIncidence ? `: ${se.expectedIncidence}` : ''}${se.management ? `\n  ${se.management}` : ''}`;
            }
          }).join('\n\n');
          
          sections.push({
            title: 'Likely Side Effects (Common, ≥10%)',
            content: likelyContent,
            type: 'recommendation',
            actionable: true,
            actionableData: {
              field: 'likelySideEffects',
              value: effectsList
            },
            criteriaItems: effectsList.map(effect => ({
              text: effect,
              actionableData: {
                field: 'singleLikelySideEffect',
                value: effect,
                category: 'common'
              }
            })),
            citations: [],
            confidence: 0.85,
          });
        }
      }
      
      // 2. Less Likely Side Effects (1-10% incidence)
      if (jsonData.lessLikelySideEffects) {
        let effectsList: string[] = [];
        let effectsDetails: any[] = [];
        
        // Handle new format with nested structure
        if (jsonData.lessLikelySideEffects.effects && Array.isArray(jsonData.lessLikelySideEffects.effects)) {
          effectsList = jsonData.lessLikelySideEffects.effects;
          effectsDetails = effectsList.map(effect => ({ effect }));
        }
        // Handle old format with direct array
        else if (Array.isArray(jsonData.lessLikelySideEffects)) {
          effectsDetails = jsonData.lessLikelySideEffects;
          effectsList = jsonData.lessLikelySideEffects.map((se: any) => 
            typeof se === 'string' ? se : se.effect
          );
        }
        
        if (effectsList.length > 0) {
          // Build content string
          const lessLikelyContent = effectsDetails.map((se: any) => {
            if (typeof se === 'string') {
              return `• ${se}`;
            } else {
              return `• **${se.effect}**${se.expectedIncidence ? `: ${se.expectedIncidence}` : ''}${se.management ? `\n  ${se.management}` : ''}`;
            }
          }).join('\n\n');
          
          sections.push({
            title: 'Less Likely Side Effects (Occasional, 1-10%)',
            content: lessLikelyContent,
            type: 'recommendation',
            actionable: true,
            actionableData: {
              field: 'lessLikelySideEffects',
              value: effectsList
            },
            criteriaItems: effectsList.map(effect => ({
              text: effect,
              actionableData: {
                field: 'singleLessLikelySideEffect',
                value: effect,
                category: 'occasional'
              }
            })),
            citations: [],
            confidence: 0.80,
          });
        }
      }
      
      // 3. Rare but Serious Side Effects (<1% but medically significant)
      if (jsonData.rareButSeriousSideEffects || jsonData.rareSeriousSideEffects) {
        const rareData = jsonData.rareButSeriousSideEffects || jsonData.rareSeriousSideEffects;
        let effectsList: string[] = [];
        let effectsDetails: any[] = [];
        
        // Handle new format with nested structure
        if (rareData.effects && Array.isArray(rareData.effects)) {
          effectsList = rareData.effects;
          effectsDetails = effectsList.map(effect => ({ effect }));
        }
        // Handle old format with direct array
        else if (Array.isArray(rareData)) {
          effectsDetails = rareData;
          effectsList = rareData.map((se: any) => 
            typeof se === 'string' ? se : se.effect
          );
        }
        
        if (effectsList.length > 0) {
          // Build content string
          const rareContent = effectsDetails.map((se: any) => {
            if (typeof se === 'string') {
              return `• ${se}`;
            } else {
              return `• **${se.effect}**${se.expectedIncidence ? `: ${se.expectedIncidence}` : ''}${se.severity ? `\n  Severity: ${se.severity}` : ''}${se.monitoringRequired ? `\n  ${se.monitoringRequired}` : ''}`;
            }
          }).join('\n\n');
          
          sections.push({
            title: 'Rare but Serious Side Effects (<1%)',
            content: rareContent,
            type: 'recommendation',
            actionable: true,
            actionableData: {
              field: 'rareSeriousSideEffects',
              value: effectsList
            },
            criteriaItems: effectsList.map(effect => ({
              text: effect,
              actionableData: {
                field: 'singleRareSeriousSideEffect',
                value: effect,
                category: 'serious'
              }
            })),
            citations: [],
            confidence: 0.85,
          });
        }
      }
      
      // 4. Class Effects (informational)
      if (jsonData.classEffects) {
        const classEffects = jsonData.classEffects;
        let content = '';
        
        if (classEffects.knownClassEffects && classEffects.knownClassEffects.length > 0) {
          content += `**Known Class Effects:**\n${classEffects.knownClassEffects.map((e: string) => `• ${e}`).join('\n')}\n\n`;
        }
        
        if (classEffects.mechanismRelated && classEffects.mechanismRelated.length > 0) {
          content += `**Mechanism-Related Effects:**\n${classEffects.mechanismRelated.map((e: string) => `• ${e}`).join('\n')}\n\n`;
        }
        
        if (classEffects.offTargetEffects && classEffects.offTargetEffects.length > 0) {
          content += `**Off-Target Effects:**\n${classEffects.offTargetEffects.map((e: string) => `• ${e}`).join('\n')}`;
        }
        
        if (content) {
          sections.push({
            title: 'Drug Class Effects',
            content: content.trim(),
            type: 'information',
            actionable: false,
            citations: [],
            confidence: 0.80,
          });
        }
      }
      
      // 5. Demographic Considerations (informational)
      if (jsonData.demographicConsiderations) {
        const demo = jsonData.demographicConsiderations;
        let content = '';
        
        if (demo.elderly) {
          content += `**Elderly Patients:** ${demo.elderly}\n\n`;
        }
        if (demo.pediatric) {
          content += `**Pediatric Patients:** ${demo.pediatric}\n\n`;
        }
        if (demo.renalImpairment) {
          content += `**Renal Impairment:** ${demo.renalImpairment}\n\n`;
        }
        if (demo.hepaticImpairment) {
          content += `**Hepatic Impairment:** ${demo.hepaticImpairment}`;
        }
        
        if (content) {
          sections.push({
            title: 'Special Population Considerations',
            content: content.trim(),
            type: 'information',
            actionable: false,
            citations: [],
            confidence: 0.75,
          });
        }
      }
      
      // 6. Benchmark Studies
      if (jsonData.benchmarkStudies && jsonData.benchmarkStudies.length > 0) {
        const validStudies = jsonData.benchmarkStudies.filter((s: any) => 
          s.nctNumber && s.nctNumber.match(/NCT\d+/i)
        );
        
        if (validStudies.length > 0) {
          const benchmarkContent = validStudies.map((s: any) => 
            `• **${s.nctNumber}**:\n` +
            `  Drug: ${s.drugName || 'Not specified'}\n` +
            `  Key Findings: ${s.keyFindings || 'Not specified'}\n` +
            `  Discontinuation Rate: ${s.discontinuationRate || 'Not reported'}`
          ).join('\n\n');
          
          sections.push({
            title: 'Safety Data from Similar Trials',
            content: benchmarkContent,
            type: 'references',
            actionable: false,
            citations: validStudies.map((s: any) => ({
              nctNumber: s.nctNumber,
              title: 'Safety Profile Reference',
              url: `s3://trialynx-clinical-trials-gov/text-documents/${s.nctNumber.substring(0,6)}/${s.nctNumber}.txt`
            })),
            confidence: 0.75,
          });
        }
      }
      
    } catch (error) {
      console.log('Failed to parse safety profile JSON, falling back to text parsing:', error);
      return this.parseResponseIntoSections(text, citations, 'safety-profile');
    }
    
    console.log('Parsed sections count:', sections.length);
    console.log('===========================================\n');
    
    return sections;
  }
  
  private parseReproductiveRisksResponse(text: string, citations: any[]): any[] {
    const sections = [];
    
    console.log('\n=== PARSING REPRODUCTIVE RISKS RESPONSE ===');
    console.log('Text length:', text.length);
    
    try {
      // Extract JSON from response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in reproductive risks response');
      }
      
      const jsonData = JSON.parse(jsonMatch[0]);
      console.log('Successfully parsed JSON for reproductive risks');
      
      // 1. Primary Risk Assessment (Yes/No)
      if (jsonData.hasReproductiveRisks) {
        const risks = jsonData.hasReproductiveRisks;
        let content = `**Assessment:** ${risks.assessment ? 'Yes - Reproductive risks identified' : 'No - No reproductive risks identified'}\n\n`;
        
        if (risks.pregnancyCategory) {
          content += `**Pregnancy Category:** ${risks.pregnancyCategory}\n\n`;
        }
        
        if (risks.rationale) {
          content += `**Rationale:** ${risks.rationale}`;
        }
        
        sections.push({
          title: 'Reproductive Risk Assessment',
          content: content,
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'hasReproductiveRisks',
            value: risks.assessment ? 'yes' : 'no'
          },
          citations: [],
          confidence: 0.90,
        });
      }
      
      // 2. Detailed Risk Information (for text field)
      if (jsonData.reproductiveRiskDetails) {
        const details = jsonData.reproductiveRiskDetails;
        let detailsText = '';
        
        // Build comprehensive details text
        if (details.animalData) {
          detailsText += `ANIMAL DATA:\n${details.animalData}\n\n`;
        }
        
        if (details.humanData) {
          detailsText += `HUMAN DATA:\n${details.humanData}\n\n`;
        }
        
        if (details.teratogenicity) {
          detailsText += `TERATOGENICITY RISK:\n${details.teratogenicity}\n\n`;
        }
        
        if (details.fertilityEffects) {
          detailsText += `FERTILITY EFFECTS:\n${details.fertilityEffects}\n\n`;
        }
        
        if (details.lactationRisk) {
          detailsText += `LACTATION RISK:\n${details.lactationRisk}\n\n`;
        }
        
        if (details.mechanismConcerns) {
          detailsText += `MECHANISM-BASED CONCERNS:\n${details.mechanismConcerns}`;
        }
        
        if (detailsText) {
          sections.push({
            title: 'Detailed Risk Profile',
            content: detailsText.trim(),
            type: 'details',
            actionable: true,
            actionableData: {
              field: 'reproductiveRisksDetails',
              value: detailsText.trim()
            },
            citations: [],
            confidence: 0.85,
          });
        }
      }
      
      // 3. Risk Mitigation Strategies
      if (jsonData.riskMitigation) {
        const mitigation = jsonData.riskMitigation;
        let mitigationContent = '';
        
        // Contraception Requirements
        if (mitigation.contraceptionRequirements) {
          const cr = mitigation.contraceptionRequirements;
          mitigationContent += '**CONTRACEPTION REQUIREMENTS:**\n';
          
          if (cr.females) {
            mitigationContent += `• Females: ${cr.females}\n`;
          }
          if (cr.males) {
            mitigationContent += `• Males: ${cr.males}\n`;
          }
          if (cr.duration) {
            mitigationContent += `• Duration: ${cr.duration}\n`;
          }
          mitigationContent += '\n';
        }
        
        // Pregnancy Testing
        if (mitigation.pregnancyTesting) {
          const pt = mitigation.pregnancyTesting;
          mitigationContent += '**PREGNANCY TESTING:**\n';
          
          if (pt.frequency) {
            mitigationContent += `• Frequency: ${pt.frequency}\n`;
          }
          if (pt.type) {
            mitigationContent += `• Type: ${pt.type}\n`;
          }
          if (pt.timing) {
            mitigationContent += `• Timing: ${pt.timing}\n`;
          }
          mitigationContent += '\n';
        }
        
        // Counseling and Registry
        if (mitigation.counseling) {
          mitigationContent += `**COUNSELING:** ${mitigation.counseling}\n\n`;
        }
        
        if (mitigation.registry) {
          mitigationContent += `**PREGNANCY REGISTRY:** ${mitigation.registry}\n`;
        }
        
        if (mitigationContent) {
          // Create the full mitigation text for the details field
          const fullMitigationText = mitigationContent.trim();
          
          sections.push({
            title: 'Risk Mitigation Strategies',
            content: fullMitigationText,
            type: 'recommendation',
            actionable: true,
            actionableData: {
              field: 'reproductiveRisksDetails',
              value: fullMitigationText
            },
            citations: [],
            confidence: 0.85,
          });
        }
      }
      
      // 4. Regulatory Guidance
      if (jsonData.regulatoryGuidance) {
        const guidance = jsonData.regulatoryGuidance;
        let guidanceContent = '';
        
        if (guidance.fdaRequirements) {
          guidanceContent += `**FDA Requirements:** ${guidance.fdaRequirements}\n\n`;
        }
        
        if (guidance.ichGuidelines) {
          guidanceContent += `**ICH Guidelines:** ${guidance.ichGuidelines}\n\n`;
        }
        
        if (guidance.rems) {
          guidanceContent += `**REMS:** ${guidance.rems}`;
        }
        
        if (guidanceContent) {
          sections.push({
            title: 'Regulatory Guidance',
            content: guidanceContent.trim(),
            type: 'information',
            actionable: false,
            citations: [],
            confidence: 0.80,
          });
        }
      }
      
      // 5. Comparator Drug Data
      if (jsonData.comparatorData && jsonData.comparatorData.length > 0) {
        const comparatorContent = jsonData.comparatorData.map((d: any) => 
          `• **${d.drugName}**: Category ${d.pregnancyCategory || 'Not specified'}\n` +
          `  ${d.keyFindings || 'No specific findings reported'}`
        ).join('\n\n');
        
        sections.push({
          title: 'Similar Drug Reproductive Data',
          content: comparatorContent,
          type: 'information',
          actionable: false,
          citations: [],
          confidence: 0.75,
        });
      }
      
      // 6. Benchmark Studies
      if (jsonData.benchmarkStudies && jsonData.benchmarkStudies.length > 0) {
        const validStudies = jsonData.benchmarkStudies.filter((s: any) => 
          s.nctNumber && s.nctNumber.match(/NCT\d+/i)
        );
        
        if (validStudies.length > 0) {
          const benchmarkContent = validStudies.map((s: any) => 
            `• **${s.nctNumber}**:\n` +
            `  Strategy: ${s.reproductiveStrategy || 'Not specified'}\n` +
            `  Exclusions: ${s.exclusions || 'Standard reproductive exclusions'}`
          ).join('\n\n');
          
          sections.push({
            title: 'Reproductive Risk Management in Similar Trials',
            content: benchmarkContent,
            type: 'references',
            actionable: false,
            citations: validStudies.map((s: any) => ({
              nctNumber: s.nctNumber,
              title: 'Reproductive Risk Management',
              url: `s3://trialynx-clinical-trials-gov/text-documents/${s.nctNumber.substring(0,6)}/${s.nctNumber}.txt`
            })),
            confidence: 0.75,
          });
        }
      }
      
    } catch (error) {
      console.log('Failed to parse reproductive risks JSON, falling back to text parsing:', error);
      return this.parseResponseIntoSections(text, citations, 'reproductive-risks');
    }
    
    console.log('Parsed sections count:', sections.length);
    console.log('===========================================\n');
    
    return sections;
  }
  
  private parseStudyTimelineResponse(text: string, citations: any[]): any[] {
    const sections = [];
    
    console.log('\n=== PARSING STUDY TIMELINE RESPONSE ===');
    console.log('Text length:', text.length);
    
    try {
      // Extract JSON from response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in study timeline response');
      }
      
      const jsonData = JSON.parse(jsonMatch[0]);
      console.log('Successfully parsed JSON for study timeline');
      
      // 1. Timeline Recommendations
      if (jsonData.timeline) {
        const timeline = jsonData.timeline;
        let timelineContent = '';
        
        if (timeline.screeningPeriod) {
          timelineContent += `**Screening Period:** ${timeline.screeningPeriod.duration}\n`;
          if (timeline.screeningPeriod.rationale) {
            timelineContent += `*Rationale:* ${timeline.screeningPeriod.rationale}\n\n`;
          }
        }
        
        if (timeline.baselinePeriod) {
          timelineContent += `**Baseline Period:** ${timeline.baselinePeriod.duration}\n`;
          if (timeline.baselinePeriod.rationale) {
            timelineContent += `*Rationale:* ${timeline.baselinePeriod.rationale}\n\n`;
          }
        }
        
        if (timeline.treatmentPeriod) {
          timelineContent += `**Treatment Period:** ${timeline.treatmentPeriod.duration}\n`;
          if (timeline.treatmentPeriod.rationale) {
            timelineContent += `*Rationale:* ${timeline.treatmentPeriod.rationale}\n\n`;
          }
        }
        
        if (timeline.followUpPeriod) {
          timelineContent += `**Follow-up Period:** ${timeline.followUpPeriod.duration}\n`;
          if (timeline.followUpPeriod.rationale) {
            timelineContent += `*Rationale:* ${timeline.followUpPeriod.rationale}\n\n`;
          }
        }
        
        if (timeline.totalDuration) {
          timelineContent += `**Total Study Duration:** ${timeline.totalDuration}\n\n`;
        }
        
        if (timeline.durationWithDates) {
          timelineContent += `**Expected Timeline:** ${timeline.durationWithDates}`;
        }
        
        sections.push({
          title: 'Recommended Study Timeline',
          content: timelineContent.trim(),
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'timeline',
            screeningPeriod: timeline.screeningPeriod?.duration,
            baselinePeriod: timeline.baselinePeriod?.duration,
            treatmentPeriod: timeline.treatmentPeriod?.duration,
            followUpPeriod: timeline.followUpPeriod?.duration,
            totalDuration: timeline.totalDuration,
            durationWithDates: timeline.durationWithDates
          },
          citations: [],
          confidence: 0.85,
        });
      }
      
      // 2. Benchmark Studies
      if (jsonData.benchmarkStudies && jsonData.benchmarkStudies.length > 0) {
        const benchmarkContent = jsonData.benchmarkStudies.map((study: any) => {
          let content = `**${study.nctNumber}**\n`;
          if (study.screeningDuration) {
            content += `• Screening: ${study.screeningDuration}\n`;
          }
          if (study.treatmentDuration) {
            content += `• Treatment: ${study.treatmentDuration}\n`;
          }
          if (study.followUpDuration) {
            content += `• Follow-up: ${study.followUpDuration}\n`;
          }
          if (study.totalDuration) {
            content += `• Total Duration: ${study.totalDuration}`;
          }
          return content;
        }).join('\n\n');
        
        sections.push({
          title: 'Benchmark Studies',
          content: benchmarkContent,
          type: 'references',
          actionable: false,
          citations: [], // Only use real citations from Bedrock
          confidence: 0.8,
        });
      }
      
    } catch (error) {
      console.log('Failed to parse JSON, falling back to text parsing:', error);
      return this.parseResponseIntoSections(text, citations, 'study-timeline');
    }
    
    console.log('===========================================\n');
    
    return sections;
  }
  
  private parseVisitScheduleResponse(text: string, citations: any[]): any[] {
    const sections = [];
    
    console.log('\n=== PARSING VISIT SCHEDULE RESPONSE ===');
    console.log('Text length:', text.length);
    
    try {
      // Extract JSON from response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in visit schedule response');
      }
      
      const jsonData = JSON.parse(jsonMatch[0]);
      console.log('Successfully parsed JSON for visit schedule');
      
      // 1. Visit Schedule with individual selection
      if (jsonData.visitSchedule && jsonData.visitSchedule.length > 0) {
        // Create individual visit items for selection
        const visitItems = jsonData.visitSchedule.map((visit: any) => {
          let visitText = `**${visit.visitName}** (${visit.timepoint})`;
          if (visit.critical) {
            visitText += ' - **CRITICAL**';
          }
          visitText += `\nProcedures: ${visit.procedures.join(', ')}`;
          if (visit.rationale) {
            visitText += `\n*${visit.rationale}*`;
          }
          
          return {
            text: visitText,
            actionableData: {
              field: 'singleVisit',
              value: {
                name: visit.visitName,
                timepoint: visit.timepoint,
                procedures: visit.procedures,
                critical: visit.critical || false
              }
            }
          };
        });
        
        sections.push({
          title: 'Recommended Visit Schedule',
          content: `Based on similar trials, consider the following visit schedule:`,
          type: 'recommendation',
          actionable: true,
          criteriaItems: visitItems,
          actionableData: {
            field: 'visits',
            visits: jsonData.visitSchedule.map((v: any) => ({
              name: v.visitName,
              timepoint: v.timepoint,
              procedures: v.procedures,
              critical: v.critical || false
            }))
          },
          citations: [],
          confidence: 0.85,
        });
      }
      
      // 2. Visit Frequency
      if (jsonData.visitFrequency) {
        const freq = jsonData.visitFrequency;
        let freqContent = '';
        
        if (freq.duringTreatment) {
          freqContent += `**During Treatment:** ${freq.duringTreatment}\n`;
        }
        if (freq.duringFollowUp) {
          freqContent += `**During Follow-up:** ${freq.duringFollowUp}\n`;
        }
        if (freq.rationale) {
          freqContent += `\n*Rationale:* ${freq.rationale}`;
        }
        
        sections.push({
          title: 'Visit Frequency Recommendations',
          content: freqContent.trim(),
          type: 'information',
          actionable: false,
          citations: [],
          confidence: 0.80,
        });
      }
      
      // 3. Benchmark Studies
      if (jsonData.benchmarkStudies && jsonData.benchmarkStudies.length > 0) {
        const benchmarkContent = jsonData.benchmarkStudies.map((study: any) => {
          let content = `**${study.nctNumber}**\n`;
          if (study.visitCount) {
            content += `• Total Visits: ${study.visitCount}\n`;
          }
          if (study.visitFrequency) {
            content += `• Visit Frequency: ${study.visitFrequency}\n`;
          }
          if (study.keyProcedures) {
            content += `• Key Procedures: ${study.keyProcedures}`;
          }
          return content;
        }).join('\n\n');
        
        sections.push({
          title: 'Benchmark Studies',
          content: benchmarkContent,
          type: 'references',
          actionable: false,
          citations: [], // Only use real citations from Bedrock
          confidence: 0.75,
        });
      }
      
    } catch (error) {
      console.log('Failed to parse JSON, falling back to text parsing:', error);
      return this.parseResponseIntoSections(text, citations, 'visit-schedule');
    }
    
    console.log('===========================================\n');
    
    return sections;
  }
  
  private parseInterventionDetailsResponse(text: string, citations: any[]): any[] {
    const sections = [];
    
    console.log('\n=== PARSING INTERVENTION DETAILS RESPONSE ===');
    console.log('Text length:', text.length);
    
    try {
      // Extract JSON from response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in intervention details response');
      }
      
      const jsonData = JSON.parse(jsonMatch[0]);
      console.log('Successfully parsed JSON for intervention details');
      
      // 1. Intervention Details
      if (jsonData.interventionDetails) {
        const details = jsonData.interventionDetails;
        let interventionText = '';
        
        // Dosing information
        if (details.dosing) {
          interventionText += `**Dosing Recommendation:**\n`;
          interventionText += `${details.dosing.recommendation}\n`;
          if (details.dosing.rationale) {
            interventionText += `*Rationale: ${details.dosing.rationale}*\n`;
          }
          if (details.dosing.alternatives && details.dosing.alternatives.length > 0) {
            interventionText += `\n**Alternative Dosing Options:**\n`;
            interventionText += details.dosing.alternatives.map((alt: string) => `• ${alt}`).join('\n');
            interventionText += '\n';
          }
        }
        
        // Administration details
        if (details.administration) {
          interventionText += `\n**Administration:**\n`;
          if (details.administration.route) {
            interventionText += `• Route: ${details.administration.route}\n`;
          }
          if (details.administration.frequency) {
            interventionText += `• Frequency: ${details.administration.frequency}\n`;
          }
          if (details.administration.duration) {
            interventionText += `• Duration: ${details.administration.duration}\n`;
          }
        }
        
        // Packaging and supply
        if (details.packaging || details.supplyChain || details.storageRequirements) {
          interventionText += `\n**Supply & Storage:**\n`;
          if (details.packaging) {
            interventionText += `• Packaging: ${details.packaging}\n`;
          }
          if (details.supplyChain) {
            interventionText += `• Supply Chain: ${details.supplyChain}\n`;
          }
          if (details.storageRequirements) {
            interventionText += `• Storage: ${details.storageRequirements}\n`;
          }
        }
        
        sections.push({
          title: 'Trial Intervention Recommendations',
          content: interventionText.trim(),
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'trialInterventionDetails',
            value: interventionText.trim()
          },
          citations: [],
          confidence: 0.85,
        });
      }
      
      // 2. Study Events
      if (jsonData.studyEvents) {
        const events = jsonData.studyEvents;
        let eventsText = '';
        
        // Key Milestones
        if (events.keyMilestones && events.keyMilestones.length > 0) {
          eventsText += `**Key Study Milestones:**\n`;
          events.keyMilestones.forEach((milestone: any) => {
            eventsText += `\n• **${milestone.event}** (${milestone.targetTiming})\n`;
            if (milestone.description) {
              eventsText += `  ${milestone.description}\n`;
            }
          });
        }
        
        // Critical Assessments
        if (events.criticalAssessments && events.criticalAssessments.length > 0) {
          eventsText += `\n**Critical Assessments:**\n`;
          eventsText += events.criticalAssessments.map((assessment: string) => `• ${assessment}`).join('\n');
          eventsText += '\n';
        }
        
        // Operational Events
        if (events.operationalEvents && events.operationalEvents.length > 0) {
          eventsText += `\n**Operational Events:**\n`;
          eventsText += events.operationalEvents.map((event: string) => `• ${event}`).join('\n');
        }
        
        sections.push({
          title: 'Study Events & Activities',
          content: eventsText.trim(),
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'studyEventsAndActivities',
            value: eventsText.trim()
          },
          citations: [],
          confidence: 0.85,
        });
      }
      
      // 3. Benchmark Studies
      if (jsonData.benchmarkStudies && jsonData.benchmarkStudies.length > 0) {
        const benchmarkContent = jsonData.benchmarkStudies.map((study: any) => {
          let content = `**${study.nctNumber}**\n`;
          if (study.interventionApproach) {
            content += `• Intervention: ${study.interventionApproach}\n`;
          }
          if (study.keyEvents) {
            content += `• Key Events: ${study.keyEvents}`;
          }
          return content;
        }).join('\n\n');
        
        sections.push({
          title: 'Benchmark Studies',
          content: benchmarkContent,
          type: 'references',
          actionable: false,
          citations: [], // Only use real citations from Bedrock
          confidence: 0.75,
        });
      }
      
    } catch (error) {
      console.log('Failed to parse JSON, falling back to text parsing:', error);
      return this.parseResponseIntoSections(text, citations, 'intervention-details');
    }
    
    console.log('Parsed sections count:', sections.length);
    console.log('===========================================\n');
    
    return sections;
  }
  
  private parseLaboratoryStudiesResponse(text: string, citations: any[]): any[] {
    const sections = [];
    
    console.log('\n=== PARSING LABORATORY STUDIES RESPONSE ===');
    console.log('Text length:', text.length);
    
    try {
      // Extract JSON from response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in laboratory studies response');
      }
      
      const jsonData = JSON.parse(jsonMatch[0]);
      console.log('Successfully parsed JSON for laboratory studies');
      
      // 1. Specimen Collection with individual selection
      if (jsonData.specimenCollection) {
        const spec = jsonData.specimenCollection;
        
        if (spec.recommendedSpecimens && spec.recommendedSpecimens.length > 0) {
          // Create individual specimen items for selection
          const specimenItems = spec.recommendedSpecimens.map((specimen: any) => {
            let text = `**${specimen.type}**`;
            if (specimen.volume) {
              text += ` - Volume: ${specimen.volume}`;
            }
            if (specimen.frequency) {
              text += ` - Frequency: ${specimen.frequency}`;
            }
            if (specimen.rationale) {
              text += `\n*${specimen.rationale}*`;
            }
            
            return {
              text: text,
              actionableData: {
                field: 'singleSpecimen',
                value: specimen.type
              }
            };
          });
          
          let headerContent = '';
          if (spec.totalBloodVolume) {
            headerContent += `**Total Blood Volume:** ${spec.totalBloodVolume}\n`;
          }
          if (spec.processingRequirements) {
            headerContent += `**Processing Requirements:** ${spec.processingRequirements}`;
          }
          
          sections.push({
            title: 'Recommended Biological Specimens',
            content: headerContent || 'Select the specimens to collect:',
            type: 'recommendation',
            actionable: true,
            criteriaItems: specimenItems,
            actionableData: {
              field: 'biologicalSpecimens',
              specimens: spec.recommendedSpecimens.map((s: any) => s.type),
              collectionAndProcessing: spec.processingRequirements || ''
            },
            citations: [],
            confidence: 0.85,
          });
        }
      }
      
      // 2. Specialized Studies with individual selection
      if (jsonData.specializedStudies) {
        const studies = jsonData.specializedStudies;
        const studyItems = [];
        const studyRecommendations: any = {};
        
        // Pharmacokinetics
        if (studies.pharmacokinetics) {
          const pk = studies.pharmacokinetics;
          let pkText = `**Pharmacokinetics (PK):** ${pk.recommended ? '✓ Recommended' : 'Not Recommended'}`;
          if (pk.samplingSchedule) {
            pkText += `\n• Sampling: ${pk.samplingSchedule}`;
          }
          if (pk.rationale) {
            pkText += `\n• *${pk.rationale}*`;
          }
          
          studyItems.push({
            text: pkText,
            actionableData: {
              willConductPK: pk.recommended ? 'Yes' : 'No'
            }
          });
          studyRecommendations.willConductPK = pk.recommended ? 'Yes' : 'No';
        }
        
        // Biomarkers
        if (studies.biomarkers) {
          const bio = studies.biomarkers;
          let bioText = `**Biomarker Testing:** ${bio.recommended ? '✓ Recommended' : 'Not Recommended'}`;
          if (bio.types && bio.types.length > 0) {
            bioText += `\n• Types: ${bio.types.join(', ')}`;
          }
          if (bio.rationale) {
            bioText += `\n• *${bio.rationale}*`;
          }
          
          studyItems.push({
            text: bioText,
            actionableData: {
              willConductBiomarker: bio.recommended ? 'Yes' : 'No'
            }
          });
          studyRecommendations.willConductBiomarker = bio.recommended ? 'Yes' : 'No';
        }
        
        // Immunogenicity
        if (studies.immunogenicity) {
          const imm = studies.immunogenicity;
          let immText = `**Immunogenicity Testing:** ${imm.recommended ? '✓ Recommended' : 'Not Recommended'}`;
          if (imm.frequency) {
            immText += `\n• Frequency: ${imm.frequency}`;
          }
          if (imm.rationale) {
            immText += `\n• *${imm.rationale}*`;
          }
          
          studyItems.push({
            text: immText,
            actionableData: {
              willConductImmunogenicity: imm.recommended ? 'Yes' : 'No'
            }
          });
          studyRecommendations.willConductImmunogenicity = imm.recommended ? 'Yes' : 'No';
        }
        
        // Genetic Testing
        if (studies.geneticTesting) {
          const gen = studies.geneticTesting;
          let genText = `**Genetic Testing:** ${gen.recommended ? '✓ Recommended' : 'Not Recommended'}`;
          if (gen.purpose) {
            genText += `\n• Purpose: ${gen.purpose}`;
          }
          if (gen.rationale) {
            genText += `\n• *${gen.rationale}*`;
          }
          
          studyItems.push({
            text: genText,
            actionableData: {
              willConductGeneticTesting: gen.recommended ? 'Yes' : 'No'
            }
          });
          studyRecommendations.willConductGeneticTesting = gen.recommended ? 'Yes' : 'No';
        }
        
        sections.push({
          title: 'Specialized Laboratory Studies',
          content: 'Select the specialized studies to conduct:',
          type: 'recommendation',
          actionable: true,
          criteriaItems: studyItems,
          actionableData: studyRecommendations,
          citations: [],
          confidence: 0.85,
        });
      }
      
      // 3. Benchmark Studies
      if (jsonData.benchmarkStudies && jsonData.benchmarkStudies.length > 0) {
        const benchmarkContent = jsonData.benchmarkStudies.map((study: any) => {
          let content = `**${study.nctNumber}**\n`;
          if (study.specimensCollected) {
            content += `• Specimens: ${study.specimensCollected}\n`;
          }
          if (study.specializedTests) {
            content += `• Specialized Tests: ${study.specializedTests}`;
          }
          return content;
        }).join('\n\n');
        
        sections.push({
          title: 'Benchmark Studies',
          content: benchmarkContent,
          type: 'references',
          actionable: false,
          citations: [], // Only use real citations from Bedrock
          confidence: 0.8,
        });
      }
      
    } catch (error) {
      console.log('Failed to parse JSON, falling back to text parsing:', error);
      return this.parseResponseIntoSections(text, citations, 'laboratory-studies');
    }
    
    console.log('===========================================\n');
    
    return sections;
  }
  
  private parseOperationalStrategyResponse(text: string, citations: any[]): any[] {
    const sections = [];
    
    console.log('\n=== PARSING OPERATIONAL STRATEGY RESPONSE ===');
    console.log('Text length:', text.length);
    
    try {
      // Extract JSON from response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in operational strategy response');
      }
      
      const jsonData = JSON.parse(jsonMatch[0]);
      console.log('Successfully parsed JSON for operational strategy');
      
      // 1. Recruitment Strategy
      if (jsonData.recruitmentStrategy) {
        const recruit = jsonData.recruitmentStrategy;
        let recruitContent = '';
        const recruitData: any = {};
        
        if (recruit.expectedRate) {
          recruitContent += `**Expected Recruitment Rate:** ${recruit.expectedRate.value}`;
          if (recruit.expectedRate.range) {
            recruitContent += ` (Range: ${recruit.expectedRate.range.min} - ${recruit.expectedRate.range.max})`;
          }
          recruitContent += '\n';
          if (recruit.expectedRate.rationale) {
            recruitContent += `*${recruit.expectedRate.rationale}*\n`;
          }
          recruitContent += '\n';
          recruitData.recruitmentRate = recruit.expectedRate.value;
        }
        
        if (recruit.screenFailureRate) {
          recruitContent += `**Screen Failure Rate:** ${recruit.screenFailureRate.value}`;
          if (recruit.screenFailureRate.range) {
            recruitContent += ` (Range: ${recruit.screenFailureRate.range.min} - ${recruit.screenFailureRate.range.max})`;
          }
          recruitContent += '\n';
          if (recruit.screenFailureRate.rationale) {
            recruitContent += `*${recruit.screenFailureRate.rationale}*\n`;
          }
          recruitContent += '\n';
          recruitData.screenFailureRate = recruit.screenFailureRate.value;
        }
        
        if (recruit.dropoutRate) {
          recruitContent += `**Expected Dropout Rate:** ${recruit.dropoutRate.value}`;
          if (recruit.dropoutRate.range) {
            recruitContent += ` (Range: ${recruit.dropoutRate.range.min} - ${recruit.dropoutRate.range.max})`;
          }
          recruitContent += '\n';
          if (recruit.dropoutRate.rationale) {
            recruitContent += `*${recruit.dropoutRate.rationale}*\n`;
          }
          recruitContent += '\n';
          recruitData.dropoutRate = recruit.dropoutRate.value;
        }
        
        if (recruit.overage) {
          recruitContent += `**Recommended Enrollment Overage:** ${recruit.overage}`;
        }
        
        sections.push({
          title: 'Recruitment Strategy',
          content: recruitContent.trim(),
          type: 'recommendation',
          actionable: true,
          actionableData: recruitData,
          citations: [],
          confidence: 0.85,
        });
      }
      
      // 2. Data Management
      if (jsonData.dataManagement) {
        const dm = jsonData.dataManagement;
        let dmContent = `**Recommended System:** ${dm.recommendedSystem}\n`;
        
        if (dm.specificPlatforms && dm.specificPlatforms.length > 0) {
          dmContent += `**Platform Options:** ${dm.specificPlatforms.join(', ')}\n`;
        }
        
        if (dm.rationale) {
          dmContent += `\n*${dm.rationale}*`;
        }
        
        sections.push({
          title: 'Data Management System',
          content: dmContent,
          type: 'recommendation',
          actionable: true,
          actionableData: {
            field: 'edcCTMSName',
            value: dm.specificPlatforms && dm.specificPlatforms.length > 0 ? dm.specificPlatforms[0] : '',
            dataManagementSystem: dm.recommendedSystem.toLowerCase()
          },
          citations: [],
          confidence: 0.85,
        });
      }
      
      // 3. Monitoring Strategy
      if (jsonData.monitoringStrategy) {
        const mon = jsonData.monitoringStrategy;
        let monContent = `**Monitoring Approach:** ${mon.approach}\n`;
        
        if (mon.onSiteFrequency) {
          monContent += `**On-site Frequency:** ${mon.onSiteFrequency}\n`;
        }
        
        if (mon.remoteMonitoring) {
          monContent += `**Remote Monitoring:** ${mon.remoteMonitoring}\n`;
        }
        
        if (mon.rationale) {
          monContent += `\n*${mon.rationale}*`;
        }
        
        sections.push({
          title: 'Monitoring Strategy',
          content: monContent,
          type: 'recommendation',
          actionable: true,
          actionableData: {
            monitoringApproach: mon.approach // Changed to directly set the field
          },
          citations: [],
          confidence: 0.85,
        });
      }
      
      // 4. Benchmark Studies
      if (jsonData.benchmarkStudies && jsonData.benchmarkStudies.length > 0) {
        const benchmarkContent = jsonData.benchmarkStudies.map((study: any) => {
          let content = `**${study.nctNumber}**\n`;
          if (study.recruitmentRate) {
            content += `• Recruitment: ${study.recruitmentRate}\n`;
          }
          if (study.operationalApproach) {
            content += `• Approach: ${study.operationalApproach}`;
          }
          return content;
        }).join('\n\n');
        
        sections.push({
          title: 'Benchmark Studies',
          content: benchmarkContent,
          type: 'references',
          actionable: false,
          citations: [], // Only use real citations from Bedrock
          confidence: 0.8,
        });
      }
      
    } catch (error) {
      console.log('Failed to parse JSON, falling back to text parsing:', error);
      return this.parseResponseIntoSections(text, citations, 'operational-strategy');
    }
    
    console.log('===========================================\n');
    
    return sections;
  }

  private parseFinancialPlanningResponse(text: string, citations: any[]): any[] {
    const sections = [];
    
    console.log('\n=== PARSING FINANCIAL PLANNING RESPONSE ===');
    console.log('Text length:', text.length);
    
    try {
      // Extract JSON from response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in financial planning response');
      }
      
      const jsonData = JSON.parse(jsonMatch[0]);
      console.log('Successfully parsed JSON for financial planning');
      console.log('JSON data structure:', JSON.stringify(jsonData, null, 2));
      
      // 1. Participant Compensation
      if (jsonData.participantCompensation) {
        const comp = jsonData.participantCompensation;
        let compContent = '';
        const compData: any = {};
        
        if (comp.perVisitAmount) {
          compContent += `**Per-Visit Payment:** ${comp.perVisitAmount.value}`;
          if (comp.perVisitAmount.range) {
            compContent += ` (Range: ${comp.perVisitAmount.range.min} - ${comp.perVisitAmount.range.max})`;
          }
          compContent += '\n';
          if (comp.perVisitAmount.rationale) {
            compContent += `*${comp.perVisitAmount.rationale}*\n`;
          }
          compContent += '\n';
          
          // Extract numeric value for form field
          const amountMatch = comp.perVisitAmount.value.match(/\$?(\d+(?:-\d+)?)/);
          if (amountMatch) {
            compData.perVisitCompensation = amountMatch[1];
          }
        }
        
        if (comp.completionBonus) {
          compContent += `**Completion Bonus:** ${comp.completionBonus.value}`;
          if (comp.completionBonus.range) {
            compContent += ` (Range: ${comp.completionBonus.range.min} - ${comp.completionBonus.range.max})`;
          }
          compContent += '\n';
          if (comp.completionBonus.rationale) {
            compContent += `*${comp.completionBonus.rationale}*\n`;
          }
          compContent += '\n';
          
          // Extract numeric value for form field
          const bonusMatch = comp.completionBonus.value.match(/\$?(\d+)/);
          if (bonusMatch) {
            compData.completionBonus = bonusMatch[1];
          }
        }
        
        if (comp.totalCompensation) {
          compContent += `**Total Compensation:** ${comp.totalCompensation.value}\n`;
          if (comp.totalCompensation.breakdown) {
            compContent += `*${comp.totalCompensation.breakdown}*\n`;
          }
          compContent += '\n';
        }
        
        if (comp.paymentSchedule) {
          compContent += `**Payment Schedule:** ${comp.paymentSchedule}`;
          // Normalize payment schedule to match dropdown values
          let scheduleNormalized = comp.paymentSchedule.trim();
          
          // Map common variations to exact dropdown values
          const scheduleMap: Record<string, string> = {
            'after each visit': 'After each visit',
            'after every visit': 'After each visit',
            'per visit': 'After each visit',
            'monthly': 'Monthly',
            'monthly basis': 'Monthly',
            'upon completion': 'Upon completion',
            'at completion': 'Upon completion',
            'study completion': 'Upon completion',
            'milestone-based': 'Milestone-based',
            'milestone based': 'Milestone-based',
            'by milestone': 'Milestone-based'
          };
          
          // Try to match with known variations (case-insensitive)
          const lowerSchedule = scheduleNormalized.toLowerCase();
          for (const [key, value] of Object.entries(scheduleMap)) {
            if (lowerSchedule.includes(key)) {
              scheduleNormalized = value;
              break;
            }
          }
          
          // If no match found, try to determine best match
          if (!['After each visit', 'Monthly', 'Upon completion', 'Milestone-based'].includes(scheduleNormalized)) {
            if (lowerSchedule.includes('visit')) {
              scheduleNormalized = 'After each visit';
            } else if (lowerSchedule.includes('month')) {
              scheduleNormalized = 'Monthly';
            } else if (lowerSchedule.includes('complet')) {
              scheduleNormalized = 'Upon completion';
            } else if (lowerSchedule.includes('milestone')) {
              scheduleNormalized = 'Milestone-based';
            }
            console.log('Payment schedule did not match exactly, using best guess:', scheduleNormalized);
          }
          
          console.log('Payment schedule raw:', comp.paymentSchedule);
          console.log('Payment schedule normalized:', scheduleNormalized);
          compData.paymentSchedule = scheduleNormalized;
        }
        
        console.log('Participant compensation actionableData:', compData);
        sections.push({
          title: 'Participant Compensation',
          content: compContent.trim(),
          type: 'recommendation',
          actionable: true,
          actionableData: compData,
          citations: [],
          confidence: 0.85,
        });
      }
      
      // 2. Travel Reimbursement
      if (jsonData.travelReimbursement) {
        const travel = jsonData.travelReimbursement;
        let travelContent = '';
        const travelData: any = {};
        
        if (travel.mileageRate) {
          travelContent += `**Mileage Rate:** ${travel.mileageRate}\n`;
          // Extract numeric value
          const mileageMatch = travel.mileageRate.match(/\$?([\d.]+)/);
          if (mileageMatch) {
            travelData.mileageRate = mileageMatch[1];
          }
        }
        
        if (travel.parkingCap) {
          travelContent += `**Parking Cap:** ${travel.parkingCap} per visit\n`;
          // Extract numeric value
          const parkingMatch = travel.parkingCap.match(/\$?(\d+)/);
          if (parkingMatch) {
            travelData.parkingReimbursement = parkingMatch[1];
          }
        }
        
        if (travel.publicTransport) {
          travelContent += `**Public Transport:** ${travel.publicTransport}\n`;
          // Extract numeric value for public transport cap
          const publicMatch = travel.publicTransport.match(/\$?(\d+)/);
          console.log('Public transport raw:', travel.publicTransport);
          console.log('Public transport match:', publicMatch);
          if (publicMatch) {
            travelData.publicTransportCap = publicMatch[1];
            console.log('Public transport cap extracted:', publicMatch[1]);
          }
        }
        
        if (travel.overnight) {
          travelContent += '\n**Overnight Stays:**\n';
          if (travel.overnight.hotelCap) {
            travelContent += `• Hotel Cap: ${travel.overnight.hotelCap}\n`;
            // Extract hotel cap numeric value
            const hotelMatch = travel.overnight.hotelCap.match(/\$?(\d+)/);
            console.log('Hotel cap raw:', travel.overnight.hotelCap);
            console.log('Hotel cap match:', hotelMatch);
            if (hotelMatch) {
              travelData.hotelCap = hotelMatch[1];
              console.log('Hotel cap extracted:', hotelMatch[1]);
            }
          }
          if (travel.overnight.mealAllowance) {
            travelContent += `• Meal Allowance: ${travel.overnight.mealAllowance}\n`;
            // Extract meal allowance numeric value
            const mealMatch = travel.overnight.mealAllowance.match(/\$?(\d+)/);
            console.log('Meal allowance raw:', travel.overnight.mealAllowance);
            console.log('Meal allowance match:', mealMatch);
            if (mealMatch) {
              travelData.mealAllowance = mealMatch[1];
              console.log('Meal allowance extracted:', mealMatch[1]);
            }
          }
        }
        
        console.log('Travel reimbursement actionableData:', travelData);
        sections.push({
          title: 'Travel Reimbursement',
          content: travelContent.trim(),
          type: 'recommendation',
          actionable: true,
          actionableData: travelData,
          citations: [],
          confidence: 0.85,
        });
      }
      
      // 3. Additional Support
      if (jsonData.additionalSupport) {
        const support = jsonData.additionalSupport;
        let supportContent = '';
        let additionalDetails = [];
        
        if (support.childcare) {
          supportContent += `**Childcare:** ${support.childcare}\n`;
          additionalDetails.push(`Childcare: ${support.childcare}`);
        }
        
        if (support.lostWages) {
          supportContent += `**Lost Wages:** ${support.lostWages}\n`;
          additionalDetails.push(`Lost wages: ${support.lostWages}`);
        }
        
        if (support.specialAccommodations) {
          supportContent += `**Special Accommodations:** ${support.specialAccommodations}\n`;
          additionalDetails.push(`Special accommodations: ${support.specialAccommodations}`);
        }
        
        if (supportContent) {
          const supportData = {
            additionalSupport: additionalDetails.join('; ')
          };
          console.log('Additional support actionableData:', supportData);
          sections.push({
            title: 'Additional Support',
            content: supportContent.trim(),
            type: 'recommendation',
            actionable: true,
            actionableData: supportData,
            citations: [],
            confidence: 0.85,
          });
        }
      }
      
      // 4. Billing Scenarios
      if (jsonData.billingScenarios && jsonData.billingScenarios.length > 0) {
        const billingContent = jsonData.billingScenarios.map((scenario: any) => {
          let content = `**${scenario.scenario}**\n`;
          if (scenario.billing) {
            content += `• Billing: ${scenario.billing}\n`;
          }
          if (scenario.coverage) {
            content += `• Coverage: ${scenario.coverage}`;
          }
          return content;
        }).join('\n\n');
        
        sections.push({
          title: 'Billing Scenarios',
          content: billingContent,
          type: 'information',
          actionable: false,
          citations: [],
          confidence: 0.85,
        });
      }
      
      // 5. Benchmark Studies
      if (jsonData.benchmarkStudies && jsonData.benchmarkStudies.length > 0) {
        const benchmarkContent = jsonData.benchmarkStudies.map((study: any) => {
          let content = `**${study.nctNumber}**\n`;
          if (study.compensationModel) {
            content += `• Compensation Model: ${study.compensationModel}\n`;
          }
          if (study.totalCompensation) {
            content += `• Total Compensation: ${study.totalCompensation}`;
          }
          return content;
        }).join('\n\n');
        
        sections.push({
          title: 'Benchmark Studies',
          content: benchmarkContent,
          type: 'references',
          actionable: false,
          citations: [], // Only use real citations from Bedrock
          confidence: 0.8,
        });
      }
      
    } catch (error) {
      console.log('Failed to parse JSON, falling back to text parsing:', error);
      return this.parseResponseIntoSections(text, citations, 'financial-planning');
    }
    
    console.log('Financial planning sections created:');
    sections.forEach((section, index) => {
      console.log(`  ${index + 1}. ${section.title} - type: ${section.type}, actionable: ${section.actionable}`);
      if (section.actionableData) {
        console.log(`     ActionableData keys: ${Object.keys(section.actionableData).join(', ')}`);
      }
    });
    console.log('===========================================\n');
    
    return sections;
  }
  
  private parseResponseIntoSections(text: string, citations: any[], field: string): any[] {
    const sections = [];
    
    // Define section types based on keywords
    const sectionTypeMap: Record<string, { type: string; actionable: boolean }> = {
      'RECOMMENDATION': { type: 'recommendation', actionable: true },
      'PRIMARY RECOMMENDATION': { type: 'recommendation', actionable: true },
      'PHASE RECOMMENDATION': { type: 'recommendation', actionable: true },
      'SECONDARY ENDPOINTS RECOMMENDATION': { type: 'overview', actionable: false },
      'EFFICACY ENDPOINTS': { type: 'efficacy-endpoints', actionable: true },
      'SAFETY ENDPOINTS': { type: 'safety-endpoints', actionable: true },
      'QUALITY OF LIFE ENDPOINTS': { type: 'qol-endpoints', actionable: true },
      'EXPLORATORY ENDPOINTS': { type: 'exploratory-endpoints', actionable: true },
      'MEASUREMENT DETAILS': { type: 'details', actionable: false },
      'KEY DESIGN ELEMENTS': { type: 'details', actionable: false },
      'RATIONALE': { type: 'rationale', actionable: false },
      'ALTERNATIVE OPTIONS': { type: 'alternatives', actionable: true },
      'SAMPLE SIZE ESTIMATE': { type: 'calculation', actionable: false },
      'SIMILAR STUDIES': { type: 'references', actionable: false },
      'CRITICAL CONSIDERATIONS': { type: 'considerations', actionable: false },
      'SIMILAR SUCCESSFUL STUDIES': { type: 'references', actionable: false },
    };
    
    // Split by markdown headers (##)
    const sectionRegex = /^#{2}\s+(.+?)$/gm;
    const matches = [...text.matchAll(sectionRegex)];
    
    if (matches.length > 0) {
      // Parse structured sections
      for (let i = 0; i < matches.length; i++) {
        const match = matches[i];
        const nextMatch = matches[i + 1];
        const startIndex = match.index! + match[0].length;
        const endIndex = nextMatch ? nextMatch.index! : text.length;
        
        const rawTitle = match[1]!.trim();
        const title = rawTitle.replace(/\*+/g, '').trim(); // Remove markdown bold
        const content = text.substring(startIndex, endIndex).trim();
        
        // Skip empty sections
        if (!content) {
          continue;
        }
        
        // Determine section type and actionability
        const upperTitle = title.toUpperCase();
        let sectionInfo = { type: 'information', actionable: false };
        
        for (const [key, value] of Object.entries(sectionTypeMap)) {
          if (upperTitle.includes(key)) {
            sectionInfo = value;
            break;
          }
        }
        
        // Check if this is an endpoints section that needs special parsing
        const isEndpointSection = sectionInfo.type.endsWith('-endpoints');
        
        if (isEndpointSection) {
          // Parse numbered endpoints list
          const endpoints = this.parseEndpointsList(content);
          
          sections.push({
            title: this.formatSectionTitle(title, sectionInfo.type),
            content: content, // Keep original for reference
            type: sectionInfo.type,
            actionable: sectionInfo.actionable,
            endpoints: endpoints, // Add parsed endpoints array
            citations: this.extractCitations(content, citations),
            confidence: 0.85,
          });
        } else {
          // Clean and format the content normally
          const cleanContent = this.formatSectionContent(content, sectionInfo.type);
          
          sections.push({
            title: this.formatSectionTitle(title, sectionInfo.type),
            content: cleanContent,
            type: sectionInfo.type,
            actionable: sectionInfo.actionable,
            citations: this.extractCitations(content, citations),
            confidence: sectionInfo.type === 'recommendation' ? 0.90 : 0.85,
          });
        }
      }
    }
    
    // Fallback for unstructured responses
    if (sections.length === 0) {
      // Try to extract a recommendation from the text
      const recommendationMatch = text.match(/(?:recommend|suggest|advise)[\s\S]{0,500}/i);
      
      if (recommendationMatch) {
        sections.push({
          title: 'Recommendation',
          content: recommendationMatch[0],
          type: 'recommendation',
          actionable: true,
          citations: this.extractCitations(text, citations),
          confidence: 0.75,
        });
      } else {
        sections.push({
          title: this.getDefaultTitle(field),
          content: text,
          type: 'information',
          actionable: false,
          citations: this.extractCitations(text, citations),
          confidence: 0.70,
        });
      }
    }
    
    return sections;
  }
  
  private parseEndpointsList(content: string): any[] {
    const endpoints = [];
    
    // Pattern to match numbered endpoints with bold titles
    // Matches: **1. Title** or **Title** after a number
    const lines = content.split('\n');
    let currentEndpoint: any = null;
    
    for (const line of lines) {
      // Check for numbered item with bold title (e.g., "**1. Response Rate**" or "1. **Response Rate**")
      const numberedMatch = line.match(/^(?:\*\*)?(\d+)\.\s*\*?\*?(.+?)\*?\*?$/);
      
      if (numberedMatch) {
        // Save previous endpoint if exists
        if (currentEndpoint) {
          endpoints.push(currentEndpoint);
        }
        
        // Start new endpoint
        const title = numberedMatch[2].replace(/\*/g, '').trim();
        currentEndpoint = {
          title: title,
          description: []
        };
      } else if (currentEndpoint && line.trim()) {
        // Add description lines (skip empty lines)
        const trimmedLine = line.trim();
        if (trimmedLine.startsWith('-') || trimmedLine.startsWith('•')) {
          // Remove bullet and add to description
          currentEndpoint.description.push(trimmedLine.substring(1).trim());
        } else if (!trimmedLine.startsWith('#')) {
          // Add non-header lines to description
          currentEndpoint.description.push(trimmedLine);
        }
      }
    }
    
    // Don't forget the last endpoint
    if (currentEndpoint) {
      endpoints.push(currentEndpoint);
    }
    
    // Format each endpoint for display
    return endpoints.map(ep => ({
      title: ep.title,
      content: ep.description.join('\n'),
      actionable: true
    }));
  }
  
  private formatSectionContent(content: string, sectionType: string): string {
    // Clean up the content based on section type
    let formatted = content
      .split('\n')
      .map(line => line.trim())
      .filter(line => line && !line.startsWith('##'))
      .join('\n');
    
    // Special formatting for recommendation sections
    if (sectionType === 'recommendation') {
      // Remove brackets and clean up
      formatted = formatted
        .replace(/^\[/, '')
        .replace(/\]$/, '')
        .trim();
    }
    
    // Format bullet points consistently
    formatted = formatted
      .replace(/^- /gm, '• ')
      .replace(/^\* /gm, '• ')
      .replace(/\*\*(.*?)\*\*/g, '$1'); // Remove bold markers but keep text
    
    return formatted;
  }
  
  private formatSectionTitle(title: string, sectionType: string): string {
    // Create user-friendly titles
    const titleMap: Record<string, string> = {
      'recommendation': 'Recommendation',
      'details': 'Implementation Details',
      'rationale': 'Scientific Rationale',
      'alternatives': 'Alternative Options',
      'calculation': 'Statistical Considerations',
      'references': 'Supporting Studies',
      'considerations': 'Key Considerations',
    };
    
    // If we have a mapped title for this type, use it
    if (titleMap[sectionType]) {
      return titleMap[sectionType];
    }
    
    // Otherwise, clean up the original title
    return title
      .replace(/^(PRIMARY |PHASE |KEY |CRITICAL |SIMILAR )/i, '')
      .replace(/_/g, ' ')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  }
  
  private extractCitations(text: string, citations: any[]): any[] {
    const nctRegex = /NCT\d{8}/g;
    const mentionedNCTs = text.match(nctRegex) || [];
    
    // First try to match mentioned NCTs with retrieved references
    const citationList = citations
      .flatMap(c => c.retrievedReferences || [])
      .filter(ref => {
        const uri = ref.location?.s3Location?.uri || '';
        return mentionedNCTs.some(nct => uri.includes(nct));
      })
      .map(ref => ({
        id: this.extractNCTId(ref.location?.s3Location?.uri),
        title: this.extractTitle(ref.content?.text),
        url: ref.location?.s3Location?.uri || '',
        relevance: ref.score || 0.5,
      }))
      .slice(0, 5);
    
    // If citations were found from retrieved references, return them
    if (citationList.length > 0) {
      return citationList;
    }
    
    // If no citations from retrieved references but NCT numbers are mentioned in text,
    // create synthetic citations from the mentioned NCT numbers
    if (mentionedNCTs.length > 0 && citationList.length === 0) {
      console.log('Creating synthetic citations for mentioned NCT numbers:', mentionedNCTs);
      return mentionedNCTs
        .filter((nct, index, self) => self.indexOf(nct) === index) // Remove duplicates
        .slice(0, 5) // Limit to 5
        .map(nct => ({
          id: nct,
          title: `Referenced study ${nct}`,
          url: `s3://trialynx-clinical-trials-gov/text-documents/${nct.substring(0, 6)}/${nct}.txt`,
          relevance: 0.75, // Default relevance for synthetic citations
        }));
    }
    
    // If no specific citations found but we have citations with references, include top ones
    if (citations.length > 0) {
      const topRefs = citations
        .flatMap(c => c.retrievedReferences || [])
        .slice(0, 3);
      
      if (topRefs.length > 0) {
        return topRefs.map(ref => ({
          id: this.extractNCTId(ref.location?.s3Location?.uri),
          title: this.extractTitle(ref.content?.text),
          url: ref.location?.s3Location?.uri || '',
          relevance: ref.score || 0.5,
        }));
      }
    }
    
    return [];
  }
  
  private extractNCTId(uri: string): string {
    const match = uri?.match(/NCT\d{8}/);
    return match ? match[0] : 'Unknown';
  }
  
  private extractTitle(content: string): string {
    const lines = content?.split('\n') || [];
    const titleLine = lines.find(line => 
      line.includes('Title:') || line.includes('Official Title:')
    );
    if (titleLine) {
      return titleLine.replace(/^.*?Title:\s*/i, '').substring(0, 200).trim();
    }
    return content?.substring(0, 100) + '...' || 'Clinical Trial';
  }
  
  private extractSourceDocuments(citations: any[]): any[] {
    const sources: any[] = [];
    const seenNCTs = new Set<string>();
    
    // Extract unique source documents from all citations
    citations.forEach(citation => {
      if (citation.retrievedReferences) {
        citation.retrievedReferences.forEach((ref: any) => {
          const s3Uri = ref.location?.s3Location?.uri;
          if (s3Uri) {
            const nctId = this.extractNCTId(s3Uri);
            
            // Only add if we haven't seen this NCT ID yet
            if (nctId && nctId !== 'Unknown' && !seenNCTs.has(nctId)) {
              seenNCTs.add(nctId);
              
              const content = ref.content?.text || '';
              
              // Extract status - look for it anywhere in the content
              let status = 'Unknown';
              const statusMatch = content.match(/Status:\s*([A-Z]+)/i);
              if (statusMatch && statusMatch[1]) {
                status = statusMatch[1].trim();
                // Handle common truncations
                if (status === 'UNKNOWN') status = 'Unknown';
                else if (status === 'COMPLETE') status = 'COMPLETED';
                else if (status === 'RECRUIT') status = 'RECRUITING';
              }
              
              // Extract study type - look for it anywhere in the content
              let studyType = 'Unknown';
              const studyTypeMatch = content.match(/Study Type:\s*([A-Z]+)/i);
              if (studyTypeMatch && studyTypeMatch[1]) {
                studyType = studyTypeMatch[1].trim();
                // Handle common truncations
                if (studyType === 'INTERVENTIONA') studyType = 'INTERVENTIONAL';
                else if (studyType === 'OBSERVATION') studyType = 'OBSERVATIONAL';
                else if (studyType === 'UNKNOWN') studyType = 'Unknown';
              }
              
              // Get content preview - keep it simple, just clean it up a bit
              let excerpt = undefined;
              const lines = content.split('\n');
              
              // Look for meaningful content keywords
              const meaningfulContent = lines.find(line => {
                const lower = line.toLowerCase();
                return lower.includes('primary outcome') ||
                       lower.includes('secondary endpoint') ||
                       lower.includes('remission') ||
                       lower.includes('response') ||
                       lower.includes('progression') ||
                       lower.includes('survival') ||
                       lower.includes('safety') ||
                       lower.includes('efficacy');
              });
              
              if (meaningfulContent) {
                // If we found meaningful content, use that
                excerpt = meaningfulContent.trim().substring(0, 200);
              } else {
                // Otherwise, just grab the first non-header content
                const nonHeaderLines = lines.filter(line => {
                  const trimmed = line.trim();
                  return trimmed && 
                         !trimmed.match(/^-+$/) &&
                         !trimmed.match(/^=+$/) &&
                         !trimmed.match(/^[A-Z\s]{0,30}$/) && // Skip short all-caps lines
                         trimmed.length > 20; // Skip very short lines
                });
                
                if (nonHeaderLines.length > 0) {
                  excerpt = nonHeaderLines[0].trim().substring(0, 200);
                }
              }
              
              sources.push({
                nctId,
                status,
                studyType,
                s3Uri,
                excerpt,
                score: ref.score, // Keep score but won't display if undefined
              });
            }
          }
        });
      }
    });
    
    // Sort by score (relevance) if available
    sources.sort((a, b) => (b.score || 0) - (a.score || 0));
    
    return sources;
  }
  
  private getDefaultTitle(field: string): string {
    const titles: Record<string, string> = {
      'phase': 'Phase Recommendation',
      'primary-endpoint': 'Primary Endpoint Recommendation',
      'secondary-endpoints': 'Secondary Endpoints Recommendation',
      'study-duration': 'Study Duration Recommendation',
      'site-selection': 'Site Selection Strategy',
      'visit-schedule': 'Visit Schedule Recommendation',
      'safety-monitoring': 'Safety Monitoring Plan',
    };
    return titles[field] || 'Recommendation';
  }
  
  private getMockResponse(request: BedrockQueryRequest & { field?: string }, startTime: number): BedrockQueryResponse {
    console.log('Generating mock response for field:', request.field);
    
    const mockSections = [
      {
        title: 'Mock Recommendation',
        content: `**Mock Response for ${request.field || 'testing'}**\n\nThis is a mock response for local testing. In production, this would return actual insights from the AWS Bedrock Knowledge Base.\n\n**Context:**\n- Condition: ${request.context?.condition || 'Not specified'}\n- Phase: ${request.context?.phase || 'Not specified'}\n- Study Type: ${request.context?.studyType || 'Not specified'}`,
        type: 'recommendation' as const,
        actionable: true,
        actionableData: {
          field: request.field || 'test',
          value: 'Mock value for testing',
        },
        citations: [],
        confidence: 0.75,
      },
      {
        title: 'Mock Supporting Information',
        content: 'This section would contain supporting information and references from similar trials.',
        type: 'information' as const,
        actionable: false,
        citations: [],
        confidence: 0.8,
      }
    ];
    
    const mockSources = [
      {
        nctId: 'NCT00000001',
        title: 'Mock Clinical Trial Example',
        condition: request.context?.condition || 'Test Condition',
        phase: request.context?.phase || 'Phase 2',
        url: 's3://mock-bucket/NCT00000001.txt',
        score: 0.95,
      }
    ];
    
    return {
      text: 'Mock response text for local testing',
      sections: mockSections,
      sources: mockSources,
      citations: [],
      queryTime: Date.now() - startTime,
    };
  }
}