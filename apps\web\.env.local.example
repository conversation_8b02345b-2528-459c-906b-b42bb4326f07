# When adding additional environment variables, the schema in "/src/env.js"
# should be updated accordingly.

# ================================
# AUTHENTICATION CONFIGURATION
# ================================

# NextAuth Secret (required)
# You can generate a new secret with: npx auth secret
AUTH_SECRET="REPLACE_WITH_YOUR_AUTH_SECRET"

# NextAuth URL for local development (NextAuth prefers NEXTAUTH_URL)
AUTH_URL="http://localhost:3000"
NEXTAUTH_URL="http://localhost:3000"

# Google OAuth Configuration (get from Google Cloud Console)
AUTH_GOOGLE_ID="REPLACE_WITH_YOUR_GOOGLE_OAUTH_CLIENT_ID"
AUTH_GOOGLE_SECRET="REPLACE_WITH_YOUR_GOOGLE_OAUTH_CLIENT_SECRET"

# Azure AD OAuth (optional - uncomment to enable)
# AUTH_AZURE_AD_ID=REPLACE_WITH_YOUR_AZURE_APP_ID
# AUTH_AZURE_AD_SECRET=REPLACE_WITH_YOUR_AZURE_APP_SECRET
# AUTH_AZURE_AD_TENANT_ID=REPLACE_WITH_YOUR_AZURE_TENANT_ID

# ================================
# AWS & DYNAMODB CONFIGURATION
# ================================

# Access via AWS CLI profile
# IMPORTANT: Replace with your actual AWS SSO profile name
# This is required for local development to access DynamoDB and other AWS services
# Get your profile name with: aws configure list-profiles
AWS_PROFILE=REPLACE_WITH_YOUR_AWS_SSO_PROFILE_NAME

# AWS Region
AWS_REGION=us-west-2

# DynamoDB Table Names (using deployed infrastructure)
AUTH_TABLE_NAME=trialynx-insights-dev-next-auth
ALLOWLIST_TABLE_NAME=trialynx-insights-dev-allowlist
WAITLIST_TABLE_NAME=trialynx-insights-dev-waitlist
STUDY_SESSIONS_TABLE_NAME=trialynx-insights-dev-study-design-sessions

# AWS Bedrock Configuration
BEDROCK_KNOWLEDGE_BASE_ID=M0KKCP0UI9

# Lambda API Gateway Endpoint
LAMBDA_ENDPOINT_URL=https://81cjr0fh5l.execute-api.us-west-2.amazonaws.com/dev

# ================================
# EMAIL CONFIGURATION (optional)
# ================================

# Email sending configuration (for email verification, password reset)
EMAIL_FROM="TriaLynx Insights <<EMAIL>>"
# EMAIL_SERVER=smtp://username:<EMAIL>:587

# ================================
# FEATURE FLAGS & CLIENT CONFIG
# ================================

# Feature Flags
NEXT_PUBLIC_USE_MOCK_DATA=false  # Use real AWS Lambda/Bedrock
NEXT_PUBLIC_ENABLE_AWS=true      # Enable AWS integrations

# Public API URLs (if needed for client-side requests)
# NEXT_PUBLIC_API_GATEWAY_URL=https://81cjr0fh5l.execute-api.us-west-2.amazonaws.com/dev

# ================================
# DEVELOPMENT ENVIRONMENT
# ================================

NODE_ENV=development