import { withAuth } from "next-auth/middleware";
import { NextResponse } from "next/server";

export default withAuth(
  function middleware(req) {
    // Get the pathname of the request (e.g. /, /dashboard, /study/new)
    const { pathname } = req.nextUrl;
    
    // Allow static assets and API routes (except protected tRPC routes)
    if (
      pathname.startsWith('/_next') ||
      pathname.startsWith('/api/auth') ||
      pathname.startsWith('/favicon') ||
      pathname.startsWith('/robots') ||
      pathname.includes('.')
    ) {
      return NextResponse.next();
    }

    // Public routes that don't require authentication
    const publicRoutes = [
      '/',
      '/auth/signin',
      '/auth/signup',
      '/auth/error',
      '/auth/verify-request',
      '/auth/invite-required'
    ];

    // If it's a public route, allow access
    if (publicRoutes.includes(pathname)) {
      return NextResponse.next();
    }

    // For all other routes, authentication is required
    // The withAuth wrapper will handle the redirect to sign-in
    return NextResponse.next();
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl;
        
        // Allow public routes without authentication
        const publicRoutes = [
          '/',
          '/auth/signin',
          '/auth/signup',
          '/auth/error',
          '/auth/verify-request',
          '/auth/invite-required'
        ];

        if (publicRoutes.includes(pathname)) {
          return true;
        }

        // Protect tRPC API routes
        if (pathname.startsWith('/api/trpc')) {
          return !!token;
        }

        // Protect dashboard and study routes
        if (pathname.startsWith('/dashboard') || pathname.startsWith('/study')) {
          return !!token;
        }

        // Default to requiring authentication for all other routes
        return !!token;
      },
    },
    pages: {
      signIn: '/auth/signin',
    },
  }
);

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};