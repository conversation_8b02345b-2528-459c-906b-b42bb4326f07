import { NextRequest, NextResponse } from 'next/server';
import { auth } from '~/server/auth';
import { BedrockKnowledgeBaseClient } from '~/server/bedrock/knowledge-base-client';
import type { BedrockQueryRequest } from '~/server/bedrock/types';

const bedrockClient = new BedrockKnowledgeBaseClient();

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth();
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse request body
    const body: BedrockQueryRequest = await request.json();
    
    // Validate request
    if (!body.query || typeof body.query !== 'string') {
      return NextResponse.json(
        { error: 'Query parameter is required' },
        { status: 400 }
      );
    }
    
    // Query the knowledge base with field context
    console.log('🎯 ECS BEDROCK QUERY - Field:', body.field, 'at', new Date().toISOString());
    console.log('Query:', body.query);
    console.log('Context:', body.context);
    
    const response = await bedrockClient.query({
      ...body,
      field: body.field || 'default',
    });
    
    // Log the query for analytics
    console.log({
      userId: session.user.id,
      query: body.query,
      field: body.field,
      hasResults: !!response.sections,
      metadata: response.metadata
    });
    
    // Log sections being returned for debugging
    if (response.sections) {
      console.log('\n=== RETURNING SECTIONS TO FRONTEND (ECS) ===');
      console.log('Total sections:', response.sections.length);
      response.sections.forEach((section, idx) => {
        console.log(`Section ${idx + 1}:`, {
          title: section.title,
          type: section.type,
          actionable: section.actionable,
          hasEndpoints: !!section.endpoints,
          endpointsCount: section.endpoints?.length || 0
        });
      });
      console.log('=======================================\n');
    }
    
    return NextResponse.json(response);
    
  } catch (error) {
    console.error('Error handling ECS Bedrock request:', error);
    
    // Determine appropriate error response
    if (error instanceof Error) {
      if (error.message.includes('authorization')) {
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 401 }
        );
      }
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle CORS preflight
export async function OPTIONS() {
  return NextResponse.json(
    {},
    {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    }
  );
}