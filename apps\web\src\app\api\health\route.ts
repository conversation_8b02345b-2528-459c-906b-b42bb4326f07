import { NextResponse } from "next/server";
import { env } from "../../../env";

export async function GET() {
  try {
    const healthStatus = {
      status: "healthy",
      timestamp: new Date().toISOString(),
      environment: env.NODE_ENV,
      version: process.env.npm_package_version || "unknown",
      services: {
        database: await checkDynamoDBConnection(),
        auth: checkAuthConfiguration(),
      },
    };

    return NextResponse.json(healthStatus);
  } catch (error) {
    console.error("Health check failed:", error);
    
    return NextResponse.json(
      {
        status: "unhealthy",
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 503 }
    );
  }
}

async function checkDynamoDBConnection() {
  try {
    // Simple check - we don't want to create a real connection here
    // Just verify the environment variables are set
    const hasAuthConfig = !!(env.AUTH_DYNAMODB_REGION || env.AWS_REGION);
    const hasTableConfig = !!(env.AUTH_TABLE_NAME);
    
    return {
      status: hasAuthConfig && hasTableConfig ? "configured" : "not_configured",
      region: env.AUTH_DYNAMODB_REGION || env.AWS_REGION || "not_set",
      tables: {
        auth: env.AUTH_TABLE_NAME || "not_set",
        allowlist: env.ALLOWLIST_TABLE_NAME || "not_set",
        waitlist: env.WAITLIST_TABLE_NAME || "not_set",
      },
    };
  } catch (error) {
    return {
      status: "error",
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

function checkAuthConfiguration() {
  const providers = [];
  
  if (env.EMAIL_SERVER) providers.push("email");
  if (env.AUTH_GOOGLE_ID && env.AUTH_GOOGLE_SECRET) providers.push("google");
  if (env.AUTH_AZURE_AD_ID && env.AUTH_AZURE_AD_SECRET) providers.push("azure-ad");
  
  return {
    status: providers.length > 0 ? "configured" : "not_configured",
    providers,
    secret_configured: !!env.AUTH_SECRET,
    url_configured: !!env.AUTH_URL,
  };
}