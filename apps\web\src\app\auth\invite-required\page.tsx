"use client";

import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { Suspense } from "react";

function InviteRequiredContent() {
  const searchParams = useSearchParams();
  const email = searchParams.get("email");

  return (
    <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100">
      <div className="w-full max-w-md space-y-8 rounded-2xl bg-white p-8 shadow-lg">
        <div className="text-center">
          <Link href="/" className="inline-flex items-center gap-2">
            <div className="h-10 w-10 rounded-lg bg-gradient-to-br from-[#5A32FA] to-[#7D2AE8]" />
            <span className="text-2xl font-bold text-gray-900">TriaLynx Insights</span>
          </Link>
          
          <div className="mt-6">
            <div className="mx-auto h-16 w-16 rounded-full bg-orange-100 flex items-center justify-center">
              <svg className="h-8 w-8 text-orange-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
          
          <h2 className="mt-6 text-3xl font-bold text-gray-900">You're on the waitlist!</h2>
          <p className="mt-2 text-sm text-gray-600">
            Thanks for your interest in TriaLynx Insights
          </p>
        </div>

        <div className="rounded-lg bg-orange-50 p-4 border border-orange-200">
          <div className="text-sm text-orange-800">
            <p className="font-medium mb-2">Access is currently limited</p>
            <p className="mb-3">
              TriaLynx Insights is invite-only during our beta phase. Only users who have been specifically 
              approved can register directly.
            </p>
            {email && (
              <p className="mb-3">
                <span className="font-medium">Your email ({email}) has been added to our waitlist.</span>
              </p>
            )}
            <p>
              We'll notify you as soon as access becomes available. Thanks for your patience!
            </p>
          </div>
        </div>

        <div className="space-y-4">
          <Link
            href="/auth/signin"
            className="w-full rounded-lg bg-gradient-to-r from-[#5A32FA] to-[#7D2AE8] px-4 py-2 text-white font-medium text-center block hover:from-[#7D2AE8] hover:to-[#5A32FA] focus:outline-none focus:ring-2 focus:ring-[#5A32FA] focus:ring-offset-2 transition-all duration-200"
          >
            Back to Sign In
          </Link>
          
          <Link
            href="/"
            className="w-full rounded-lg border border-gray-300 bg-white px-4 py-2 text-gray-700 font-medium text-center block hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-[#5A32FA] focus:ring-offset-2 transition-colors"
          >
            Go to Homepage
          </Link>
        </div>

        <div className="text-center">
          <p className="text-xs text-gray-500">
            Have questions?{" "}
            <a href="mailto:<EMAIL>" className="text-[#5A32FA] hover:text-[#7D2AE8]">
              Contact support
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}

export default function InviteRequiredPage() {
  return (
    <Suspense fallback={
      <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="w-full max-w-md space-y-8 rounded-2xl bg-white p-8 shadow-lg">
          <div className="text-center">Loading...</div>
        </div>
      </div>
    }>
      <InviteRequiredContent />
    </Suspense>
  );
}