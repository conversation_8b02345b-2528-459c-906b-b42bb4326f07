"use client";

import { signIn } from "next-auth/react";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { useState, Suspense } from "react";

function SignInForm() {
 const searchParams = useSearchParams();
 const callbackUrl = searchParams.get("callbackUrl") ?? "/dashboard";
 const [isLoading, setIsLoading] = useState(false);
 const [error, setError] = useState<string | null>(null);

 const handleCredentialsSignIn = async (e: React.FormEvent<HTMLFormElement>) => {
 e.preventDefault();
 setIsLoading(true);
 setError(null);

 const formData = new FormData(e.currentTarget);
 const email = formData.get("email") as string;
 const password = formData.get("password") as string;

 try {
 const result = await signIn("credentials", {
 email,
 password,
 redirect: false,
 callbackUrl,
 });

 if (result?.error) {
 setError("Invalid email or password");
 } else if (result?.url) {
 window.location.href = result.url;
 }
 } catch (err) {
 setError("An error occurred. Please try again.");
 } finally {
 setIsLoading(false);
 }
 };

 const handleOAuthSignIn = (provider: string) => {
 setIsLoading(true);
 signIn(provider, { callbackUrl });
 };

 return (
 <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100">
 <div className="w-full max-w-md space-y-8 rounded-2xl bg-white p-8 shadow-lg">
 <div className="text-center">
 <Link href="/" className="inline-flex items-center gap-2">
 <div className="h-10 w-10 rounded-lg bg-gradient-to-br from-blue-600 to-indigo-600" />
 <span className="text-2xl font-bold text-gray-900">TriaLynx Insights</span>
 </Link>
 <h2 className="mt-6 text-3xl font-bold text-gray-900">Sign in to your account</h2>
 <p className="mt-2 text-sm text-gray-600">
 Or{" "}
 <Link href="/auth/signup" className="font-medium text-blue-600 hover:text-blue-500">
 create a new account
 </Link>
 </p>
 </div>

 <form className="mt-8 space-y-6" onSubmit={handleCredentialsSignIn}>
 {error && (
 <div className="rounded-lg bg-red-50 p-4">
 <p className="text-sm text-red-800">{error}</p>
 </div>
 )}

 <div className="space-y-4">
 <div>
 <label htmlFor="email" className="block text-sm font-medium text-gray-700">
 Email address
 </label>
 <input
 id="email"
 name="email"
 type="email"
 autoComplete="email"
 required
 className="mt-1 block w-full rounded-lg border border-gray-300 px-3 py-2 text-gray-900 placeholder-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
 placeholder="<EMAIL>"
 />
 </div>

 <div>
 <label htmlFor="password" className="block text-sm font-medium text-gray-700">
 Password
 </label>
 <input
 id="password"
 name="password"
 type="password"
 autoComplete="current-password"
 required
 className="mt-1 block w-full rounded-lg border border-gray-300 px-3 py-2 text-gray-900 placeholder-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
 placeholder="Enter your password"
 />
 </div>
 </div>

 <div className="flex items-center justify-between">
 <div className="flex items-center">
 <input
 id="remember-me"
 name="remember-me"
 type="checkbox"
 className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
 />
 <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-900">
 Remember me
 </label>
 </div>

 <Link href="/auth/forgot-password" className="text-sm text-blue-600 hover:text-blue-500">
 Forgot your password?
 </Link>
 </div>

 <button
 type="submit"
 disabled={isLoading}
 className="w-full rounded-lg bg-blue-600 px-4 py-2 text-white font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
 >
 {isLoading ? "Signing in..." : "Sign in"}
 </button>

 </form>

 <div className="relative">
 <div className="absolute inset-0 flex items-center">
 <div className="w-full border-t border-gray-300" />
 </div>
 <div className="relative flex justify-center text-sm">
 <span className="bg-white px-2 text-gray-500">Or continue with</span>
 </div>
 </div>

 <div className="grid gap-3">
 {process.env.NEXT_PUBLIC_GOOGLE_ENABLED !== "false" && (
 <button
 type="button"
 onClick={() => handleOAuthSignIn("google")}
 disabled={isLoading}
 className="flex w-full items-center justify-center gap-2 rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
 >
 <svg className="h-5 w-5" viewBox="0 0 24 24">
 <path
 fill="#4285F4"
 d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
 />
 <path
 fill="#34A853"
 d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
 />
 <path
 fill="#FBBC05"
 d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
 />
 <path
 fill="#EA4335"
 d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
 />
 </svg>
 Sign in with Google
 </button>
 )}
 </div>
 </div>
 </div>
 );
}

export default function SignInPage() {
  return (
    <Suspense fallback={
      <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="w-full max-w-md space-y-8 rounded-2xl bg-white p-8 shadow-lg">
          <div className="text-center">Loading...</div>
        </div>
      </div>
    }>
      <SignInForm />
    </Suspense>
  );
}