"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useTrialDesignStore } from "~/store/trial-design";
import { But<PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Switch } from "~/components/ui/switch";
import { BlurFade } from "~/components/ui/blur-fade";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { InsightsButton } from "~/components/insights/InsightsButton";
import { InsightsPanelPortal } from "~/components/insights/InsightsPanelPortal";
import { DocumentViewerPortal } from "~/components/insights/DocumentViewerPortal";
import { TBDConfirmationPortal } from "~/components/ui/tbd-confirmation-portal";
import { api } from "~/trpc/react";
import { toast } from "sonner";
import { 
 ChevronLeft, 
 Beaker,
 ShieldCheck,
 FileSearch,
 Pill,
 Microscope,
 Heart,
 TestTube,
 AlertTriangle,
 Plus,
 X,
 AlertCircle
} from "lucide-react";

export default function InvestigationalProductPage() {
 const router = useRouter();
 const store = useTrialDesignStore();
 
 const [formData, setFormData] = useState({
 // Basic drug information (existing)
 name: store.discovery.intervention.name || "",
 condition: store.discovery.condition || "",
 category: store.discovery.intervention.category || "",
 mechanism: store.discovery.intervention.mechanism || "",
 class: store.discovery.intervention.class || "",
 isNewCompound: store.discovery.intervention.isNewCompound ?? false,
 
 // Enhanced drug product information (11 critical questions)
 medicalProblem: store.discovery.intervention.medicalProblem || "",
 comparisonToExistingTreatments: store.discovery.intervention.comparisonToExistingTreatments || "",
 regulatoryStatus: store.discovery.intervention.regulatoryStatus || "",
 activeIngredients: store.discovery.intervention.activeIngredients || [],
 inactiveIngredients: store.discovery.intervention.inactiveIngredients || [],
 ingredientRationale: store.discovery.intervention.ingredientRationale || "",
 preclinicalStudies: store.discovery.intervention.preclinicalStudies || "",
 toxicityStudies: store.discovery.intervention.toxicityStudies || "",
 clinicalTrialsHistory: store.discovery.intervention.clinicalTrialsHistory || "",
 keyFindings: store.discovery.intervention.keyFindings || "",
 pregnancySafety: store.discovery.intervention.pregnancySafety || "unknown",
 pregnancySafetyDetails: store.discovery.intervention.pregnancySafetyDetails || "",
 fdaApprovalStatus: store.discovery.intervention.fdaApprovalStatus || "",
 drugCompoundNumber: store.discovery.intervention.drugCompoundNumber || "",
 indApplicationNumber: store.discovery.intervention.indApplicationNumber || "",
 });

 const [newActiveIngredient, setNewActiveIngredient] = useState("");
 const [newInactiveIngredient, setNewInactiveIngredient] = useState("");
 const [activeInsightsPanel, setActiveInsightsPanel] = useState<string | null>(null);
 const [insightsData, setInsightsData] = useState<Record<string, { sections: any[]; sources?: any[]; progressStatus?: string; progressMessages?: string[] }>>({});
 const [documentViewerUrl, setDocumentViewerUrl] = useState<string | null>(null);
 const [showTBDConfirmation, setShowTBDConfirmation] = useState(false);
 const [blankFieldsList, setBlankFieldsList] = useState<string[]>([]);
 const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});
 const [showValidationErrors, setShowValidationErrors] = useState(false);
 
 const cachedInsights = store.insightsCache || {};

 // Helper functions for error styling
 const getFieldError = (fieldName: string) => showValidationErrors ? fieldErrors[fieldName] : null;
 const hasFieldError = (fieldName: string) => Boolean(getFieldError(fieldName));
 
 const getInputErrorClasses = (fieldName: string) => {
 return hasFieldError(fieldName) 
 ? "border-red-500 focus:border-red-500 focus:ring-red-500" 
 : "";
 };

 const getLabelErrorClasses = (fieldName: string) => {
 return hasFieldError(fieldName) 
 ? "text-red-600 " 
 : "";
 };


 const queryInsights = api.knowledgeBase.queryInsights.useMutation({
 onSuccess: (data, variables) => {
 const insightsPayload = {
 sections: data.sections || [],
 sources: data.sources || [],
 progressStatus: undefined,
 progressMessages: [],
 };
 
 setInsightsData(prev => ({
 ...prev,
 [variables.field]: insightsPayload
 }));
 
 store.cacheInsights(variables.field, insightsPayload);
 setActiveInsightsPanel(variables.field);
 },
 onError: (error) => {
 toast.error("Failed to get insights: " + error.message);
 },
 });

 const saveDiscovery = api.studyDesign.saveDiscovery.useMutation({
 onSuccess: () => {
 store.markStepCompleted("investigational-product");
 router.push("/study/new/study-design");
 },
 onError: (error) => {
 toast.error("Failed to save: " + error.message);
 },
 });

 const handleGetInsights = async (field: string, forceRefresh = false) => {
 if (!forceRefresh && cachedInsights[field]) {
 setActiveInsightsPanel(field);
 return;
 }
 
 setActiveInsightsPanel(field);
 setInsightsData(prev => ({
 ...prev,
 [field]: {
 sections: [],
 sources: [],
 progressStatus: 'Searching for drug product information...',
 progressMessages: [],
 }
 }));
 
 const progressUpdates = [
 { delay: 0, message: 'Initializing search...' },
 { delay: 1500, message: 'Searching knowledge base...' },
 { delay: 3500, message: 'Analyzing drug products...' },
 { delay: 6000, message: 'Extracting insights...' },
 { delay: 8500, message: 'Generating recommendations...' },
 ];
 
 progressUpdates.forEach(({ delay, message }) => {
 setTimeout(() => {
 setInsightsData(prev => {
 const current = prev[field];
 if (current && !current.sections?.length) {
 return {
 ...prev,
 [field]: {
 ...current,
 progressStatus: message,
 progressMessages: [...(current.progressMessages || []), message],
 }
 };
 }
 return prev;
 });
 }, delay);
 });
 
 const context = {
 studyType: store.discovery.studyType || undefined,
 condition: formData.condition || undefined,
 phase: store.discovery.phase || undefined,
 drugName: formData.name || undefined,
 drugClass: formData.class || undefined,
 category: formData.category || undefined,
 mechanism: formData.mechanism || undefined,
 isNewCompound: formData.isNewCompound || false,
 medicalProblem: formData.medicalProblem || undefined,
 activeIngredients: formData.activeIngredients?.length > 0 ? formData.activeIngredients.join(', ') : undefined,
 regulatoryStatus: formData.regulatoryStatus || undefined,
 preclinicalStudies: formData.preclinicalStudies || undefined,
 };

 const queries: Record<string, string> = {
 "competitive-landscape": `clinical trials and treatments for ${formData.condition || "depression"} ${formData.class ? `involving ${formData.class}` : ""} ${formData.mechanism ? `targeting serotonin dopamine` : ""}`.trim(),
 };

 await queryInsights.mutateAsync({
 sessionId: store.sessionId!,
 field,
 context,
 query: queries[field] || "",
 });
 };

 const addActiveIngredient = () => {
 if (newActiveIngredient.trim()) {
 setFormData(prev => ({
 ...prev,
 activeIngredients: [...prev.activeIngredients, newActiveIngredient.trim()]
 }));
 setNewActiveIngredient("");
 // Clear error when ingredients are added
 clearFieldError("activeIngredients");
 }
 };

 const removeActiveIngredient = (index: number) => {
 setFormData(prev => ({
 ...prev,
 activeIngredients: prev.activeIngredients.filter((_: any, i: number) => i !== index)
 }));
 };

 const addInactiveIngredient = () => {
 if (newInactiveIngredient.trim()) {
 setFormData(prev => ({
 ...prev,
 inactiveIngredients: [...prev.inactiveIngredients, newInactiveIngredient.trim()]
 }));
 setNewInactiveIngredient("");
 }
 };

 const removeInactiveIngredient = (index: number) => {
 setFormData(prev => ({
 ...prev,
 inactiveIngredients: prev.inactiveIngredients.filter((_: any, i: number) => i !== index)
 }));
 };

 // Enhanced validation function that tracks field errors
 const validateForm = () => {
 const errors: Record<string, string> = {};
 
 if (!formData.name.trim()) {
 errors.name = "Drug name is required";
 }
 
 if (!formData.condition.trim()) {
 errors.condition = "Medical condition is required";
 }
 
 if (!formData.medicalProblem.trim()) {
 errors.medicalProblem = "Medical problem description is required";
 }
 
 if (!formData.activeIngredients || formData.activeIngredients.length === 0) {
 errors.activeIngredients = "At least one active ingredient is required";
 }

 return errors;
 };

 // Smooth scroll to first error field
 const scrollToField = (fieldName: string) => {
 const element = document.getElementById(fieldName);
 if (element) {
 element.scrollIntoView({
 behavior: 'smooth',
 block: 'center',
 inline: 'nearest'
 });
 
 // Focus the field after scrolling
 setTimeout(() => {
 element.focus();
 }, 500);
 }
 };

 // Clear specific field error when user fills it
 const clearFieldError = (fieldName: string) => {
 if (fieldErrors[fieldName]) {
 setFieldErrors(prev => {
 const newErrors = { ...prev };
 delete newErrors[fieldName];
 return newErrors;
 });
 }
 };

 const handleContinue = () => {
 if (!store.sessionId) {
 toast.error("Session not found");
 return;
 }

 // Clear previous errors
 setFieldErrors({});
 setShowValidationErrors(false);

 // Validate form and get specific field errors
 const errors = validateForm();
 
 if (Object.keys(errors).length > 0) {
 setFieldErrors(errors);
 setShowValidationErrors(true);
 
 // Get first error field for scrolling
 const firstErrorField = Object.keys(errors)[0];
 const fieldDisplayNames: Record<string, string> = {
 name: "drug name",
 condition: "medical condition",
 medicalProblem: "medical problem description", 
 activeIngredients: "active ingredients"
 };
 
 // Show toast with count of errors
 const errorCount = Object.keys(errors).length;
 const firstFieldName = firstErrorField ? (fieldDisplayNames[firstErrorField] || firstErrorField) : "required field";
 
 if (errorCount === 1) {
 toast.error(`Please fill in the ${firstFieldName}`);
 } else {
 toast.error(`Please fill in ${errorCount} required fields. Starting with ${firstFieldName}.`);
 }
 
 // Scroll to first error
 if (firstErrorField) {
 scrollToField(firstErrorField);
 }
 return;
 }

 // Helper function to identify blank fields that will be filled with TBD
 const getBlankFields = (data: any) => {
 const fieldLabels = {
 mechanism: "Mechanism of Action",
 class: "Drug Class",
 comparisonToExistingTreatments: "Comparison to Existing Treatments",
 regulatoryStatus: "Regulatory Status Details",
 ingredientRationale: "Formulation Rationale",
 preclinicalStudies: "Preclinical Studies",
 toxicityStudies: "Toxicity Studies",
 clinicalTrialsHistory: "Previous Clinical Trials",
 keyFindings: "Key Safety & Efficacy Findings",
 pregnancySafetyDetails: "Pregnancy Safety Details",
 fdaApprovalStatus: "FDA Approval Status",
 drugCompoundNumber: "Drug Compound Number",
 indApplicationNumber: "IND Application Number",
 };

 const blankFields: string[] = [];
 Object.keys(fieldLabels).forEach(key => {
 if (!data[key] || data[key].trim() === "") {
 blankFields.push(fieldLabels[key as keyof typeof fieldLabels]);
 }
 });

 return blankFields;
 };

 // Check for blank fields that will be filled with TBD
 const blankFields = getBlankFields(formData);
 
 if (blankFields.length > 0) {
 setBlankFieldsList(blankFields);
 setShowTBDConfirmation(true);
 return;
 }

 // If no blank fields, proceed directly
 proceedWithSave();
 };

 // Helper function to fill empty fields with "TBD"
 const fillEmptyFieldsWithTBD = (data: any) => ({
 ...data,
 mechanism: data.mechanism || "TBD",
 class: data.class || "TBD",
 comparisonToExistingTreatments: data.comparisonToExistingTreatments || "TBD",
 regulatoryStatus: data.regulatoryStatus || "TBD",
 ingredientRationale: data.ingredientRationale || "TBD",
 preclinicalStudies: data.preclinicalStudies || "TBD",
 toxicityStudies: data.toxicityStudies || "TBD",
 clinicalTrialsHistory: data.clinicalTrialsHistory || "TBD",
 keyFindings: data.keyFindings || "TBD",
 pregnancySafetyDetails: data.pregnancySafetyDetails || "TBD",
 fdaApprovalStatus: data.fdaApprovalStatus || "TBD",
 drugCompoundNumber: data.drugCompoundNumber || "TBD",
 indApplicationNumber: data.indApplicationNumber || "TBD",
 });

 const proceedWithSave = () => {
 // Close confirmation dialog
 setShowTBDConfirmation(false);
 
 // Fill empty fields before saving
 const processedFormData = fillEmptyFieldsWithTBD(formData);

 // Update store with comprehensive intervention data
 store.updateDiscovery({ 
 intervention: {
 // Basic information
 name: processedFormData.name,
 category: processedFormData.category,
 mechanism: processedFormData.mechanism,
 class: processedFormData.class,
 isNewCompound: processedFormData.isNewCompound,
 
 // Enhanced drug product information
 medicalProblem: processedFormData.medicalProblem,
 comparisonToExistingTreatments: processedFormData.comparisonToExistingTreatments,
 regulatoryStatus: processedFormData.regulatoryStatus,
 activeIngredients: processedFormData.activeIngredients,
 inactiveIngredients: processedFormData.inactiveIngredients,
 ingredientRationale: processedFormData.ingredientRationale,
 preclinicalStudies: processedFormData.preclinicalStudies,
 toxicityStudies: processedFormData.toxicityStudies,
 clinicalTrialsHistory: processedFormData.clinicalTrialsHistory,
 keyFindings: processedFormData.keyFindings,
 pregnancySafety: processedFormData.pregnancySafety,
 pregnancySafetyDetails: processedFormData.pregnancySafetyDetails,
 fdaApprovalStatus: processedFormData.fdaApprovalStatus,
 drugCompoundNumber: processedFormData.drugCompoundNumber,
 indApplicationNumber: processedFormData.indApplicationNumber,
 },
 condition: processedFormData.condition,
 });

 saveDiscovery.mutate({
 sessionId: store.sessionId!,
 data: {
 intervention: {
 name: processedFormData.name,
 category: processedFormData.category,
 mechanism: processedFormData.mechanism,
 class: processedFormData.class,
 isNewCompound: processedFormData.isNewCompound,
 medicalProblem: processedFormData.medicalProblem,
 comparisonToExistingTreatments: processedFormData.comparisonToExistingTreatments,
 regulatoryStatus: processedFormData.regulatoryStatus,
 activeIngredients: processedFormData.activeIngredients,
 inactiveIngredients: processedFormData.inactiveIngredients,
 ingredientRationale: processedFormData.ingredientRationale,
 preclinicalStudies: processedFormData.preclinicalStudies,
 toxicityStudies: processedFormData.toxicityStudies,
 clinicalTrialsHistory: processedFormData.clinicalTrialsHistory,
 keyFindings: processedFormData.keyFindings,
 pregnancySafety: processedFormData.pregnancySafety,
 pregnancySafetyDetails: processedFormData.pregnancySafetyDetails,
 fdaApprovalStatus: processedFormData.fdaApprovalStatus,
 drugCompoundNumber: processedFormData.drugCompoundNumber,
 indApplicationNumber: processedFormData.indApplicationNumber,
 },
 condition: processedFormData.condition,
 }
 });
 };

 const handleApplySuggestion = (field: string, suggestion: string, actionableData?: any) => {
 if (field === "competitive-landscape") {
 if (actionableData) {
 const updates: any = {};
 
 if (actionableData.field === 'medicalProblem') {
 updates.medicalProblem = actionableData.value;
 toast.success("Medical problem description updated");
 }
 
 if (actionableData.field === 'comparisonToExistingTreatments') {
 updates.comparisonToExistingTreatments = actionableData.value;
 toast.success("Competitive analysis updated");
 }
 
 // Handle combined data with multiple insights
 if (actionableData.epidemiology && actionableData.field === 'medicalProblem') {
 // Enhance medical problem with epidemiology data
 let enhancedDescription = `${actionableData.value}\n\nDisease Burden: ${actionableData.epidemiology}`;
 if (actionableData.unmetNeed) {
 enhancedDescription += `\n\nUnmet Medical Need: ${actionableData.unmetNeed}`;
 }
 updates.medicalProblem = enhancedDescription;
 }
 
 if (Object.keys(updates).length > 0) {
 setFormData(prev => ({ ...prev, ...updates }));
 }
 }
 return;
 }
 };

 return (
 <div className="space-y-8">
 <BlurFade delay={0.01} inView>
 <div>
 <h1 className="text-3xl font-bold text-gray-900">
 Investigational Product
 </h1>
 <p className="mt-2 text-lg text-gray-600">
 Provide comprehensive information about your investigational drug product
 </p>
 </div>
 </BlurFade>

 {/* Basic Drug Information */}
 <BlurFade delay={0.02} inView>
 <Card>
 <CardHeader>
 <CardTitle className="flex items-center gap-2">
 <Pill className="h-5 w-5" />
 Basic Drug Information
 </CardTitle>
 <CardDescription>
 Fundamental details about your investigational product
 </CardDescription>
 </CardHeader>
 <CardContent className="space-y-4">
 <div className="grid grid-cols-2 gap-4">
 <div className="space-y-2">
 <Label htmlFor="name" className={`flex items-center gap-1 ${getLabelErrorClasses("name")}`}>
 Drug Name *
 {hasFieldError("name") && <AlertCircle className="h-4 w-4" />}
 </Label>
 <Input
 id="name"
 placeholder="e.g., ABC-123, Compound-XYZ"
 value={formData.name}
 onChange={(e) => {
 setFormData({ ...formData, name: e.target.value });
 if (e.target.value.trim()) {
 clearFieldError("name");
 }
 }}
 className={getInputErrorClasses("name")}
 />
 {getFieldError("name") && (
 <p className="text-sm text-red-600 flex items-center gap-1">
 <AlertCircle className="h-3 w-3" />
 {getFieldError("name")}
 </p>
 )}
 </div>
 <div className="space-y-2">
 <Label htmlFor="condition" className={`flex items-center gap-1 ${getLabelErrorClasses("condition")}`}>
 Medical Condition(s) or Disease(s) Targeted *
 {hasFieldError("condition") && <AlertCircle className="h-4 w-4" />}
 </Label>
 <Input
 id="condition"
 placeholder="e.g., Major Depressive Disorder, Type 2 Diabetes"
 value={formData.condition}
 onChange={(e) => {
 setFormData({ ...formData, condition: e.target.value });
 if (e.target.value.trim()) {
 clearFieldError("condition");
 }
 }}
 className={getInputErrorClasses("condition")}
 />
 {getFieldError("condition") && (
 <p className="text-sm text-red-600 flex items-center gap-1">
 <AlertCircle className="h-3 w-3" />
 {getFieldError("condition")}
 </p>
 )}
 </div>
 </div>

 <div className="grid grid-cols-2 gap-4">
 <div className="space-y-2">
 <Label htmlFor="class">Drug Class</Label>
 <Input
 id="class"
 placeholder="e.g., GLP-1 receptor agonist, SSRI"
 value={formData.class}
 onChange={(e) => setFormData({ ...formData, class: e.target.value })}
 />
 </div>
 <div className="space-y-2">
 <Label htmlFor="category">Route/Formulation</Label>
 <Select
 value={formData.category}
 onValueChange={(value) => setFormData({ ...formData, category: value })}
 >
 <SelectTrigger id="category">
 <SelectValue placeholder="Select route/formulation" />
 </SelectTrigger>
 <SelectContent>
 <SelectItem value="oral">Oral</SelectItem>
 <SelectItem value="injectable">Injectable</SelectItem>
 <SelectItem value="topical">Topical</SelectItem>
 <SelectItem value="inhalation">Inhalation</SelectItem>
 <SelectItem value="other">Other</SelectItem>
 </SelectContent>
 </Select>
 </div>
 </div>

 <div className="grid grid-cols-2 gap-4">
 <div className="space-y-2">
 <Label htmlFor="isNewCompound">Novel compound (never tested in humans)</Label>
 <Select 
 value={formData.isNewCompound ? "yes" : "no"}
 onValueChange={(value) => setFormData({ ...formData, isNewCompound: value === "yes" })}
 >
 <SelectTrigger id="isNewCompound">
 <SelectValue placeholder="Select yes or no" />
 </SelectTrigger>
 <SelectContent>
 <SelectItem value="yes">Yes</SelectItem>
 <SelectItem value="no">No</SelectItem>
 </SelectContent>
 </Select>
 <p className="text-xs text-gray-600">
 Select if this is a completely new chemical entity
 </p>
 </div>
 <div className="space-y-2">
 {/* Empty column for now */}
 </div>
 </div>

 <div className="space-y-2">
 <Label htmlFor="mechanism">Mechanism of Action</Label>
 <Textarea
 id="mechanism"
 placeholder="Describe mechanism of action..."
 rows={3}
 value={formData.mechanism}
 onChange={(e) => setFormData({ ...formData, mechanism: e.target.value })}
 />
 </div>
 </CardContent>
 </Card>
 </BlurFade>

 {/* Medical Problem & Competitive Landscape */}
 <BlurFade delay={0.03} inView>
 <Card>
 <CardHeader>
 <div className="flex items-center justify-between">
 <div>
 <CardTitle className="flex items-center gap-2">
 <Heart className="h-5 w-5" />
 Medical Problem & Treatment Landscape
 </CardTitle>
 <CardDescription>
 Describe the medical need and how this drug compares to existing treatments
 </CardDescription>
 </div>
 <InsightsButton
 onClick={() => handleGetInsights("competitive-landscape")}
 onRefresh={() => handleGetInsights("competitive-landscape", true)}
 loading={queryInsights.isPending && queryInsights.variables?.field === "competitive-landscape"}
 hasCachedData={!!cachedInsights["competitive-landscape"]}
 showRefresh={!!cachedInsights["competitive-landscape"]}
 />
 </div>
 </CardHeader>
 <CardContent className="space-y-4">
 <div className="space-y-2">
 <Label htmlFor="medicalProblem" className={`flex items-center gap-1 ${getLabelErrorClasses("medicalProblem")}`}>
 Medical Problem Addressed *
 {hasFieldError("medicalProblem") && <AlertCircle className="h-4 w-4" />}
 </Label>
 <Textarea
 id="medicalProblem"
 placeholder="Describe the medical condition, unmet need, and patient impact..."
 rows={4}
 value={formData.medicalProblem}
 onChange={(e) => {
 setFormData({ ...formData, medicalProblem: e.target.value });
 if (e.target.value.trim()) {
 clearFieldError("medicalProblem");
 }
 }}
 className={getInputErrorClasses("medicalProblem")}
 />
 {getFieldError("medicalProblem") && (
 <p className="text-sm text-red-600 flex items-center gap-1">
 <AlertCircle className="h-3 w-3" />
 {getFieldError("medicalProblem")}
 </p>
 )}
 </div>

 <div className="space-y-2">
 <Label htmlFor="comparisonToExistingTreatments">Comparison to Existing Treatments</Label>
 <Textarea
 id="comparisonToExistingTreatments"
 placeholder="Compare to existing treatments..."
 rows={4}
 value={formData.comparisonToExistingTreatments}
 onChange={(e) => setFormData({ ...formData, comparisonToExistingTreatments: e.target.value })}
 />
 </div>
 </CardContent>
 </Card>
 </BlurFade>

 {/* Regulatory Status */}
 <BlurFade delay={0.04} inView>
 <Card>
 <CardHeader>
 <CardTitle className="flex items-center gap-2">
 <ShieldCheck className="h-5 w-5" />
 Regulatory Status & Approvals
 </CardTitle>
 <CardDescription>
 Current regulatory status and approval pathways
 </CardDescription>
 </CardHeader>
 <CardContent className="space-y-4">
 <div className="grid grid-cols-2 gap-4">
 <div className="space-y-2">
 <Label htmlFor="fdaApprovalStatus">FDA Approval Status *</Label>
 <Select
 value={formData.fdaApprovalStatus}
 onValueChange={(value) => setFormData({ ...formData, fdaApprovalStatus: value as any })}
 >
 <SelectTrigger id="fdaApprovalStatus">
 <SelectValue placeholder="Make your selection" />
 </SelectTrigger>
 <SelectContent>
 <SelectItem value="approved">FDA Approved for this indication</SelectItem>
 <SelectItem value="investigational">Investigational (IND)</SelectItem>
 <SelectItem value="compassionate">Compassionate use</SelectItem>
 <SelectItem value="off-label">Off-label use of approved drug</SelectItem>
 </SelectContent>
 </Select>
 </div>
 
 <div className="space-y-2">
 <Label htmlFor="drugCompoundNumber">Drug Compound Number</Label>
 <Input
 id="drugCompoundNumber"
 placeholder="Add compound number (e.g., ABC-123)"
 value={formData.drugCompoundNumber}
 onChange={(e) => setFormData({ ...formData, drugCompoundNumber: e.target.value })}
 />
 </div>
 </div>

 <div className="grid grid-cols-2 gap-4">
 <div className="space-y-2">
 <Label htmlFor="indApplicationNumber">IND Application Number</Label>
 <Input
 id="indApplicationNumber"
 placeholder="Add IND number (e.g., IND-123456)"
 value={formData.indApplicationNumber}
 onChange={(e) => setFormData({ ...formData, indApplicationNumber: e.target.value })}
 />
 </div>
 </div>

 <div className="space-y-2">
 <Label htmlFor="regulatoryStatus">Regulatory Status Details</Label>
 <Textarea
 id="regulatoryStatus"
 placeholder="Add regulatory status details..."
 rows={3}
 value={formData.regulatoryStatus}
 onChange={(e) => setFormData({ ...formData, regulatoryStatus: e.target.value })}
 />
 </div>
 </CardContent>
 </Card>
 </BlurFade>

 {/* Drug Composition */}
 <BlurFade delay={0.05} inView>
 <Card>
 <CardHeader>
 <CardTitle className="flex items-center gap-2">
 <Beaker className="h-5 w-5" />
 Drug Composition
 </CardTitle>
 <CardDescription>
 Active and inactive ingredients in the investigational product
 </CardDescription>
 </CardHeader>
 <CardContent className="space-y-4">
 {/* Active Ingredients */}
 <div className="space-y-3">
 <Label className={`flex items-center gap-1 ${getLabelErrorClasses("activeIngredients")}`}>
 Active Ingredients *
 {hasFieldError("activeIngredients") && <AlertCircle className="h-4 w-4" />}
 </Label>
 {formData.activeIngredients.map((ingredient: string, index: number) => (
 <div key={index} className="flex items-center gap-2 rounded-lg border p-3">
 <TestTube className="h-4 w-4 text-blue-500" />
 <span className="flex-1 text-sm">{ingredient}</span>
 <Button
 size="icon"
 variant="ghost"
 onClick={() => removeActiveIngredient(index)}
 >
 <X className="h-4 w-4" />
 </Button>
 </div>
 ))}
 
 <div className="flex gap-2">
 <Input
 id="activeIngredients"
 placeholder="Add active ingredient..."
 value={newActiveIngredient}
 onChange={(e) => setNewActiveIngredient(e.target.value)}
 onKeyDown={(e) => e.key === "Enter" && (e.preventDefault(), addActiveIngredient())}
 className={getInputErrorClasses("activeIngredients")}
 />
 <Button onClick={addActiveIngredient} size="icon">
 <Plus className="h-4 w-4" />
 </Button>
 </div>
 {getFieldError("activeIngredients") && (
 <p className="text-sm text-red-600 flex items-center gap-1">
 <AlertCircle className="h-3 w-3" />
 {getFieldError("activeIngredients")}
 </p>
 )}
 </div>

 {/* Inactive Ingredients */}
 <div className="space-y-3">
 <Label>Inactive Ingredients (Excipients)</Label>
 {formData.inactiveIngredients.map((ingredient: string, index: number) => (
 <div key={index} className="flex items-center gap-2 rounded-lg border p-3">
 <span className="flex-1 text-sm">{ingredient}</span>
 <Button
 size="icon"
 variant="ghost"
 onClick={() => removeInactiveIngredient(index)}
 >
 <X className="h-4 w-4" />
 </Button>
 </div>
 ))}
 
 <div className="flex gap-2">
 <Input
 placeholder="Add inactive ingredient..."
 value={newInactiveIngredient}
 onChange={(e) => setNewInactiveIngredient(e.target.value)}
 onKeyDown={(e) => e.key === "Enter" && (e.preventDefault(), addInactiveIngredient())}
 />
 <Button onClick={addInactiveIngredient} size="icon">
 <Plus className="h-4 w-4" />
 </Button>
 </div>
 </div>

 <div className="space-y-2">
 <Label htmlFor="ingredientRationale">Formulation Rationale</Label>
 <Textarea
 id="ingredientRationale"
 placeholder="Add formulation rationale..."
 rows={3}
 value={formData.ingredientRationale}
 onChange={(e) => setFormData({ ...formData, ingredientRationale: e.target.value })}
 />
 </div>
 </CardContent>
 </Card>
 </BlurFade>

 {/* Preclinical & Clinical History */}
 <BlurFade delay={0.06} inView>
 <Card>
 <CardHeader>
 <CardTitle className="flex items-center gap-2">
 <Microscope className="h-5 w-5" />
 Preclinical & Clinical History
 </CardTitle>
 <CardDescription>
 Studies conducted to support the investigational product
 </CardDescription>
 </CardHeader>
 <CardContent className="space-y-4">
 <div className="space-y-2">
 <Label htmlFor="preclinicalStudies">Preclinical Studies</Label>
 <Textarea
 id="preclinicalStudies"
 placeholder="Describe preclinical studies..."
 rows={4}
 value={formData.preclinicalStudies}
 onChange={(e) => setFormData({ ...formData, preclinicalStudies: e.target.value })}
 />
 </div>

 <div className="space-y-2">
 <Label htmlFor="toxicityStudies">Toxicity Studies & Findings</Label>
 <Textarea
 id="toxicityStudies"
 placeholder="Describe toxicity studies..."
 rows={4}
 value={formData.toxicityStudies}
 onChange={(e) => setFormData({ ...formData, toxicityStudies: e.target.value })}
 />
 </div>

 <div className="space-y-2">
 <Label htmlFor="clinicalTrialsHistory">Previous Clinical Trials</Label>
 <Textarea
 id="clinicalTrialsHistory"
 placeholder="List previous clinical trials..."
 rows={4}
 value={formData.clinicalTrialsHistory}
 onChange={(e) => setFormData({ ...formData, clinicalTrialsHistory: e.target.value })}
 />
 </div>

 <div className="space-y-2">
 <Label htmlFor="keyFindings">Key Safety & Efficacy Findings</Label>
 <Textarea
 id="keyFindings"
 placeholder="Summarize key findings..."
 rows={4}
 value={formData.keyFindings}
 onChange={(e) => setFormData({ ...formData, keyFindings: e.target.value })}
 />
 </div>
 </CardContent>
 </Card>
 </BlurFade>

 {/* Pregnancy & Special Populations */}
 <BlurFade delay={0.07} inView>
 <Card>
 <CardHeader>
 <CardTitle className="flex items-center gap-2">
 <AlertTriangle className="h-5 w-5" />
 Pregnancy & Special Population Safety
 </CardTitle>
 <CardDescription>
 Safety considerations for pregnancy and special populations
 </CardDescription>
 </CardHeader>
 <CardContent className="space-y-4">
 <div className="space-y-2">
 <Label htmlFor="pregnancySafety">Pregnancy Safety Classification</Label>
 <Select
 value={formData.pregnancySafety}
 onValueChange={(value) => setFormData({ ...formData, pregnancySafety: value as any })}
 >
 <SelectTrigger id="pregnancySafety">
 <SelectValue placeholder="Select pregnancy safety" />
 </SelectTrigger>
 <SelectContent>
 <SelectItem value="safe">Safe for use during pregnancy</SelectItem>
 <SelectItem value="unsafe">Unsafe/contraindicated in pregnancy</SelectItem>
 <SelectItem value="unknown">Safety unknown</SelectItem>
 <SelectItem value="contraindicated">Contraindicated in pregnancy</SelectItem>
 </SelectContent>
 </Select>
 </div>

 <div className="space-y-2">
 <Label htmlFor="pregnancySafetyDetails">Pregnancy Safety Details</Label>
 <Textarea
 id="pregnancySafetyDetails"
 placeholder="Add pregnancy safety details..."
 rows={4}
 value={formData.pregnancySafetyDetails}
 onChange={(e) => setFormData({ ...formData, pregnancySafetyDetails: e.target.value })}
 />
 </div>
 </CardContent>
 </Card>
 </BlurFade>

 {/* Navigation */}
 <BlurFade delay={0.08} inView>
 <div className="flex justify-between pt-6">
 <Button
 variant="outline"
 onClick={() => router.push("/study/new/study-overview")}
 >
 <ChevronLeft className="mr-2 h-4 w-4" />
 Back to Study Overview
 </Button>
 <Button
 onClick={handleContinue}
 disabled={saveDiscovery.isPending}
 >
 {saveDiscovery.isPending ? "Saving..." : "Continue to Study Design"}
 </Button>
 </div>
 </BlurFade>

 {/* Insights Panel */}
 {activeInsightsPanel && (
 <InsightsPanelPortal
 isOpen={true}
 onClose={() => setActiveInsightsPanel(null)}
 title={`Insights: ${activeInsightsPanel.replace("-", " ").replace(/\b\w/g, l => l.toUpperCase())}`}
 description="Recommendations based on similar studies"
 loading={queryInsights.isPending}
 sections={insightsData[activeInsightsPanel]?.sections || cachedInsights[activeInsightsPanel]?.sections || []}
 sources={insightsData[activeInsightsPanel]?.sources || cachedInsights[activeInsightsPanel]?.sources || []}
 progressStatus={insightsData[activeInsightsPanel]?.progressStatus}
 progressMessages={insightsData[activeInsightsPanel]?.progressMessages}
 onDocumentClick={(url) => setDocumentViewerUrl(url)}
 onApplySuggestion={(suggestion, actionableData) => handleApplySuggestion(activeInsightsPanel, suggestion, actionableData)}
 />
 )}

 {/* Document Viewer */}
 {documentViewerUrl && (
 <DocumentViewerPortal
 isOpen={true}
 onClose={() => setDocumentViewerUrl(null)}
 documentUrl={documentViewerUrl}
 loading={false}
 citations={(() => {
 // Extract all unique citations from all sections
 const allCitations = new Map<string, any>();
 Object.values(insightsData).forEach(data => {
 data.sections?.forEach((section: any) => {
 if (section.citations && Array.isArray(section.citations)) {
 section.citations.forEach((citation: any) => {
 if (citation.id && !allCitations.has(citation.id)) {
 allCitations.set(citation.id, citation);
 }
 });
 }
 });
 });
 return Array.from(allCitations.values());
 })()}
 sources={(() => {
 // Extract all unique sources from all insights data
 const allSources = new Map<string, any>();
 Object.values(insightsData).forEach(data => {
 data.sources?.forEach((source: any) => {
 if (source.nctId && !allSources.has(source.nctId)) {
 allSources.set(source.nctId, source);
 }
 });
 });
 return Array.from(allSources.values());
 })()}
 onStudySelect={(studyUrl) => setDocumentViewerUrl(studyUrl)}
 currentStudyId={(() => {
 // Extract NCT ID from the current document URL
 if (!documentViewerUrl) return undefined;
 const match = documentViewerUrl.match(/NCT\d+/i);
 return match ? match[0] : undefined;
 })()}
 />
 )}

 {/* TBD Confirmation Dialog */}
 <TBDConfirmationPortal
 isOpen={showTBDConfirmation}
 onClose={() => setShowTBDConfirmation(false)}
 onConfirm={proceedWithSave}
 blankFields={blankFieldsList}
 isLoading={saveDiscovery.isPending}
 />
 </div>
 );
}