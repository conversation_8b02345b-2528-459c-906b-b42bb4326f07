"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { useTrialDesignStore } from "~/store/trial-design";
import { StudyTypeSelector } from "~/components/wizard/StudyTypeSelector";
import { But<PERSON> } from "~/components/ui/button";
import { BlurFade } from "~/components/ui/blur-fade";
import { api } from "~/trpc/react";
import { toast } from "sonner";
import type { StudyType } from "~/types/trial-design";

export default function StudyTypePage() {
 const router = useRouter();
 const { data: session, status } = useSession();
 const store = useTrialDesignStore();
 const [selectedType, setSelectedType] = useState<StudyType | null>(
 store.discovery.studyType
 );

 // Show loading state while checking authentication
 if (status === "loading") {
   return (
     <div className="flex items-center justify-center min-h-[400px]">
       <div className="text-lg text-gray-600">Loading...</div>
     </div>
   );
 }

 // This should be handled by middleware, but add extra safety check
 if (status === "unauthenticated") {
   router.push('/auth/signin');
   return null;
 }

 // Create session mutation
 const createSession = api.studyDesign.createSession.useMutation({
 onSuccess: (data) => {
 console.log('Session created:', data.sessionId);
 store.setSessionId(data.sessionId);
 toast.success("Session created successfully");
 },
 onError: (error) => {
 console.error('Failed to create session:', error);
 toast.error("Failed to create session: " + error.message);
 },
 });

 // Save discovery mutation
 const saveDiscovery = api.studyDesign.saveDiscovery.useMutation({
 onSuccess: () => {
 store.markStepCompleted("study-overview");
 router.push("/study/new/investigational-product");
 },
 onError: (error) => {
 toast.error("Failed to save: " + error.message);
 // If session not found, create a new one
 if (error.message.includes("Session not found")) {
 store.resetSession();
 createSession.mutate({});
 }
 },
 });

 // Check session validity
 const { data: sessionData } = api.studyDesign.getSession.useQuery(
 { sessionId: store.sessionId! },
 { 
 enabled: !!store.sessionId,
 retry: false,
 refetchOnWindowFocus: false,
 }
 );
 
 // Handle missing session data (null response means session not found/expired)
 useEffect(() => {
 if (store.sessionId && sessionData === null && !createSession.isPending) {
 console.log('Session not found, creating new one');
 store.resetSession();
 createSession.mutate({});
 }
 }, [sessionData, store.sessionId, createSession.isPending]);

 // Initialize session on mount (only once)
 useEffect(() => {
 if (!store.sessionId && !createSession.isPending) {
 console.log('Initializing new session');
 createSession.mutate({});
 }
 }, []); // Empty dependency array - only run once on mount

 const handleContinue = async () => {
 if (!selectedType) {
 toast.error("Please select a study type");
 return;
 }

 if (!store.sessionId) {
 toast.error("Session not initialized");
 return;
 }

 // Update store
 store.updateDiscovery({ studyType: selectedType });

 // Save to backend
 saveDiscovery.mutate({
 sessionId: store.sessionId,
 data: {
 studyType: selectedType,
 },
 });
 };

 return (
 <div className="space-y-8">
 <BlurFade delay={0.01} inView>
 <div>
 <h1 className="text-3xl font-bold text-gray-900">
 Study Overview & Background
 </h1>
 <p className="mt-2 text-lg text-gray-600">
 Begin by defining your study type and basic protocol information
 </p>
 </div>
 </BlurFade>

 <BlurFade delay={0.02} inView>
 <div className="space-y-6">
 <div>
 <h2 className="text-lg font-semibold text-gray-900 mb-4">Study Type Selection</h2>
 <StudyTypeSelector
 value={selectedType}
 onChange={setSelectedType}
 />
 </div>
 </div>
 </BlurFade>

 <BlurFade delay={0.03} inView>
 <div className="flex justify-between pt-6">
 <Button
 variant="outline"
 onClick={() => router.push("/dashboard")}
 >
 Cancel
 </Button>
 <Button
 onClick={handleContinue}
 disabled={!selectedType || saveDiscovery.isPending}
 >
 {saveDiscovery.isPending ? "Saving..." : "Continue"}
 </Button>
 </div>
 </BlurFade>
 </div>
 );
}