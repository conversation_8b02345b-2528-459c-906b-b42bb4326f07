"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useTrialDesignStore } from "~/store/trial-design";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { BlurFade } from "~/components/ui/blur-fade";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { InsightsButton } from "~/components/insights/InsightsButton";
import { InsightsPanelPortal } from "~/components/insights/InsightsPanelPortal";
import { DocumentViewerPortal } from "~/components/insights/DocumentViewerPortal";
import { TBDConfirmationPortal } from "~/components/ui/tbd-confirmation-portal";
import { api } from "~/trpc/react";
import { toast } from "sonner";
import { 
 ChevronLeft, 
 Shield,
 DollarSign,
 Building2,
 Users,
 UserCheck,
 FileText,
 Gavel,
 CheckCircle,
 Plus,
 X
} from "lucide-react";

export default function RegulatoryFinancialLegalPage() {
 const router = useRouter();
 const store = useTrialDesignStore();
 
 const [errors, setErrors] = useState<Record<string, boolean>>({});
 const [showTBDConfirmation, setShowTBDConfirmation] = useState(false);
 const [blankFieldsList, setBlankFieldsList] = useState<string[]>([]);
 
 const [formData, setFormData] = useState({
 // IRB & Ethics Committee (1 question)
 irbName: store.discovery.regulatory?.irbName || "",
 
 // Sponsor & Manufacturers (3 questions)
 sponsorName: store.discovery.regulatory?.sponsorName || "",
 drugManufacturerName: store.discovery.regulatory?.drugManufacturerName || "",
 deviceManufacturerName: store.discovery.regulatory?.deviceManufacturerName || "",
 
 // CRO Information (2 questions)
 willUseCRO: store.discovery.regulatory?.willUseCRO ?? false,
 croName: store.discovery.regulatory?.croName || "",
 croAddress: store.discovery.regulatory?.croAddress || "",
 croContact: store.discovery.regulatory?.croContact || "",
 
 // Data Evaluation Committees (2 questions)
 dataEvaluationCommittees: store.discovery.regulatory?.dataEvaluationCommittees || [],
 independentCommittees: store.discovery.regulatory?.independentCommittees || [],
 
 // Financial (2 questions + expanded compensation fields)
 willParticipantsBeCompensated: store.discovery.regulatory?.willParticipantsBeCompensated ?? true,
 compensationDetails: store.discovery.regulatory?.compensationDetails || "",
 perVisitCompensation: store.discovery.regulatory?.perVisitCompensation || "",
 completionBonus: store.discovery.regulatory?.completionBonus || "",
 paymentSchedule: store.discovery.regulatory?.paymentSchedule || "",
 mileageRate: store.discovery.regulatory?.mileageRate || "",
 parkingReimbursement: store.discovery.regulatory?.parkingReimbursement || "",
 publicTransportCap: store.discovery.regulatory?.publicTransportCap || "",
 hotelCap: store.discovery.regulatory?.hotelCap || "",
 mealAllowance: store.discovery.regulatory?.mealAllowance || "",
 
 // Billing Scenarios - convert from array to individual Yes/No fields
 sponsorPaysNonStandardCare: store.discovery.regulatory?.sponsorPaysNonStandardCare || "",
 freeDrugDeviceProvided: store.discovery.regulatory?.freeDrugDeviceProvided || "",
 standardCareChargedToParticipant: store.discovery.regulatory?.standardCareChargedToParticipant || "",
 noBillableProcedures: store.discovery.regulatory?.noBillableProcedures || "",
 });

 const [newDataEvaluationCommittee, setNewDataEvaluationCommittee] = useState("");
 const [newIndependentCommittee, setNewIndependentCommittee] = useState("");
 const [activeInsightsPanel, setActiveInsightsPanel] = useState<string | null>(null);
 const [insightsData, setInsightsData] = useState<Record<string, { sections: any[]; sources?: any[]; progressStatus?: string; progressMessages?: string[] }>>({});
 const [documentViewerUrl, setDocumentViewerUrl] = useState<string | null>(null);
 
 const cachedInsights = store.insightsCache || {};

 // Helper function to clear error when field is updated
 const handleFieldChange = (fieldName: string, value: any) => {
 setFormData(prev => ({ ...prev, [fieldName]: value }));
 if (errors[fieldName]) {
 setErrors(prev => ({ ...prev, [fieldName]: false }));
 }
 };

 const queryInsights = api.knowledgeBase.queryInsights.useMutation({
 onSuccess: (data, variables) => {
 const insightsPayload = {
 sections: data.sections || [],
 sources: data.sources || [],
 progressStatus: undefined,
 progressMessages: [],
 };
 
 setInsightsData(prev => ({
 ...prev,
 [variables.field]: insightsPayload
 }));
 
 store.cacheInsights(variables.field, insightsPayload);
 setActiveInsightsPanel(variables.field);
 },
 onError: (error) => {
 toast.error("Failed to get insights: " + error.message);
 },
 });

 const saveDiscovery = api.studyDesign.saveDiscovery.useMutation({
 onSuccess: () => {
 store.markStepCompleted("regulatory-financial-legal");
 router.push("/study/new/review");
 },
 onError: (error) => {
 toast.error("Failed to save: " + error.message);
 },
 });

 const handleGetInsights = async (field: string, forceRefresh = false) => {
 if (!forceRefresh && cachedInsights[field]) {
 setActiveInsightsPanel(field);
 return;
 }
 
 setActiveInsightsPanel(field);
 setInsightsData(prev => ({
 ...prev,
 [field]: {
 sections: [],
 sources: [],
 progressStatus: 'Analyzing regulatory requirements...',
 progressMessages: [],
 }
 }));
 
 // Progress updates for financial planning insights
 const progressUpdates = [
 { delay: 0, message: 'Initializing search...' },
 { delay: 1500, message: 'Searching compensation strategies...' },
 { delay: 3500, message: 'Analyzing billing structures...' },
 { delay: 6000, message: 'Extracting financial patterns...' },
 { delay: 8500, message: 'Generating recommendations...' },
 ];
 
 progressUpdates.forEach(({ delay, message }) => {
 setTimeout(() => {
 setInsightsData(prev => {
 const current = prev[field];
 if (current && !current.sections?.length) {
 return {
 ...prev,
 [field]: {
 ...current,
 progressStatus: message,
 progressMessages: [...(current.progressMessages || []), message],
 }
 };
 }
 return prev;
 });
 }, delay);
 });
 
 // Gather comprehensive context for financial planning insights
 const visitCount = store.discovery.timeline?.visits?.length || 0;
 const hasLaboratoryStudies = store.discovery.laboratory?.willCollectBiologicalSamples || false;
 const hasSpecializedStudies = store.discovery.laboratory?.willConductPK || store.discovery.laboratory?.willConductBiomarker || store.discovery.laboratory?.willConductImmunogenicity || store.discovery.laboratory?.willConductGeneticTesting || false;
 
 // Calculate procedure burden based on available data
 let procedureBurden = 'standard';
 if (visitCount > 8 || hasSpecializedStudies) {
   procedureBurden = 'high';
 } else if (visitCount < 4 && !hasLaboratoryStudies) {
   procedureBurden = 'low';
 }
 
 const context = {
 studyType: store.discovery.studyType || undefined,
 condition: store.discovery.condition || undefined,
 phase: store.discovery.phase || undefined,
 geographicScope: store.discovery.population.geographicScope || undefined,
 // Enhanced context for financial planning
 studyDuration: store.discovery.timeline?.totalDuration || undefined,
 treatmentPeriod: store.discovery.timeline?.treatmentPeriod || undefined,
 followupPeriod: store.discovery.timeline?.followUpPeriod || undefined,
 numberOfVisits: visitCount > 0 ? visitCount.toString() : undefined,
 targetEnrollment: store.discovery.population?.targetEnrollment || undefined,
 healthyVolunteers: store.discovery.population?.healthyVolunteers || 'no',
 procedureBurden: procedureBurden,
 };

 const queries: Record<string, string> = {
 "financial-planning": `What are appropriate participant compensation strategies for ${store.discovery.phase || "Phase 2/3"} ${store.discovery.condition || "clinical"} trials with ${visitCount || "multiple"} visits over ${store.discovery.timeline?.totalDuration || "several months"}?`,
 };

 await queryInsights.mutateAsync({
 sessionId: store.sessionId!,
 field,
 context,
 query: queries[field] || "",
 });
 };

 // Array management functions
 const addDataEvaluationCommittee = () => {
 if (newDataEvaluationCommittee.trim()) {
 setFormData(prev => ({
 ...prev,
 dataEvaluationCommittees: [...prev.dataEvaluationCommittees, newDataEvaluationCommittee.trim()]
 }));
 setNewDataEvaluationCommittee("");
 }
 };

 const removeDataEvaluationCommittee = (index: number) => {
 setFormData(prev => ({
 ...prev,
 dataEvaluationCommittees: prev.dataEvaluationCommittees.filter((_: string, i: number) => i !== index)
 }));
 };

 const addIndependentCommittee = () => {
 if (newIndependentCommittee.trim()) {
 setFormData(prev => ({
 ...prev,
 independentCommittees: [...prev.independentCommittees, newIndependentCommittee.trim()]
 }));
 setNewIndependentCommittee("");
 }
 };

 const removeIndependentCommittee = (index: number) => {
 setFormData(prev => ({
 ...prev,
 independentCommittees: prev.independentCommittees.filter((_: string, i: number) => i !== index)
 }));
 };

 const handleBillingScenarioChange = (scenarioKey: string, value: string) => {
 setFormData(prev => ({ ...prev, [scenarioKey]: value }));
 if (errors[scenarioKey]) {
 setErrors(prev => ({ ...prev, [scenarioKey]: false }));
 }
 };

 const billingScenarios = [
 {
 key: 'sponsorPaysNonStandardCare',
 label: 'This study involves tests and procedures that are not considered standard of care. The non-standard care activities will be paid for by the study sponsor.',
 shortLabel: 'Sponsor pays for non-standard care'
 },
 {
 key: 'freeDrugDeviceProvided',
 label: 'Free drug/device provided by sponsor',
 shortLabel: 'Free drug/device from sponsor'
 },
 {
 key: 'standardCareChargedToParticipant',
 label: 'Standard of care tests, visits, and/or procedures will be charged to the participant/insurance as part of normal care.',
 shortLabel: 'Standard care charged to participant/insurance'
 },
 {
 key: 'noBillableProcedures',
 label: 'There are no billable procedures associated with this study.',
 shortLabel: 'No billable procedures'
 }
 ];

 // Helper function to identify blank fields that will be filled with TBD
 const getBlankFields = (data: any) => {
 const fieldLabels: Record<string, string> = {
 drugManufacturerName: "Drug Manufacturer Name",
 deviceManufacturerName: "Device Manufacturer Name",
 croAddress: "CRO Address",
 croContact: "CRO Contact Information",
 };

 const blankFields: string[] = [];
 
 // Check truly optional fields that can be TBD (not validated as required)
 // Only check the relevant manufacturer field based on study type
 if (store.discovery.studyType === "drug" && !data.drugManufacturerName) {
 blankFields.push(fieldLabels.drugManufacturerName!);
 }
 if (store.discovery.studyType === "device" && !data.deviceManufacturerName) {
 blankFields.push(fieldLabels.deviceManufacturerName!);
 }
 
 // Check optional CRO fields (only address and contact are optional when CRO is used)
 // Note: croName is required when CRO is used, so it's not checked here
 if (data.willUseCRO) {
 if (!data.croAddress) blankFields.push(fieldLabels.croAddress!);
 if (!data.croContact) blankFields.push(fieldLabels.croContact!);
 }
 
 // Note: irbName, croName, and compensationDetails are validated as required
 // so they are not included in the TBD check
 
 return blankFields;
 };

 // Helper function to fill empty fields with "TBD"
 const fillEmptyFieldsWithTBD = (data: any) => ({
 ...data,
 // Only fill truly optional fields with TBD
 drugManufacturerName: data.drugManufacturerName || "TBD",
 deviceManufacturerName: data.deviceManufacturerName || "TBD",
 // Only fill optional CRO fields (address and contact) when CRO is used
 croAddress: data.willUseCRO ? (data.croAddress || "TBD") : data.croAddress,
 croContact: data.willUseCRO ? (data.croContact || "TBD") : data.croContact,
 // Note: irbName, croName, and compensationDetails are not filled here
 // because they are required fields that must already have values
 });

 const proceedWithSave = () => {
 // Close confirmation dialog
 setShowTBDConfirmation(false);
 
 // Fill empty fields before saving
 const processedFormData = fillEmptyFieldsWithTBD(formData);
 
 // Convert billing scenarios to array format for storage
 const selectedBillingScenarios = billingScenarios
 .filter(s => processedFormData[s.key as keyof typeof processedFormData] === 'yes')
 .map(s => s.label);
 
 // Update store with comprehensive regulatory data
 store.updateDiscovery({ 
 regulatory: {
 ...processedFormData,
 billingScenarios: selectedBillingScenarios
 }
 });

 saveDiscovery.mutate({
 sessionId: store.sessionId!,
 data: {
 regulatory: {
 ...processedFormData,
 billingScenarios: selectedBillingScenarios
 }
 },
 });
 };

 const handleContinue = () => {
 if (!store.sessionId) {
 toast.error("Session not found");
 return;
 }

 // Comprehensive validation
 const newErrors: Record<string, boolean> = {};
 let hasError = false;
 let firstErrorField: string | null = null;

 // Required field: IRB name (can be TBD but must have something)
 if (!formData.irbName) {
 newErrors.irbName = true;
 hasError = true;
 if (!firstErrorField) firstErrorField = "irbName";
 }

 // Required field: sponsor name (cannot be TBD)
 if (!formData.sponsorName) {
 newErrors.sponsorName = true;
 hasError = true;
 if (!firstErrorField) firstErrorField = "sponsorName";
 }

 // Required: all billing scenarios must be answered
 const billingScenarioFields = ['sponsorPaysNonStandardCare', 'freeDrugDeviceProvided', 'standardCareChargedToParticipant', 'noBillableProcedures'];
 for (const field of billingScenarioFields) {
 if (!formData[field as keyof typeof formData]) {
 newErrors[field] = true;
 hasError = true;
 if (!firstErrorField) firstErrorField = field;
 }
 }

 // Conditional validation: if CRO is used, require CRO details (can be TBD)
 if (formData.willUseCRO && !formData.croName) {
 newErrors.croName = true;
 hasError = true;
 if (!firstErrorField) firstErrorField = "croName";
 }

 // Conditional validation: if compensation is provided, require details (can be TBD)
 if (formData.willParticipantsBeCompensated && !formData.compensationDetails) {
 newErrors.compensationDetails = true;
 hasError = true;
 if (!firstErrorField) firstErrorField = "compensationDetails";
 }

 setErrors(newErrors);

 if (hasError) {
 toast.error("Please complete all required fields");
 
 // Scroll to first error field
 if (firstErrorField) {
 const element = document.getElementById(firstErrorField);
 if (element) {
 element.scrollIntoView({ behavior: "smooth", block: "center" });
 setTimeout(() => {
 element.focus();
 }, 500);
 }
 }
 return;
 }

 // Check for blank fields that will be filled with TBD
 const blankFields = getBlankFields(formData);
 
 if (blankFields.length > 0) {
 setBlankFieldsList(blankFields);
 setShowTBDConfirmation(true);
 return;
 }

 // Convert billing scenarios back to array format for storage
 const selectedBillingScenarios = billingScenarios
 .filter(s => formData[s.key as keyof typeof formData] === 'yes')
 .map(s => s.label);

 // No validation errors and no TBD fields, proceed with save
 // Update store with comprehensive regulatory data
 store.updateDiscovery({ 
 regulatory: {
 irbName: formData.irbName,
 sponsorName: formData.sponsorName,
 drugManufacturerName: formData.drugManufacturerName,
 deviceManufacturerName: formData.deviceManufacturerName,
 willUseCRO: formData.willUseCRO,
 croName: formData.croName,
 croAddress: formData.croAddress,
 croContact: formData.croContact,
 dataEvaluationCommittees: formData.dataEvaluationCommittees,
 independentCommittees: formData.independentCommittees,
 willParticipantsBeCompensated: formData.willParticipantsBeCompensated,
 compensationDetails: formData.compensationDetails,
 // Store individual billing scenario answers for future use
 sponsorPaysNonStandardCare: formData.sponsorPaysNonStandardCare,
 freeDrugDeviceProvided: formData.freeDrugDeviceProvided,
 standardCareChargedToParticipant: formData.standardCareChargedToParticipant,
 noBillableProcedures: formData.noBillableProcedures,
 // Keep legacy array format for backward compatibility
 billingScenarios: selectedBillingScenarios,
 }
 });

 saveDiscovery.mutate({
 sessionId: store.sessionId,
 data: {
 regulatory: {
 irbName: formData.irbName,
 sponsorName: formData.sponsorName,
 drugManufacturerName: formData.drugManufacturerName,
 deviceManufacturerName: formData.deviceManufacturerName,
 willUseCRO: formData.willUseCRO,
 croName: formData.croName,
 croAddress: formData.croAddress,
 croContact: formData.croContact,
 dataEvaluationCommittees: formData.dataEvaluationCommittees,
 independentCommittees: formData.independentCommittees,
 willParticipantsBeCompensated: formData.willParticipantsBeCompensated,
 compensationDetails: formData.compensationDetails,
 // Store individual billing scenario answers
 sponsorPaysNonStandardCare: formData.sponsorPaysNonStandardCare,
 freeDrugDeviceProvided: formData.freeDrugDeviceProvided,
 standardCareChargedToParticipant: formData.standardCareChargedToParticipant,
 noBillableProcedures: formData.noBillableProcedures,
 // Legacy array format
 billingScenarios: selectedBillingScenarios,
 }
 },
 });
 };

 const handleApplySuggestion = (field: string, suggestion: string, actionableData?: any) => {
 if (field === "financial-planning") {
 if (actionableData) {
 const updates: any = {};
 
 // Handle participant compensation fields
 if (actionableData.perVisitCompensation) {
 updates.perVisitCompensation = actionableData.perVisitCompensation;
 }
 if (actionableData.completionBonus) {
 updates.completionBonus = actionableData.completionBonus;
 }
 if (actionableData.paymentSchedule) {
 updates.paymentSchedule = actionableData.paymentSchedule;
 }
 
 // Handle travel reimbursement fields
 if (actionableData.mileageRate) {
 updates.mileageRate = actionableData.mileageRate;
 }
 if (actionableData.parkingReimbursement) {
 updates.parkingReimbursement = actionableData.parkingReimbursement;
 }
 if (actionableData.publicTransportCap) {
 updates.publicTransportCap = actionableData.publicTransportCap;
 }
 if (actionableData.hotelCap) {
 updates.hotelCap = actionableData.hotelCap;
 }
 if (actionableData.mealAllowance) {
 updates.mealAllowance = actionableData.mealAllowance;
 }
 
 // Handle additional support - append to compensationDetails
 if (actionableData.additionalSupport) {
 const currentDetails = formData.compensationDetails;
 const newDetails = currentDetails 
 ? `${currentDetails}\n\n${actionableData.additionalSupport}`
 : actionableData.additionalSupport;
 updates.compensationDetails = newDetails;
 }
 
 // Apply legacy field mappings
 if (actionableData.field === 'compensationDetails') {
 updates.compensationDetails = actionableData.value;
 }
 if (actionableData.field === 'billingScenarios' && actionableData.scenarios) {
 // Handle billing scenarios by setting individual fields to 'yes'
 billingScenarios.forEach(scenario => {
 if (actionableData.scenarios.some((s: string) => s === scenario.label)) {
 updates[scenario.key] = 'yes';
 }
 });
 }
 
 if (Object.keys(updates).length > 0) {
 setFormData(prev => ({ ...prev, ...updates }));
 toast.success("Financial planning recommendations applied");
 store.incrementInsightsCounter();
 }
 }
 return;
 }
 };

 return (
 <div className="space-y-8">
 <BlurFade delay={0.01} inView>
 <div>
 <h1 className="text-3xl font-bold text-gray-900">
 Regulatory, Financial & Legal
 </h1>
 <p className="mt-2 text-lg text-gray-600">
 Define governance framework, oversight committees, and financial arrangements
 </p>
 </div>
 </BlurFade>

 {/* IRB & Ethics Committee */}
 <BlurFade delay={0.02} inView>
 <Card>
 <CardHeader>
 <div className="flex items-center justify-between">
 <div>
 <CardTitle className="flex items-center gap-2">
 <Shield className="h-5 w-5" />
 IRB & Ethics Committee
 </CardTitle>
 <CardDescription>
 Specify the institutional review board or ethics committee overseeing the study
 </CardDescription>
 </div>
 </div>
 </CardHeader>
 <CardContent className="space-y-4">
 <div className="space-y-2">
 <Label htmlFor="irbName">IRB/Ethics Committee Name *</Label>
 <Input
 id="irbName"
 placeholder="e.g., Advarra IRB, University IRB, To be determined"
 value={formData.irbName}
 onChange={(e) => handleFieldChange("irbName", e.target.value)}
 className={errors.irbName ? "border-red-500 focus:ring-red-500" : ""}
 />
 <p className="text-xs text-muted-foreground">
 If not yet determined, enter "To be determined" and complete when details are available
 </p>
 </div>
 </CardContent>
 </Card>
 </BlurFade>

 {/* Sponsors & Manufacturers */}
 <BlurFade delay={0.03} inView>
 <Card>
 <CardHeader>
 <div className="flex items-center justify-between">
 <div>
 <CardTitle className="flex items-center gap-2">
 <Building2 className="h-5 w-5" />
 Sponsors & Manufacturers
 </CardTitle>
 <CardDescription>
 Identify key organizations responsible for the study and products
 </CardDescription>
 </div>
 </div>
 </CardHeader>
 <CardContent className="space-y-4">
 <div className="space-y-2">
 <Label htmlFor="sponsorName">Study Sponsor Name *</Label>
 <Input
 id="sponsorName"
 placeholder="e.g., Acme Pharmaceuticals, Inc., University of Medicine, To be determined"
 value={formData.sponsorName}
 onChange={(e) => handleFieldChange("sponsorName", e.target.value)}
 className={errors.sponsorName ? "border-red-500 focus:ring-red-500" : ""}
 />
 <p className="text-xs text-muted-foreground">
 The organization with primary responsibility for initiating and conducting the clinical investigation
 </p>
 </div>

 {/* Conditionally show manufacturer fields based on study type */}
 {store.discovery.studyType === "drug" && (
 <div className="space-y-2">
 <Label htmlFor="drugManufacturerName">Drug Manufacturer Name & Address</Label>
 <Textarea
 id="drugManufacturerName"
 placeholder="Same as sponsor
Acme Pharmaceuticals, Inc.
123 Main St., Anytown, USA 12345
To be determined"
 rows={3}
 value={formData.drugManufacturerName}
 onChange={(e) => handleFieldChange("drugManufacturerName", e.target.value)}
 className={errors.drugManufacturerName ? "border-red-500 focus:ring-red-500" : ""}
 />
 </div>
 )}
 
 {store.discovery.studyType === "device" && (
 <div className="space-y-2">
 <Label htmlFor="deviceManufacturerName">Device Manufacturer Name & Address</Label>
 <Textarea
 id="deviceManufacturerName"
 placeholder="Same as sponsor
Acme Medical Devices, Inc.
123 Main St., Anytown, USA 12345
To be determined"
 rows={3}
 value={formData.deviceManufacturerName}
 onChange={(e) => handleFieldChange("deviceManufacturerName", e.target.value)}
 className={errors.deviceManufacturerName ? "border-red-500 focus:ring-red-500" : ""}
 />
 </div>
 )}
 
 {/* Show a note for other study types */}
 {store.discovery.studyType && !["drug", "device"].includes(store.discovery.studyType) && (
 <div className="text-sm text-muted-foreground">
 <p>No manufacturer information required for {store.discovery.studyType} studies.</p>
 </div>
 )}
 </CardContent>
 </Card>
 </BlurFade>

 {/* CRO Management */}
 <BlurFade delay={0.04} inView>
 <Card>
 <CardHeader>
 <CardTitle className="flex items-center gap-2">
 <Users className="h-5 w-5" />
 Clinical Research Organization (CRO)
 </CardTitle>
 <CardDescription>
 Specify if a CRO will manage trial operations
 </CardDescription>
 </CardHeader>
 <CardContent className="space-y-4">
 <div className="space-y-2">
 <Label htmlFor="willUseCRO">Will a CRO (Clinical Research Organization) be used?</Label>
 <Select 
 value={formData.willUseCRO ? "yes" : "no"}
 onValueChange={(value) => handleFieldChange("willUseCRO", value === "yes")}
 >
 <SelectTrigger id="willUseCRO" className={errors.willUseCRO ? "border-red-500 focus:ring-red-500" : ""}>
 <SelectValue placeholder="Select yes or no" />
 </SelectTrigger>
 <SelectContent>
 <SelectItem value="yes">Yes</SelectItem>
 <SelectItem value="no">No</SelectItem>
 </SelectContent>
 </Select>
 </div>

 {formData.willUseCRO && (
 <div className="space-y-4">
 <div className="rounded-lg bg-blue-50 p-4">
 <div className="flex items-center gap-2 mb-2">
 <UserCheck className="h-4 w-4 text-blue-600" />
 <h4 className="font-medium text-blue-900 ">CRO Partnership Active</h4>
 </div>
 <p className="text-sm text-blue-800 ">
 Please provide details about the CRO that will manage trial operations.
 </p>
 </div>

 <div className="space-y-2">
 <Label htmlFor="croName">CRO Name</Label>
 <Input
 id="croName"
 placeholder="e.g., Acme Clinical Research, Inc., To be determined"
 value={formData.croName}
 onChange={(e) => handleFieldChange("croName", e.target.value)}
 className={errors.croName ? "border-red-500 focus:ring-red-500" : ""}
 />
 </div>

 <div className="space-y-2">
 <Label htmlFor="croAddress">CRO Address</Label>
 <Textarea
 id="croAddress"
 placeholder="123 Main St.
Anytown, USA 12345"
 rows={2}
 value={formData.croAddress}
 onChange={(e) => handleFieldChange("croAddress", e.target.value)}
 className={errors.croAddress ? "border-red-500 focus:ring-red-500" : ""}
 />
 </div>

 <div className="space-y-2">
 <Label htmlFor="croContact">CRO Contact Information</Label>
 <Textarea
 id="croContact"
 placeholder="(123) 456-7890
<EMAIL>"
 rows={2}
 value={formData.croContact}
 onChange={(e) => handleFieldChange("croContact", e.target.value)}
 className={errors.croContact ? "border-red-500 focus:ring-red-500" : ""}
 />
 </div>
 </div>
 )}
 </CardContent>
 </Card>
 </BlurFade>

 {/* Data Evaluation Committees */}
 <BlurFade delay={0.05} inView>
 <Card>
 <CardHeader>
 <CardTitle className="flex items-center gap-2">
 <FileText className="h-5 w-5" />
 Data Evaluation Committees
 </CardTitle>
 <CardDescription>
 Committees that will review data during the trial
 </CardDescription>
 </CardHeader>
 <CardContent className="space-y-4">
 {formData.dataEvaluationCommittees.map((committee: string, index: number) => (
 <div key={index} className="flex items-center gap-2 rounded-lg border border-purple-200 bg-purple-50 p-3">
 <CheckCircle className="h-4 w-4 text-purple-500" />
 <span className="flex-1 text-sm">{committee}</span>
 <Button
 size="icon"
 variant="ghost"
 onClick={() => removeDataEvaluationCommittee(index)}
 >
 <X className="h-4 w-4" />
 </Button>
 </div>
 ))}
 
 <div className="flex gap-2">
 <Input
 placeholder="Add data evaluation committee (e.g., Data Monitoring Committee, Safety Monitoring Committee)..."
 value={newDataEvaluationCommittee}
 onChange={(e) => setNewDataEvaluationCommittee(e.target.value)}
 onKeyDown={(e) => e.key === "Enter" && (e.preventDefault(), addDataEvaluationCommittee())}
 />
 <Button onClick={addDataEvaluationCommittee} size="icon">
 <Plus className="h-4 w-4" />
 </Button>
 </div>
 
 <div className="rounded-lg bg-gray-50 p-3">
 <p className="text-xs text-muted-foreground">
 <strong>Common committees:</strong> Data Monitoring Committee (DMC), Dose Escalation Committee (DEC), 
 Endpoint Adjudication Committee (EAC), Safety Monitoring Committee (SMC), Efficacy Monitoring Committee, 
 Trial Steering Committee (TSC)
 </p>
 </div>
 </CardContent>
 </Card>
 </BlurFade>

 {/* Independent Committees */}
 <BlurFade delay={0.06} inView>
 <Card>
 <CardHeader>
 <CardTitle className="flex items-center gap-2">
 <Gavel className="h-5 w-5" />
 Independent Committees
 </CardTitle>
 <CardDescription>
 Independent committees involved in the trial (including IRB/EC)
 </CardDescription>
 </CardHeader>
 <CardContent className="space-y-4">
 {formData.independentCommittees.map((committee: string, index: number) => (
 <div key={index} className="flex items-center gap-2 rounded-lg border border-green-200 bg-green-50 p-3">
 <Shield className="h-4 w-4 text-green-500" />
 <span className="flex-1 text-sm">{committee}</span>
 <Button
 size="icon"
 variant="ghost"
 onClick={() => removeIndependentCommittee(index)}
 >
 <X className="h-4 w-4" />
 </Button>
 </div>
 ))}
 
 <div className="flex gap-2">
 <Input
 placeholder="Add independent committee (e.g., University IRB, Ethics Review Board)..."
 value={newIndependentCommittee}
 onChange={(e) => setNewIndependentCommittee(e.target.value)}
 onKeyDown={(e) => e.key === "Enter" && (e.preventDefault(), addIndependentCommittee())}
 />
 <Button onClick={addIndependentCommittee} size="icon">
 <Plus className="h-4 w-4" />
 </Button>
 </div>
 
 <p className="text-xs text-muted-foreground">
 Include the IRB/EC specified above and any other independent oversight committees
 </p>
 </CardContent>
 </Card>
 </BlurFade>

 {/* Financial Arrangements */}
 <BlurFade delay={0.07} inView>
 <Card>
 <CardHeader>
 <div className="flex items-center justify-between">
 <div>
 <CardTitle className="flex items-center gap-2">
 <DollarSign className="h-5 w-5" />
 Financial Arrangements
 </CardTitle>
 <CardDescription>
 Define participant compensation and study costs
 </CardDescription>
 </div>
 <InsightsButton
 onClick={() => handleGetInsights("financial-planning")}
 onRefresh={() => handleGetInsights("financial-planning", true)}
 loading={queryInsights.isPending && queryInsights.variables?.field === "financial-planning"}
 hasCachedData={!!cachedInsights["financial-planning"]}
 showRefresh={!!cachedInsights["financial-planning"]}
 />
 </div>
 </CardHeader>
 <CardContent className="space-y-4">
 {/* Participant Compensation */}
 <div className="space-y-4">
 <div className="space-y-2">
 <Label htmlFor="willParticipantsBeCompensated">Will participants be paid/compensated to participate?</Label>
 <Select 
 value={formData.willParticipantsBeCompensated ? "yes" : "no"}
 onValueChange={(value) => handleFieldChange("willParticipantsBeCompensated", value === "yes")}
 >
 <SelectTrigger id="willParticipantsBeCompensated" className={errors.willParticipantsBeCompensated ? "border-red-500 focus:ring-red-500" : ""}>
 <SelectValue placeholder="Select yes or no" />
 </SelectTrigger>
 <SelectContent>
 <SelectItem value="yes">Yes</SelectItem>
 <SelectItem value="no">No</SelectItem>
 </SelectContent>
 </Select>
 </div>

 {formData.willParticipantsBeCompensated && (
 <>
 {/* Structured Compensation Fields */}
 <div className="grid grid-cols-2 gap-4">
 <div className="space-y-2">
 <Label htmlFor="perVisitCompensation">Per-Visit Compensation ($)</Label>
 <Input
 id="perVisitCompensation"
 type="text"
 placeholder="e.g., 100 or 100-150"
 value={formData.perVisitCompensation}
 onChange={(e) => handleFieldChange("perVisitCompensation", e.target.value)}
 />
 </div>
 
 <div className="space-y-2">
 <Label htmlFor="completionBonus">Completion Bonus ($)</Label>
 <Input
 id="completionBonus"
 type="text"
 placeholder="e.g., 500"
 value={formData.completionBonus}
 onChange={(e) => handleFieldChange("completionBonus", e.target.value)}
 />
 </div>
 
 <div className="space-y-2">
 <Label htmlFor="paymentSchedule">Payment Schedule</Label>
 <Select
 value={formData.paymentSchedule}
 onValueChange={(value) => handleFieldChange("paymentSchedule", value)}
 >
 <SelectTrigger id="paymentSchedule">
 <SelectValue placeholder="Select payment timing" />
 </SelectTrigger>
 <SelectContent>
 <SelectItem value="After each visit">After each visit</SelectItem>
 <SelectItem value="Monthly">Monthly</SelectItem>
 <SelectItem value="Upon completion">Upon completion</SelectItem>
 <SelectItem value="Milestone-based">Milestone-based</SelectItem>
 </SelectContent>
 </Select>
 </div>
 
 <div className="space-y-2">
 <Label htmlFor="mileageRate">Mileage Rate ($/mile)</Label>
 <Input
 id="mileageRate"
 type="text"
 placeholder="e.g., 0.67"
 value={formData.mileageRate}
 onChange={(e) => handleFieldChange("mileageRate", e.target.value)}
 />
 </div>
 
 <div className="space-y-2">
 <Label htmlFor="parkingReimbursement">Parking Reimbursement ($/visit)</Label>
 <Input
 id="parkingReimbursement"
 type="text"
 placeholder="e.g., 20"
 value={formData.parkingReimbursement}
 onChange={(e) => handleFieldChange("parkingReimbursement", e.target.value)}
 />
 </div>
 
 <div className="space-y-2">
 <Label htmlFor="publicTransportCap">Public Transport Cap ($/visit)</Label>
 <Input
 id="publicTransportCap"
 type="text"
 placeholder="e.g., 30"
 value={formData.publicTransportCap}
 onChange={(e) => handleFieldChange("publicTransportCap", e.target.value)}
 />
 </div>
 
 <div className="space-y-2">
 <Label htmlFor="hotelCap">Hotel Cap ($/night)</Label>
 <Input
 id="hotelCap"
 type="text"
 placeholder="e.g., 150"
 value={formData.hotelCap}
 onChange={(e) => handleFieldChange("hotelCap", e.target.value)}
 />
 <p className="text-xs text-muted-foreground">For overnight stays if required</p>
 </div>
 
 <div className="space-y-2">
 <Label htmlFor="mealAllowance">Meal Allowance ($/day)</Label>
 <Input
 id="mealAllowance"
 type="text"
 placeholder="e.g., 50"
 value={formData.mealAllowance}
 onChange={(e) => handleFieldChange("mealAllowance", e.target.value)}
 />
 <p className="text-xs text-muted-foreground">For overnight stays</p>
 </div>
 </div>
 
 {/* Additional Compensation Details */}
 <div className="space-y-2">
 <Label htmlFor="compensationDetails">Additional Compensation Details</Label>
 <Textarea
 id="compensationDetails"
 placeholder="Additional support such as childcare reimbursement, meal allowances, overnight accommodations, etc."
 rows={3}
 value={formData.compensationDetails}
 onChange={(e) => handleFieldChange("compensationDetails", e.target.value)}
 className={errors.compensationDetails ? "border-red-500 focus:ring-red-500" : ""}
 />
 <p className="text-xs text-muted-foreground">
 Specify any additional compensation, support services, or special accommodations
 </p>
 </div>
 </>
 )}
 </div>

 {/* Billing Scenarios */}
 <div className="space-y-4">
 <div>
 <Label>Who will pay for participant tests/visits/products/activities? *</Label>
 <p className="text-sm text-muted-foreground">Answer yes or no for each billing scenario</p>
 </div>
 
 {billingScenarios.map((scenario) => (
 <div key={scenario.key} className="space-y-2">
 <Label htmlFor={scenario.key}>{scenario.label}</Label>
 <Select
 value={formData[scenario.key as keyof typeof formData] as string}
 onValueChange={(value) => handleBillingScenarioChange(scenario.key, value)}
 >
 <SelectTrigger id={scenario.key} className={errors[scenario.key] ? "border-red-500 focus:ring-red-500" : ""}>
 <SelectValue placeholder="Select yes or no" />
 </SelectTrigger>
 <SelectContent>
 <SelectItem value="yes">Yes</SelectItem>
 <SelectItem value="no">No</SelectItem>
 </SelectContent>
 </Select>
 </div>
 ))}
 
 {/* Summary of answered scenarios */}
 {billingScenarios.some(s => formData[s.key as keyof typeof formData] === 'yes') && (
 <div className="rounded-lg bg-green-50 p-3">
 <p className="text-sm font-medium text-green-900 mb-2">Selected billing scenarios:</p>
 <ul className="text-xs text-green-800 space-y-1">
 {billingScenarios
 .filter(s => formData[s.key as keyof typeof formData] === 'yes')
 .map(s => <li key={s.key}>• {s.shortLabel}</li>)
 }
 </ul>
 </div>
 )}
 </div>
 </CardContent>
 </Card>
 </BlurFade>

 {/* Governance Summary */}
 <BlurFade delay={0.08} inView>
 <Card className="bg-gradient-to-br from-gray-50 to-slate-50 border-gray-200">
 <CardContent className="p-6">
 <div className="flex items-center gap-3 mb-4">
 <Shield className="h-5 w-5 text-gray-600" />
 <h3 className="font-medium text-gray-900">Governance Framework Summary</h3>
 </div>
 <div className="grid grid-cols-2 gap-4 text-sm">
 <div>
 <p className="text-gray-600">IRB/Ethics</p>
 <p className="font-medium">{formData.irbName || "Not specified"}</p>
 </div>
 <div>
 <p className="text-gray-600">Sponsor</p>
 <p className="font-medium">{formData.sponsorName || "Not specified"}</p>
 </div>
 <div>
 <p className="text-gray-600">CRO Usage</p>
 <p className="font-medium">{formData.willUseCRO ? "Yes" : "No"}</p>
 </div>
 <div>
 <p className="text-gray-600">Committees</p>
 <p className="font-medium">
 {formData.dataEvaluationCommittees.length + formData.independentCommittees.length} total
 </p>
 </div>
 </div>
 {billingScenarios.some(s => formData[s.key as keyof typeof formData] === 'yes') && (
 <div className="mt-4 pt-4 border-t border-gray-200">
 <p className="text-sm text-gray-600 mb-2">Financial Framework:</p>
 <div className="flex flex-wrap gap-1">
 {billingScenarios
 .filter(s => formData[s.key as keyof typeof formData] === 'yes')
 .map(s => (
 <Badge key={s.key} variant="secondary" className="text-xs">
 {s.shortLabel}
 </Badge>
 ))
 }
 </div>
 </div>
 )}
 </CardContent>
 </Card>
 </BlurFade>

 {/* Navigation */}
 <BlurFade delay={0.09} inView>
 <div className="flex justify-between pt-6">
 <Button
 variant="outline"
 onClick={() => router.push("/study/new/study-procedures-operations")}
 >
 <ChevronLeft className="mr-2 h-4 w-4" />
 Back to Study Procedures & Operations
 </Button>
 <Button
 onClick={handleContinue}
 disabled={saveDiscovery.isPending}
 >
 {saveDiscovery.isPending ? "Saving..." : "Continue to Review & Synopsis"}
 </Button>
 </div>
 </BlurFade>

 {/* Insights Panel */}
 {activeInsightsPanel && (
 <InsightsPanelPortal
 isOpen={true}
 onClose={() => setActiveInsightsPanel(null)}
 title={`Insights: ${activeInsightsPanel.replace("-", " ").replace(/\b\w/g, l => l.toUpperCase())}`}
 description="Recommendations based on similar studies"
 loading={queryInsights.isPending}
 sections={insightsData[activeInsightsPanel]?.sections || cachedInsights[activeInsightsPanel]?.sections || []}
 sources={insightsData[activeInsightsPanel]?.sources || cachedInsights[activeInsightsPanel]?.sources || []}
 progressStatus={insightsData[activeInsightsPanel]?.progressStatus}
 progressMessages={insightsData[activeInsightsPanel]?.progressMessages}
 onDocumentClick={(url) => setDocumentViewerUrl(url)}
 onApplySuggestion={(suggestion, actionableData) => handleApplySuggestion(activeInsightsPanel, suggestion, actionableData)}
 />
 )}

 {/* Document Viewer */}
 {documentViewerUrl && (
 <DocumentViewerPortal
 citations={(() => {
 // Extract all unique citations from all sections
 const allCitations = new Map<string, any>();
 Object.values(insightsData).forEach(data => {
 data.sections?.forEach((section: any) => {
 if (section.citations && Array.isArray(section.citations)) {
 section.citations.forEach((citation: any) => {
 if (citation.id && !allCitations.has(citation.id)) {
 allCitations.set(citation.id, citation);
 }
 });
 }
 });
 });
 return Array.from(allCitations.values());
 })()}
 sources={(() => {
 // Extract all unique sources from all insights data
 const allSources = new Map<string, any>();
 Object.values(insightsData).forEach(data => {
 data.sources?.forEach((source: any) => {
 if (source.nctId && !allSources.has(source.nctId)) {
 allSources.set(source.nctId, source);
 }
 });
 });
 return Array.from(allSources.values());
 })()}
 onStudySelect={(studyUrl) => setDocumentViewerUrl(studyUrl)}
 currentStudyId={(() => {
 // Extract NCT ID from the current document URL
 if (!documentViewerUrl) return undefined;
 const match = documentViewerUrl.match(/NCT\\d+/i);
 return match ? match[0] : undefined;
 })()}
 isOpen={true}
 onClose={() => setDocumentViewerUrl(null)}
 documentUrl={documentViewerUrl}
 loading={false}
 />
 )}

 {/* TBD Confirmation Dialog */}
 <TBDConfirmationPortal
 isOpen={showTBDConfirmation}
 onClose={() => setShowTBDConfirmation(false)}
 onConfirm={proceedWithSave}
 blankFields={blankFieldsList}
 />
 </div>
 );
}