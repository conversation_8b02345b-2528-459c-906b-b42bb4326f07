"use client";

import { useState, useRef, useEffect } from "react";
import { createPortal } from "react-dom";
import { useSession, signOut } from "next-auth/react";
import { useRouter } from "next/navigation";
import { ChevronDown, LogOut, User, Settings, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { useTrialDesignStore } from "~/store/trial-design";

export function UserProfile() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const store = useTrialDesignStore();
  const [isOpen, setIsOpen] = useState(false);
  const [isSigningOut, setIsSigningOut] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0 });
  const [mounted, setMounted] = useState(false);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Handle mounting for SSR
  useEffect(() => {
    setMounted(true);
  }, []);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dropdownRef.current && 
        !dropdownRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    }

    // Prevent focus loss on homepage by capturing focus events
    function preventFocusLoss(event: FocusEvent) {
      if (isOpen && dropdownRef.current && dropdownRef.current.contains(event.target as Node)) {
        event.stopPropagation();
        event.preventDefault();
      }
    }

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      document.addEventListener("focusout", preventFocusLoss, true);
      document.addEventListener("focusin", preventFocusLoss, true);
      return () => {
        document.removeEventListener("mousedown", handleClickOutside);
        document.removeEventListener("focusout", preventFocusLoss, true);
        document.removeEventListener("focusin", preventFocusLoss, true);
      };
    }
  }, [isOpen]);

  // Calculate dropdown position
  const updateDropdownPosition = () => {
    if (buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      setDropdownPosition({
        top: rect.bottom + 8,
        left: rect.right - 256, // 256px is the dropdown width (w-64)
      });
    }
  };

  // Update position when dropdown opens
  useEffect(() => {
    if (isOpen) {
      updateDropdownPosition();
      
      // Update position on scroll/resize
      const handleUpdate = () => updateDropdownPosition();
      window.addEventListener('scroll', handleUpdate);
      window.addEventListener('resize', handleUpdate);
      
      return () => {
        window.removeEventListener('scroll', handleUpdate);
        window.removeEventListener('resize', handleUpdate);
      };
    }
  }, [isOpen]);

  const handleSignOut = async () => {
    setIsSigningOut(true);
    
    try {
      // Clear the store
      store.resetStore();
      
      // Clear localStorage
      if (typeof window !== "undefined") {
        localStorage.removeItem("trial-design-storage");
      }
      
      // Sign out with NextAuth
      await signOut({ 
        redirect: true,
        callbackUrl: "/" 
      });
      
      toast.success("Successfully signed out");
    } catch (error) {
      console.error("Sign out error:", error);
      toast.error("Error signing out");
      setIsSigningOut(false);
    }
  };

  // Don't render if no session or still loading
  if (status === "loading") {
    return (
      <div className="flex items-center justify-center h-8 w-8 rounded-full bg-gray-200 animate-pulse">
        <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
      </div>
    );
  }

  if (status === "unauthenticated" || !session?.user) {
    return (
      <button
        onClick={() => router.push("/auth/signin")}
        className="px-4 py-2 text-sm font-medium text-[#5A32FA] hover:text-[#7D2AE8] transition-colors"
      >
        Sign In
      </button>
    );
  }

  const user = session.user;
  const initials = user.name
    ? user.name.split(" ").map(n => n[0]).join("").toUpperCase().slice(0, 2)
    : user.email?.[0]?.toUpperCase() || "U";

  // Dropdown content component
  const DropdownContent = () => (
    <div 
      ref={dropdownRef}
      className="fixed w-64 bg-white border border-gray-300 rounded-2xl shadow-2xl py-2"
      style={{
        top: dropdownPosition.top,
        left: dropdownPosition.left,
        zIndex: 2147483647,
        pointerEvents: 'auto',
        isolation: 'isolate',
        willChange: 'transform',
        transform: 'translateZ(0)', // Force hardware acceleration
      }}
      onMouseDown={(e) => e.stopPropagation()}
      onMouseUp={(e) => e.stopPropagation()}
      onClick={(e) => e.stopPropagation()}
      onFocus={(e) => e.stopPropagation()}
      onBlur={(e) => e.stopPropagation()}
    >
      {/* User Info */}
      <div className="px-4 py-3 border-b border-gray-200">
        <div className="flex items-center gap-3">
          <div className="h-10 w-10 rounded-full bg-gradient-to-br from-[#5A32FA] to-[#7D2AE8] flex items-center justify-center text-white font-semibold shadow-lg shadow-purple-500/25">
            {initials}
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 truncate">
              {user.name || "User"}
            </p>
            <p className="text-xs text-gray-500 truncate">
              {user.email}
            </p>
            <div className="flex items-center gap-2 mt-1">
              <div className="px-2 py-0.5 text-xs font-medium bg-gradient-to-r from-[#5A32FA]/10 to-[#00C4CC]/10 text-[#5A32FA] border border-[#5A32FA]/20 rounded-full">
                {(user as any).role || "researcher"}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Menu Items */}
      <div className="py-2">
        <button
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            setIsOpen(false);
            router.push("/dashboard");
          }}
          onMouseDown={(e) => e.stopPropagation()}
          onMouseUp={(e) => e.stopPropagation()}
          className="w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
          style={{ pointerEvents: 'auto', zIndex: 1000000, isolation: 'isolate' }}
        >
          <User className="h-4 w-4" />
          Dashboard
        </button>
        
        <button
          disabled
          className="w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-400 cursor-not-allowed"
        >
          <Settings className="h-4 w-4" />
          Settings
          <span className="ml-auto text-xs bg-gray-200 px-2 py-0.5 rounded">Soon</span>
        </button>
      </div>

      {/* Sign Out */}
      <div className="pt-2 border-t border-gray-200">
        <button
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            handleSignOut();
          }}
          onMouseDown={(e) => e.stopPropagation()}
          onMouseUp={(e) => e.stopPropagation()}
          disabled={isSigningOut}
          className="w-full flex items-center gap-3 px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          style={{ pointerEvents: 'auto', zIndex: 1000000, isolation: 'isolate' }}
        >
          {isSigningOut ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <LogOut className="h-4 w-4" />
          )}
          {isSigningOut ? "Signing out..." : "Sign out"}
        </button>
      </div>
    </div>
  );

  return (
    <div className="relative">
      {/* Profile Button */}
      <button
        ref={buttonRef}
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 p-1 rounded-full hover:bg-gray-100/50 transition-colors"
        disabled={isSigningOut}
      >
        <div className="h-8 w-8 rounded-full bg-gradient-to-br from-[#5A32FA] to-[#7D2AE8] flex items-center justify-center text-white font-semibold text-sm shadow-lg shadow-purple-500/25">
          {initials}
        </div>
        <ChevronDown className={`h-4 w-4 text-gray-500 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {/* Portal-rendered dropdown */}
      {isOpen && mounted && typeof window !== 'undefined' && 
        createPortal(<DropdownContent />, document.body)
      }
    </div>
  );
}