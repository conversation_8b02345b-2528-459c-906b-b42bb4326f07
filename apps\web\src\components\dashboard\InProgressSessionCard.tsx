"use client";

import { useState, useEffect, useCallback, useRef, useMemo, memo } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { 
  MoreHorizontal, 
  Play, 
  Edit2, 
  Trash2, 
  Calendar,
  TrendingUp,
  AlertCircle,
  Loader2
} from "lucide-react";
import * as DropdownMenuPrimitive from "@radix-ui/react-dropdown-menu";
import * as AlertDialogPrimitive from "@radix-ui/react-alert-dialog";
import { Input } from "~/components/ui/input";
import { toast } from "sonner";
import { api } from "~/trpc/react";
import { useTrialDesignStore } from "~/store/trial-design";
import type { StudyDesignSession } from "~/types/trial-design";
import { 
  getSessionTitle, 
  getSessionProgress, 
  formatLastUpdated,
  getCurrentStepName
} from "~/utils/sessionUtils";

interface InProgressSessionCardProps {
  session: StudyDesignSession;
  onSessionUpdated?: () => void;
}

const InProgressSessionCard = memo(function InProgressSessionCard({ session, onSessionUpdated }: InProgressSessionCardProps) {
  const router = useRouter();
  const store = useTrialDesignStore();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showRenameDialog, setShowRenameDialog] = useState(false);
  const [newTitle, setNewTitle] = useState("");
  const [isRenaming, setIsRenaming] = useState(false);
  const [isResuming, setIsResuming] = useState(false);

  // Simplified modal state management
  const timeoutRefs = useRef<NodeJS.Timeout[]>([]);

  // Cleanup function to reset all modal states
  const resetAllModalStates = useCallback(() => {
    setShowDeleteDialog(false);
    setShowRenameDialog(false);
    setNewTitle("");
    setIsRenaming(false);
    setIsResuming(false);
    
    // Clear any pending timeouts
    timeoutRefs.current.forEach(timeout => clearTimeout(timeout));
    timeoutRefs.current = [];
    
    // CRITICAL FIX: Force cleanup HTML element CSS modifications from Radix UI
    const htmlElement = document.documentElement;
    
    // Add emergency cleanup class to reset all HTML element styles
    htmlElement.classList.add('radix-cleanup');
    
    // Force reset critical CSS properties that Radix UI might have modified
    htmlElement.style.removeProperty('pointer-events');
    htmlElement.style.removeProperty('overflow');
    htmlElement.style.removeProperty('position');
    htmlElement.style.removeProperty('z-index');
    htmlElement.style.removeProperty('height');
    htmlElement.style.removeProperty('width');
    
    // Also reset body styles that might be affected
    document.body.style.removeProperty('pointer-events');
    document.body.style.removeProperty('overflow');
    document.body.style.removeProperty('position');
    document.body.style.removeProperty('padding-right'); // scrollbar compensation
    
    // Force cleanup of any orphaned overlays
    setTimeout(() => {
      const overlays = document.querySelectorAll('[data-radix-alert-dialog-overlay]');
      overlays.forEach(overlay => {
        if (overlay.parentNode) {
          overlay.parentNode.removeChild(overlay);
        }
      });
      
      // Also check for any elements with high z-index that might be blocking
      const highZElements = document.querySelectorAll('[style*="z-index"]');
      highZElements.forEach(element => {
        const style = window.getComputedStyle(element);
        if (style.zIndex && parseInt(style.zIndex) > 50 && 
            !element.closest('[data-slot="alert-dialog-content"]') &&
            !element.closest('[data-radix-dropdown-menu-content]')) {
          // This might be an orphaned modal overlay
          console.warn('Detected potential blocking element:', element);
          if (element.getAttribute('data-radix-alert-dialog-overlay') !== null) {
            element.remove();
          }
        }
      });
      
      // Remove cleanup class after a brief delay
      setTimeout(() => {
        htmlElement.classList.remove('radix-cleanup');
      }, 50);
    }, 100);
  }, []);

  // Emergency cleanup on component unmount
  useEffect(() => {
    return () => {
      timeoutRefs.current.forEach(timeout => clearTimeout(timeout));
      resetAllModalStates();
    };
  }, [resetAllModalStates]);

  // Global click detector to clean up blocking overlays
  useEffect(() => {
    const handleGlobalClick = (e: MouseEvent) => {
      // If neither dialog should be open but there are overlays, clean them up
      if (!showDeleteDialog && !showRenameDialog) {
        const overlays = document.querySelectorAll('[data-radix-alert-dialog-overlay]');
        if (overlays.length > 0) {
          console.warn('Detected orphaned overlays during click, cleaning up...');
          overlays.forEach(overlay => {
            if (overlay.parentNode) {
              overlay.parentNode.removeChild(overlay);
            }
          });
          
          // Re-enable pointer events on body if they were disabled
          document.body.style.pointerEvents = '';
        }
      }
    };

    // Add a slight delay to avoid interfering with normal dialog operations
    const timeoutId = setTimeout(() => {
      document.addEventListener('click', handleGlobalClick, true);
    }, 500);

    return () => {
      clearTimeout(timeoutId);
      document.removeEventListener('click', handleGlobalClick, true);
    };
  }, [showDeleteDialog, showRenameDialog]);

  // Emergency escape key handler
  useEffect(() => {
    const handleEscapeKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && !showDeleteDialog && !showRenameDialog) {
        // Force cleanup any blocking overlays
        console.warn('Emergency cleanup triggered by Escape key');
        resetAllModalStates();
        document.body.setAttribute('data-overlay-cleanup', 'true');
        setTimeout(() => {
          document.body.removeAttribute('data-overlay-cleanup');
        }, 100);
      }
    };

    document.addEventListener('keydown', handleEscapeKey);
    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [showDeleteDialog, showRenameDialog, resetAllModalStates]);

  // Monitor HTML element CSS modifications and force cleanup
  useEffect(() => {
    const htmlElement = document.documentElement;
    let lastPointerEvents = '';
    let lastOverflow = '';
    
    const checkHtmlElement = () => {
      const computedStyle = window.getComputedStyle(htmlElement);
      const currentPointerEvents = computedStyle.pointerEvents;
      const currentOverflow = computedStyle.overflow;
      
      // If HTML element gets modified CSS that could block interactions
      if (currentPointerEvents !== lastPointerEvents || currentOverflow !== lastOverflow) {
        // Check if this might be problematic (not from our CSS rules)
        if (currentPointerEvents !== 'none' && currentPointerEvents !== 'auto' && 
            !htmlElement.classList.contains('radix-cleanup')) {
          console.warn('Detected HTML element CSS modification:', {
            pointerEvents: currentPointerEvents,
            overflow: currentOverflow,
            className: htmlElement.className
          });
          
          // If neither dialog is open but HTML element has unusual CSS, clean it up
          if (!showDeleteDialog && !showRenameDialog) {
            console.warn('Cleaning up HTML element CSS modifications');
            resetAllModalStates();
          }
        }
        
        lastPointerEvents = currentPointerEvents;
        lastOverflow = currentOverflow;
      }
    };
    
    // Check immediately and then set up interval
    checkHtmlElement();
    const interval = setInterval(checkHtmlElement, 1000);
    
    // Also set up a MutationObserver to catch real-time changes
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && 
            (mutation.attributeName === 'style' || mutation.attributeName === 'class')) {
          checkHtmlElement();
        }
      });
    });
    
    observer.observe(htmlElement, {
      attributes: true,
      attributeFilter: ['style', 'class']
    });
    
    return () => {
      clearInterval(interval);
      observer.disconnect();
    };
  }, [showDeleteDialog, showRenameDialog, resetAllModalStates]);

  const deleteSession = api.studyDesign.deleteSession.useMutation({
    onSuccess: () => {
      toast.success("Study deleted successfully");
      store.removeInProgressSession(session.id);
      resetAllModalStates();
      onSessionUpdated?.();
    },
    onError: (error) => {
      toast.error(`Failed to delete study: ${error.message}`);
      resetAllModalStates();
    },
  });

  const updateSessionMetadata = api.studyDesign.updateSessionMetadata.useMutation({
    onSuccess: () => {
      toast.success("Study renamed successfully");
      resetAllModalStates();
      onSessionUpdated?.();
    },
    onError: (error) => {
      toast.error(`Failed to rename study: ${error.message}`);
      resetAllModalStates();
    },
  });

  const handleResume = useCallback(async () => {
    setIsResuming(true);
    try {
      // Load the session data into the store
      store.loadFromSession(session);
      // Navigate to the appropriate step
      const stepPaths = [
        "/study/new",
        "/study/new/investigational-product", 
        "/study/new/study-design",
        "/study/new/study-population",
        "/study/new/safety-assessment",
        "/study/new/study-procedures-operations",
        "/study/new/regulatory-financial-legal",
        "/study/new/review"
      ];
      
      const currentStepIndex = session.completedSteps?.length || 0;
      const targetPath = stepPaths[Math.min(currentStepIndex, stepPaths.length - 1)] || "/study/new";
      
      await router.push(targetPath);
    } catch (error) {
      console.error('Error resuming session:', error);
      setIsResuming(false);
    }
    // Note: setIsResuming(false) will happen on component unmount after navigation
  }, [session, store, router]);

  const handleDelete = useCallback(() => {
    deleteSession.mutate({ sessionId: session.id });
    setShowDeleteDialog(false);
  }, [deleteSession, session.id]);

  const handleDeleteCancel = useCallback(() => {
    setShowDeleteDialog(false);
  }, []);

  const handleRenameCancel = useCallback(() => {
    setShowRenameDialog(false);
    setNewTitle("");
    setIsRenaming(false);
  }, []);

  const handleRename = useCallback(() => {
    if (!newTitle.trim()) {
      toast.error("Please enter a valid title");
      return;
    }
    
    setIsRenaming(true);
    updateSessionMetadata.mutate({ 
      sessionId: session.id, 
      title: newTitle.trim() 
    });
  }, [newTitle, updateSessionMetadata, session.id]);

  // Memoize computed values to prevent re-render loops
  const progress = useMemo(() => getSessionProgress(session), [session.completedSteps]);
  const title = useMemo(() => getSessionTitle(session), [session.discovery]);
  const lastUpdated = useMemo(() => formatLastUpdated(session.updatedAt), [session.updatedAt]);
  const currentStep = useMemo(() => getCurrentStepName(session), [session.completedSteps]);

  const openRenameDialog = useCallback(() => {
    setNewTitle(title); // Use memoized title instead of calling getSessionTitle again
    setShowRenameDialog(true);
  }, [title]);

  return (
    <>
      <Card className="group overflow-hidden border-[#5A32FA]/10 bg-white/80 backdrop-blur-sm hover:border-[#5A32FA]/30 hover:shadow-2xl hover:shadow-purple-500/10 transition-all duration-300">
        <div className="absolute inset-0 bg-gradient-to-br from-[#5A32FA]/5 to-[#00C4CC]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        
        <CardHeader className="relative pb-3">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <CardTitle className="text-lg font-bold text-gray-900 group-hover:text-[#5A32FA] transition-colors truncate">
                {title}
              </CardTitle>
              <CardDescription className="mt-1 flex items-center gap-3 text-sm">
                <span className="flex items-center gap-1.5 text-gray-500">
                  <Calendar className="h-3.5 w-3.5" />
                  {lastUpdated}
                </span>
                <span className="flex items-center gap-1.5 text-gray-500">
                  <TrendingUp className="h-3.5 w-3.5" />
                  {progress}% complete
                </span>
              </CardDescription>
            </div>
            
            <div className="flex items-center gap-2 ml-4">
              <Badge 
                variant="outline" 
                className="bg-gradient-to-r from-[#5A32FA]/10 to-[#7D2AE8]/10 border-[#5A32FA]/20 text-[#5A32FA] px-2 py-1"
              >
                {session.discovery?.phase?.toUpperCase() || "DISCOVERY"}
              </Badge>
              
              <DropdownMenuPrimitive.Root>
                <DropdownMenuPrimitive.Trigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    disabled={isResuming || deleteSession.isPending || isRenaming}
                    className="h-8 w-8 p-0 hover:bg-[#5A32FA]/10 disabled:opacity-50"
                  >
                    {(isResuming || deleteSession.isPending || isRenaming) ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <MoreHorizontal className="h-4 w-4" />
                    )}
                  </Button>
                </DropdownMenuPrimitive.Trigger>
                <DropdownMenuPrimitive.Portal>
                  <DropdownMenuPrimitive.Content 
                    align="end" 
                    className="w-[160px] bg-white border border-gray-200 rounded-md shadow-lg z-[9999] p-1"
                  >
                    <DropdownMenuPrimitive.Item 
                      onClick={handleResume}
                      className="flex items-center px-2 py-2 text-sm hover:bg-gray-100 rounded cursor-pointer outline-none disabled:opacity-50 disabled:cursor-not-allowed"
                      disabled={isResuming || deleteSession.isPending || isRenaming}
                    >
                      {isResuming ? (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      ) : (
                        <Play className="mr-2 h-4 w-4" />
                      )}
                      {isResuming ? "Resuming..." : "Resume"}
                    </DropdownMenuPrimitive.Item>
                    <DropdownMenuPrimitive.Item 
                      onClick={openRenameDialog}
                      className="flex items-center px-2 py-2 text-sm hover:bg-gray-100 rounded cursor-pointer outline-none disabled:opacity-50 disabled:cursor-not-allowed"
                      disabled={isResuming || deleteSession.isPending || isRenaming}
                    >
                      <Edit2 className="mr-2 h-4 w-4" />
                      Rename
                    </DropdownMenuPrimitive.Item>
                    <DropdownMenuPrimitive.Item 
                      onClick={() => setShowDeleteDialog(true)}
                      className="flex items-center px-2 py-2 text-sm hover:bg-gray-100 rounded cursor-pointer outline-none text-red-600 disabled:opacity-50 disabled:cursor-not-allowed"
                      disabled={isResuming || deleteSession.isPending || isRenaming}
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete
                    </DropdownMenuPrimitive.Item>
                  </DropdownMenuPrimitive.Content>
                </DropdownMenuPrimitive.Portal>
              </DropdownMenuPrimitive.Root>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="relative pt-0">
          {/* Progress Bar */}
          <div className="mb-3">
            <div className="flex items-center justify-between text-xs text-gray-500 mb-1">
              <span>Progress</span>
              <span>{progress}%</span>
            </div>
            <div className="relative h-2 bg-gray-200 rounded-full overflow-hidden">
              <div 
                className="absolute inset-y-0 left-0 bg-gradient-to-r from-[#5A32FA] to-[#7D2AE8] transition-all duration-300"
                style={{ width: `${progress}%` }}
              >
                <div className="absolute right-0 top-0 h-full w-8 bg-white/30 animate-shimmer" />
              </div>
            </div>
          </div>

          {/* Current Step */}
          <div className="text-sm text-gray-600 mb-4">
            <span className="font-medium">Next step:</span> {currentStep}
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button 
              onClick={handleResume}
              disabled={isResuming}
              className="flex-1 bg-gradient-to-r from-[#5A32FA] to-[#7D2AE8] hover:from-[#7D2AE8] hover:to-[#5A32FA] text-white shadow-lg shadow-purple-500/25 hover:shadow-xl hover:shadow-purple-500/30 transition-all duration-300 disabled:opacity-70"
            >
              {isResuming ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Play className="mr-2 h-4 w-4" />
              )}
              {isResuming ? "Resuming..." : "Resume"}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialogPrimitive.Root 
        open={showDeleteDialog} 
        onOpenChange={(open) => {
          setShowDeleteDialog(open);
          if (!open) {
            // Force cleanup when dialog closes
            setTimeout(() => {
              resetAllModalStates();
            }, 150);
          }
        }}
      >
        <AlertDialogPrimitive.Portal>
          <AlertDialogPrimitive.Overlay className="fixed inset-0 bg-black/50 z-[998]" />
          <AlertDialogPrimitive.Content className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-white border border-gray-200 rounded-lg shadow-xl z-[999] w-full max-w-md p-6">
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <AlertCircle className="h-5 w-5 text-red-500" />
                <AlertDialogPrimitive.Title className="text-lg font-semibold">
                  Delete Study
                </AlertDialogPrimitive.Title>
              </div>
              <AlertDialogPrimitive.Description className="text-sm text-gray-600">
                Are you sure you want to delete &quot;{title}&quot;? This action cannot be undone and all progress will be lost.
              </AlertDialogPrimitive.Description>
              <div className="flex justify-end gap-2">
                <AlertDialogPrimitive.Cancel asChild>
                  <Button variant="outline" onClick={handleDeleteCancel}>
                    Cancel
                  </Button>
                </AlertDialogPrimitive.Cancel>
                <AlertDialogPrimitive.Action asChild>
                  <Button 
                    onClick={handleDelete}
                    className="bg-red-600 hover:bg-red-700 text-white"
                    disabled={deleteSession.isPending}
                  >
                    {deleteSession.isPending ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Deleting...
                      </>
                    ) : (
                      "Delete"
                    )}
                  </Button>
                </AlertDialogPrimitive.Action>
              </div>
            </div>
          </AlertDialogPrimitive.Content>
        </AlertDialogPrimitive.Portal>
      </AlertDialogPrimitive.Root>

      {/* Rename Dialog */}
      <AlertDialogPrimitive.Root 
        open={showRenameDialog} 
        onOpenChange={(open) => {
          setShowRenameDialog(open);
          if (!open) {
            // Force cleanup when dialog closes
            setTimeout(() => {
              resetAllModalStates();
            }, 150);
          }
        }}
      >
        <AlertDialogPrimitive.Portal>
          <AlertDialogPrimitive.Overlay className="fixed inset-0 bg-black/50 z-[998]" />
          <AlertDialogPrimitive.Content className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-white border border-gray-200 rounded-lg shadow-xl z-[999] w-full max-w-md p-6">
            <div className="space-y-4">
              <div>
                <AlertDialogPrimitive.Title className="text-lg font-semibold">
                  Rename Study
                </AlertDialogPrimitive.Title>
                <AlertDialogPrimitive.Description className="text-sm text-gray-600 mt-1">
                  Enter a new name for your study design.
                </AlertDialogPrimitive.Description>
              </div>
              <div>
                <Input
                  value={newTitle}
                  onChange={(e) => setNewTitle(e.target.value)}
                  placeholder="Study title"
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      handleRename();
                    }
                  }}
                />
              </div>
              <div className="flex justify-end gap-2">
                <AlertDialogPrimitive.Cancel asChild>
                  <Button variant="outline" onClick={handleRenameCancel}>
                    Cancel
                  </Button>
                </AlertDialogPrimitive.Cancel>
                <AlertDialogPrimitive.Action asChild>
                  <Button 
                    onClick={handleRename}
                    disabled={isRenaming || !newTitle.trim()}
                  >
                    {isRenaming ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Renaming...
                      </>
                    ) : (
                      "Rename"
                    )}
                  </Button>
                </AlertDialogPrimitive.Action>
              </div>
            </div>
          </AlertDialogPrimitive.Content>
        </AlertDialogPrimitive.Portal>
      </AlertDialogPrimitive.Root>

      <style jsx>{`
        @keyframes shimmer {
          0% {
            transform: translateX(-100%);
          }
          100% {
            transform: translateX(400%);
          }
        }
        .animate-shimmer {
          animation: shimmer 2s infinite;
        }
      `}</style>
    </>
  );
});

export { InProgressSessionCard };