"use client";

import { useState, useEffect } from "react";
import { X, ExternalLink, FileText, Search, Filter, BookOpen } from "lucide-react";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { Card, CardContent } from "~/components/ui/card";
import { Input } from "~/components/ui/input";
import { cn } from "~/lib/utils";
import type { SourceDocument } from "~/types/trial-design";

interface SourceStudiesViewerProps {
 isOpen: boolean;
 onClose: () => void;
 sources: SourceDocument[];
 onDocumentClick?: (url: string) => void;
 title?: string;
}

export function SourceStudiesViewer({
 isOpen,
 onClose,
 sources = [],
 onDocumentClick,
 title = "Source Studies"
}: SourceStudiesViewerProps) {
 const [searchTerm, setSearchTerm] = useState("");
 const [filteredSources, setFilteredSources] = useState<SourceDocument[]>(sources);

 // Lock body scroll when open
 useEffect(() => {
 if (isOpen) {
 document.body.style.overflow = "hidden";
 } else {
 document.body.style.overflow = "unset";
 }
 return () => {
 document.body.style.overflow = "unset";
 };
 }, [isOpen]);

 // Filter sources based on search term
 useEffect(() => {
 if (!searchTerm) {
 setFilteredSources(sources);
 } else {
 const filtered = sources.filter(source => 
 source.nctId.toLowerCase().includes(searchTerm.toLowerCase()) ||
 source.excerpt?.toLowerCase().includes(searchTerm.toLowerCase()) ||
 source.status?.toLowerCase().includes(searchTerm.toLowerCase()) ||
 source.studyType?.toLowerCase().includes(searchTerm.toLowerCase())
 );
 setFilteredSources(filtered);
 }
 }, [searchTerm, sources]);

 const getStatusBadgeVariant = (status: string) => {
 switch (status) {
 case 'COMPLETED':
 return 'default';
 case 'RECRUITING':
 return 'secondary';
 case 'ACTIVE_NOT_RECRUITING':
 return 'outline';
 default:
 return 'outline';
 }
 };

 return (
 <>
 {/* Backdrop - Higher opacity for better contrast */}
 <div
 className={cn(
 "fixed inset-0 bg-black/80 backdrop-blur-sm transition-opacity z-[60]",
 isOpen ? "opacity-100" : "opacity-0 pointer-events-none"
 )}
 onClick={onClose}
 />

 {/* Modal - Light glassmorphic background with better contrast */}
 <div
 className={cn(
 "fixed inset-4 md:inset-6 lg:inset-8 bg-white/95 backdrop-blur-xl border border-gray-200/50 rounded-xl shadow-2xl transition-all duration-300 z-[61] overflow-hidden",
 isOpen ? "scale-100 opacity-100" : "scale-95 opacity-0 pointer-events-none"
 )}
 >
 <div className="flex h-full flex-col">
 {/* Header - Light background with clear separation */}
 <div className="border-b border-gray-200 bg-gray-50/50 p-6">
 <div className="flex items-start justify-between">
 <div className="flex-1">
 <h2 className="text-xl font-semibold text-gray-900 ">
 {title}
 </h2>
 <p className="mt-1 text-sm text-gray-600 ">
 {filteredSources.length} {filteredSources.length === 1 ? 'study' : 'studies'} found
 </p>
 </div>
 <Button
 variant="ghost"
 size="icon"
 onClick={onClose}
 className="ml-4"
 >
 <X className="h-4 w-4" />
 </Button>
 </div>

 {/* Search Bar */}
 <div className="mt-4 relative">
 <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
 <Input
 placeholder="Search studies by NCT ID, condition, or status..."
 value={searchTerm}
 onChange={(e) => setSearchTerm(e.target.value)}
 className="pl-10"
 />
 </div>
 </div>

 {/* Content - Better scrolling with light background */}
 <div className="flex-1 overflow-y-auto bg-white ">
 <div className="p-6">
 {filteredSources.length === 0 ? (
 <div className="text-center py-12">
 <BookOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
 <p className="text-muted-foreground">
 {searchTerm ? 'No studies found matching your search.' : 'No source studies available.'}
 </p>
 {searchTerm && (
 <Button
 variant="outline"
 onClick={() => setSearchTerm("")}
 className="mt-2"
 >
 Clear Search
 </Button>
 )}
 </div>
 ) : (
 <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
 {filteredSources.map((source, index) => (
 <Card 
 key={`source-${index}`} 
 className="border-muted hover:border-primary/50 transition-all duration-200 cursor-pointer hover:shadow-md"
 onClick={() => onDocumentClick?.(source.s3Uri)}
 >
 <CardContent className="p-4">
 <div className="space-y-3">
 {/* NCT ID and external link icon */}
 <div className="flex items-center justify-between">
 <div className="flex items-center gap-2">
 <FileText className="h-4 w-4 text-primary" />
 <span className="text-sm font-semibold text-primary">
 {source.nctId}
 </span>
 </div>
 <ExternalLink className="h-4 w-4 text-muted-foreground" />
 </div>
 
 {/* Status and Study Type badges - only show if meaningful */}
 {(source.status !== 'Unknown' || source.studyType !== 'Unknown') && (
 <div className="flex gap-2 flex-wrap">
 {source.status !== 'Unknown' && (
 <Badge 
 variant={getStatusBadgeVariant(source.status)}
 className="text-xs"
 >
 {source.status}
 </Badge>
 )}
 {source.studyType !== 'Unknown' && (
 <Badge variant="outline" className="text-xs">
 {source.studyType}
 </Badge>
 )}
 </div>
 )}
 
 {/* Content preview if meaningful */}
 {source.excerpt && (
 <p className="text-xs text-muted-foreground line-clamp-3 pt-1">
 {source.excerpt}
 </p>
 )}
 </div>
 </CardContent>
 </Card>
 ))}
 </div>
 )}
 </div>
 </div>
 </div>
 </div>
 </>
 );
}