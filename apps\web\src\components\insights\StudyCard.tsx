"use client";

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { 
 Users, 
 Calendar, 
 MapPin, 
 FlaskConical,
 FileText,
 ChevronRight,
 Activity
} from "lucide-react";
import { cn } from "~/lib/utils";
import type { StudyResult } from "~/types/trial-design";

interface StudyCardProps {
 study: StudyResult;
 selected?: boolean;
 onSelect?: (study: StudyResult) => void;
 onViewDetails?: (study: StudyResult) => void;
 compact?: boolean;
 className?: string;
}

export function StudyCard({
 study,
 selected = false,
 onSelect,
 onViewDetails,
 compact = false,
 className
}: StudyCardProps) {
 const statusColors = {
 completed: "bg-green-100 text-green-800",
 active: "bg-blue-100 text-blue-800",
 recruiting: "bg-purple-100 text-purple-800",
 terminated: "bg-red-100 text-red-800",
 withdrawn: "bg-gray-100 text-gray-800",
 suspended: "bg-yellow-100 text-yellow-800",
 };

 const phaseColors = {
 phase1: "bg-orange-100 text-orange-800",
 phase2: "bg-indigo-100 text-indigo-800",
 phase3: "bg-violet-100 text-violet-800",
 phase4: "bg-emerald-100 text-emerald-800",
 unknown: "bg-gray-100 text-gray-800",
 };

 if (compact) {
 return (
 <Card 
 className={cn(
 "hover:shadow-md transition-all cursor-pointer",
 selected && "ring-2 ring-primary",
 className
 )}
 onClick={() => onSelect?.(study)}
 >
 <CardContent className="p-4">
 <div className="flex items-start justify-between">
 <div className="flex-1">
 <h4 className="font-medium text-sm line-clamp-2">{study.title}</h4>
 <div className="mt-2 flex flex-wrap gap-2">
 <Badge variant="outline" className="text-xs">
 <Users className="mr-1 h-3 w-3" />
 {study.enrollment}
 </Badge>
 <Badge className={cn("text-xs", statusColors[study.status])}>
 {study.status}
 </Badge>
 {study.phase && (
 <Badge className={cn("text-xs", phaseColors[study.phase])}>
 {study.phase}
 </Badge>
 )}
 </div>
 </div>
 {onViewDetails && (
 <Button
 variant="ghost"
 size="icon"
 onClick={(e) => {
 e.stopPropagation();
 onViewDetails(study);
 }}
 >
 <ChevronRight className="h-4 w-4" />
 </Button>
 )}
 </div>
 </CardContent>
 </Card>
 );
 }

 return (
 <Card 
 className={cn(
 "hover:shadow-lg transition-all",
 selected && "ring-2 ring-primary",
 className
 )}
 >
 <CardHeader>
 <div className="flex items-start justify-between">
 <div className="flex-1">
 <CardTitle className="text-lg line-clamp-2">{study.title}</CardTitle>
 <CardDescription className="mt-1">
 NCT{study.id} • {study.sponsors?.[0] || "Unknown sponsor"}
 </CardDescription>
 </div>
 <div className="ml-4 text-right">
 <Badge className={cn("mb-2", statusColors[study.status])}>
 {study.status}
 </Badge>
 {study.relevanceScore && (
 <div className="text-sm text-muted-foreground">
 {Math.round(study.relevanceScore * 100)}% match
 </div>
 )}
 </div>
 </div>
 </CardHeader>
 <CardContent className="space-y-4">
 {/* Study Info */}
 <div className="grid grid-cols-2 gap-4 text-sm">
 <div className="flex items-center gap-2">
 <Users className="h-4 w-4 text-muted-foreground" />
 <span>{study.enrollment} participants</span>
 </div>
 <div className="flex items-center gap-2">
 <Calendar className="h-4 w-4 text-muted-foreground" />
 <span>{new Date(study.startDate).getFullYear()} - {study.completionDate ? new Date(study.completionDate).getFullYear() : "Ongoing"}</span>
 </div>
 {study.phase && (
 <div className="flex items-center gap-2">
 <FlaskConical className="h-4 w-4 text-muted-foreground" />
 <Badge variant="outline" className={phaseColors[study.phase]}>
 {study.phase}
 </Badge>
 </div>
 )}
 {study.locations && study.locations.length > 0 && (
 <div className="flex items-center gap-2">
 <MapPin className="h-4 w-4 text-muted-foreground" />
 <span>{study.locations.length} sites</span>
 </div>
 )}
 </div>

 {/* Conditions & Interventions */}
 {(study.conditions || study.interventions) && (
 <div className="space-y-2">
 {study.conditions && study.conditions.length > 0 && (
 <div>
 <p className="text-xs font-medium text-muted-foreground mb-1">Conditions</p>
 <div className="flex flex-wrap gap-1">
 {study.conditions.slice(0, 3).map((condition, idx) => (
 <Badge key={idx} variant="secondary" className="text-xs">
 {condition}
 </Badge>
 ))}
 {study.conditions.length > 3 && (
 <Badge variant="outline" className="text-xs">
 +{study.conditions.length - 3} more
 </Badge>
 )}
 </div>
 </div>
 )}
 {study.interventions && study.interventions.length > 0 && (
 <div>
 <p className="text-xs font-medium text-muted-foreground mb-1">Interventions</p>
 <div className="flex flex-wrap gap-1">
 {study.interventions.slice(0, 2).map((intervention, idx) => (
 <Badge key={idx} variant="secondary" className="text-xs">
 {intervention}
 </Badge>
 ))}
 {study.interventions.length > 2 && (
 <Badge variant="outline" className="text-xs">
 +{study.interventions.length - 2} more
 </Badge>
 )}
 </div>
 </div>
 )}
 </div>
 )}

 {/* Description */}
 {study.description && (
 <p className="text-sm text-muted-foreground line-clamp-3">
 {study.description}
 </p>
 )}

 {/* Actions */}
 <div className="flex gap-2">
 {onSelect && (
 <Button
 variant={selected ? "default" : "outline"}
 size="sm"
 onClick={() => onSelect(study)}
 className="flex-1"
 >
 {selected ? "Selected" : "Select for Analysis"}
 </Button>
 )}
 {onViewDetails && (
 <Button
 variant="ghost"
 size="sm"
 onClick={() => onViewDetails(study)}
 >
 <FileText className="mr-2 h-4 w-4" />
 View Details
 </Button>
 )}
 </div>
 </CardContent>
 </Card>
 );
}