import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "~/lib/utils";

const buttonVariants = cva(
 "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
 {
 variants: {
 variant: {
 default:
 "bg-gradient-to-r from-[#5A32FA] to-[#7D2AE8] text-white shadow hover:shadow-lg hover:scale-105 active:scale-95",
 destructive:
 "bg-red-500 text-white shadow-sm hover:bg-red-600",
 outline:
 "border border-gray-300 bg-white hover:bg-gray-50 hover:text-[#5A32FA]",
 secondary:
 "bg-gradient-to-r from-[#00C4CC] to-[#4CC9F0] text-white shadow-sm hover:shadow-lg hover:scale-105",
 ghost: "hover:bg-gray-100 hover:text-[#5A32FA]",
 link: "text-[#5A32FA] underline-offset-4 hover:underline",
 },
 size: {
 default: "h-10 px-4 py-2",
 sm: "h-8 rounded-lg px-3 text-xs",
 lg: "h-12 rounded-lg px-8",
 icon: "h-10 w-10",
 },
 },
 defaultVariants: {
 variant: "default",
 size: "default",
 },
 }
);

export interface ButtonProps
 extends React.ButtonHTMLAttributes<HTMLButtonElement>,
 VariantProps<typeof buttonVariants> {
 asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
 ({ className, variant, size, asChild = false, ...props }, ref) => {
 const Comp = asChild ? Slot : "button";
 return (
 <Comp
 className={cn(buttonVariants({ variant, size, className }))}
 ref={ref}
 {...props}
 />
 );
 }
);
Button.displayName = "Button";

export { Button, buttonVariants };