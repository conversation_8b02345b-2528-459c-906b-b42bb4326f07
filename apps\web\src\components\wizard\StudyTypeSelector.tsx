"use client";

import { useState } from "react";
import { motion } from "motion/react";
import { cn } from "~/lib/utils";
import { Pill, Cpu, Brain, Microscope, HelpCircle } from "lucide-react";
import type { StudyType } from "~/types/trial-design";

interface StudyTypeSelectorProps {
 value?: StudyType | null;
 onChange: (type: StudyType) => void;
}

const studyTypes = [
 {
 id: "drug" as StudyType,
 label: "Drug",
 description: "Pharmaceutical compounds and medications",
 icon: Pill,
 color: "from-purple-500 to-purple-600",
 },
 {
 id: "device" as StudyType,
 label: "Device",
 description: "Medical devices and equipment",
 icon: Cpu,
 color: "from-cyan-500 to-cyan-600",
 },
 {
 id: "behavioral" as StudyType,
 label: "Behavioral",
 description: "Behavioral interventions and therapies",
 icon: Brain,
 color: "from-green-500 to-green-600",
 },
 {
 id: "diagnostic" as StudyType,
 label: "Diagnostic",
 description: "Diagnostic tests and procedures",
 icon: Microscope,
 color: "from-orange-500 to-orange-600",
 },
 {
 id: "other" as StudyType,
 label: "Other",
 description: "Other types of interventions",
 icon: HelpCircle,
 color: "from-gray-500 to-gray-600",
 },
];

export function StudyTypeSelector({ value, onChange }: StudyTypeSelectorProps) {
 const [hoveredId, setHoveredId] = useState<string | null>(null);

 return (
 <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
 {studyTypes.map((type) => {
 const Icon = type.icon;
 const isSelected = value === type.id;
 const isHovered = hoveredId === type.id;

 return (
 <motion.button
 key={type.id}
 onClick={() => onChange(type.id)}
 onMouseEnter={() => setHoveredId(type.id)}
 onMouseLeave={() => setHoveredId(null)}
 whileHover={{ scale: 1.02 }}
 whileTap={{ scale: 0.98 }}
 className={cn(
 "relative overflow-hidden rounded-xl border-2 p-6 text-left transition-all",
 isSelected
 ? "border-[#5A32FA] bg-purple-50"
 : "border-gray-200 bg-white hover:border-gray-300"
 )}
 >
 {/* Background gradient on hover */}
 <motion.div
 className={cn(
 "absolute inset-0 bg-gradient-to-br opacity-0",
 type.color
 )}
 animate={{
 opacity: isSelected ? 0.1 : isHovered ? 0.05 : 0,
 }}
 transition={{ duration: 0.2 }}
 />

 {/* Content */}
 <div className="relative z-10">
 <div className="mb-4 inline-flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-br p-2">
 <div
 className={cn(
 "flex h-full w-full items-center justify-center rounded-lg bg-gradient-to-br",
 isSelected ? type.color : "from-gray-100 to-gray-200"
 )}
 >
 <Icon
 className={cn(
 "h-6 w-6",
 isSelected ? "text-white" : "text-gray-600"
 )}
 />
 </div>
 </div>

 <h3 className="mb-2 text-lg font-semibold text-gray-900">
 {type.label}
 </h3>
 <p className="text-sm text-gray-600">{type.description}</p>

 {/* Selection indicator */}
 {isSelected && (
 <motion.div
 initial={{ scale: 0 }}
 animate={{ scale: 1 }}
 className="absolute right-4 top-4"
 >
 <div className="flex h-6 w-6 items-center justify-center rounded-full bg-[#5A32FA]">
 <svg
 className="h-4 w-4 text-white"
 fill="none"
 viewBox="0 0 24 24"
 stroke="currentColor"
 strokeWidth={3}
 >
 <path
 strokeLinecap="round"
 strokeLinejoin="round"
 d="M5 13l4 4L19 7"
 />
 </svg>
 </div>
 </motion.div>
 )}
 </div>
 </motion.button>
 );
 })}
 </div>
 );
}