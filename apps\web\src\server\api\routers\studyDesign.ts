import { z } from "zod";
import { createTRPCRouter, protectedProcedure, publicProcedure } from "~/server/api/trpc";
import { generateMockStudies, generateMockInsights } from "~/services/mock/trial-data";
import { studySessionsService } from "~/services/dynamodb/studySessionsService";
import type { StudyDesignSession } from "~/types/trial-design";

// DynamoDB is now used for persistent session storage

export const studyDesignRouter = createTRPCRouter({
  createSession: protectedProcedure
    .input(z.object({
      userId: z.string().optional(),
    }).optional())
    .mutation(async ({ input, ctx }) => {
      const sessionId = `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      const session: StudyDesignSession = {
        id: sessionId,
        userId: ctx.session.user.id,
        status: "discovery",
        currentStep: "study-type",
        completedSteps: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
        discovery: {
          studyType: "drug" as const,
          intervention: {},
          condition: "",
          population: {
            gender: "all",
          },
          objectives: {
            primaryGoal: "",
            keyOutcome: "",
          },
        },
      };
      
      await studySessionsService.createSession(session);
      
      return {
        sessionId,
        expiresAt: session.expiresAt,
      };
    }),

  getSession: protectedProcedure
    .input(z.object({
      sessionId: z.string(),
    }))
    .query(async ({ input, ctx }) => {
      const session = await studySessionsService.getSession(input.sessionId);
      
      if (!session) {
        // Return null instead of throwing error - let frontend handle gracefully
        return null;
      }
      
      // Check if session belongs to authenticated user
      if (session.userId !== ctx.session.user.id) {
        throw new Error("Unauthorized: Session does not belong to user");
      }
      
      // Check if session is expired (DynamoDB TTL will handle deletion automatically)
      if (session.expiresAt < new Date()) {
        return null;
      }
      
      return session;
    }),

  saveDiscovery: protectedProcedure
    .input(z.object({
      sessionId: z.string(),
      data: z.object({
        studyType: z.enum(["drug", "device", "behavioral", "diagnostic", "other"]).optional(),
        phase: z.enum(["phase1", "phase2", "phase3", "phase4", "unknown"]).optional(),
        intervention: z.object({
          name: z.string().optional(),
          category: z.string().optional(),
          mechanism: z.string().optional(),
          class: z.string().optional(),
          drugClass: z.string().optional(),
          isNewCompound: z.boolean().optional(),
          deviceClass: z.string().optional(),
          
          // Enhanced drug product information
          medicalProblem: z.string().optional(),
          comparisonToExistingTreatments: z.string().optional(),
          regulatoryStatus: z.string().optional(),
          activeIngredients: z.array(z.string()).optional(),
          inactiveIngredients: z.array(z.string()).optional(),
          ingredientRationale: z.string().optional(),
          preclinicalStudies: z.string().optional(),
          toxicityStudies: z.string().optional(),
          clinicalTrialsHistory: z.string().optional(),
          keyFindings: z.string().optional(),
          pregnancySafety: z.enum(['safe', 'unsafe', 'unknown', 'contraindicated']).optional(),
          pregnancySafetyDetails: z.string().optional(),
          fdaApprovalStatus: z.enum(['approved', 'investigational', 'compassionate', 'off-label']).optional(),
          drugCompoundNumber: z.string().optional(),
          indApplicationNumber: z.string().optional(),
        }).optional(),
        condition: z.string().optional(),
        population: z.object({
          ageMin: z.number().optional(),
          ageMax: z.number().optional(),
          gender: z.enum(["all", "male", "female"]).optional(),
          specificPopulation: z.string().optional(),
          inclusionCriteria: z.array(z.string()).optional(),
          exclusionCriteria: z.array(z.string()).optional(),
        }).optional(),
        objectives: z.object({
          primaryGoal: z.string().optional(),
          keyOutcome: z.string().optional(),
          secondaryGoals: z.array(z.string()).optional(),
        }).optional(),
        operational: z.object({
          numberOfSites: z.string().optional(),
          sitesPerCountry: z.record(z.number()).optional(),
          recruitmentRate: z.string().optional(),
          screenFailureRate: z.string().optional(),
          dropoutRate: z.string().optional(),
          dataManagementSystem: z.enum(["edc", "paper", "hybrid"]).optional(),
          edcCTMSName: z.string().optional(),
          monitoringApproach: z.enum(["on-site", "remote", "risk-based", "hybrid"]).optional(),
        }).optional(),
        regulatory: z.object({
          irbName: z.string().optional(),
          sponsorName: z.string().optional(),
          drugManufacturerName: z.string().optional(),
          deviceManufacturerName: z.string().optional(),
          willUseCRO: z.boolean().optional(),
          croName: z.string().optional(),
          croAddress: z.string().optional(),
          croContact: z.string().optional(),
          dataEvaluationCommittees: z.array(z.string()).optional(),
          independentCommittees: z.array(z.string()).optional(),
          willParticipantsBeCompensated: z.boolean().optional(),
          compensationDetails: z.string().optional(),
          perVisitCompensation: z.string().optional(),
          completionBonus: z.string().optional(),
          paymentSchedule: z.string().optional(),
          mileageRate: z.string().optional(),
          parkingReimbursement: z.string().optional(),
          publicTransportCap: z.string().optional(),
          hotelCap: z.string().optional(),
          mealAllowance: z.string().optional(),
          sponsorPaysNonStandardCare: z.string().optional(),
          freeDrugDeviceProvided: z.string().optional(),
          standardCareChargedToParticipant: z.string().optional(),
          noBillableProcedures: z.string().optional(),
          billingScenarios: z.array(z.string()).optional(),
        }).optional(),
      }),
    }))
    .mutation(async ({ input, ctx }) => {
      const session = await studySessionsService.getSession(input.sessionId);
      
      if (!session) {
        throw new Error("Session not found");
      }
      
      // Check if session belongs to authenticated user
      if (session.userId !== ctx.session.user.id) {
        throw new Error("Unauthorized: Session does not belong to user");
      }
      
      // Build discovery update object - merge nested objects properly
      const discoveryUpdate = { ...session.discovery };
      
      if (input.data.intervention) {
        discoveryUpdate.intervention = {
          ...session.discovery.intervention,
          ...input.data.intervention,
        };
      }
      
      if (input.data.population) {
        discoveryUpdate.population = {
          ...session.discovery.population,
          ...input.data.population,
        };
      }
      
      if (input.data.objectives) {
        discoveryUpdate.objectives = {
          ...session.discovery.objectives,
          ...input.data.objectives,
        };
      }
      
      if (input.data.regulatory) {
        discoveryUpdate.regulatory = {
          ...session.discovery.regulatory,
          ...input.data.regulatory,
        };
      }
      
      // Update top-level fields
      if (input.data.studyType) {
        discoveryUpdate.studyType = input.data.studyType;
      }
      
      if (input.data.phase) {
        discoveryUpdate.phase = input.data.phase;
      }
      
      if (input.data.condition) {
        discoveryUpdate.condition = input.data.condition;
      }
      
      // Update the session with only the changed fields
      await studySessionsService.updateSession(input.sessionId, {
        discovery: discoveryUpdate,
      });
      
      return {
        success: true,
        sessionId: input.sessionId,
      };
    }),

  updateStep: protectedProcedure
    .input(z.object({
      sessionId: z.string(),
      currentStep: z.string(),
      completedStep: z.string().optional(),
    }))
    .mutation(async ({ input, ctx }) => {
      const session = await studySessionsService.getSession(input.sessionId);
      
      if (!session) {
        throw new Error("Session not found");
      }
      
      // Check if session belongs to authenticated user
      if (session.userId !== ctx.session.user.id) {
        throw new Error("Unauthorized: Session does not belong to user");
      }
      
      // Build update object
      const updates: Partial<StudyDesignSession> = {
        currentStep: input.currentStep,
      };
      
      if (input.completedStep && !session.completedSteps.includes(input.completedStep)) {
        updates.completedSteps = [...session.completedSteps, input.completedStep];
      }
      
      await studySessionsService.updateSession(input.sessionId, updates);
      
      return {
        success: true,
        currentStep: updates.currentStep,
        completedSteps: updates.completedSteps || session.completedSteps,
      };
    }),

  listSessions: protectedProcedure
    .query(async ({ ctx }) => {
      const userId = ctx.session?.user?.id;
      if (!userId) return [];
      
      const userSessions = await studySessionsService.getUserSessions(userId);
      
      // Filter out expired sessions (DynamoDB TTL might not have cleaned them up yet)
      return userSessions.filter(s => s.expiresAt > new Date());
    }),

  deleteSession: protectedProcedure
    .input(z.object({
      sessionId: z.string(),
    }))
    .mutation(async ({ input, ctx }) => {
      const session = await studySessionsService.getSession(input.sessionId);
      
      if (!session) {
        throw new Error("Session not found");
      }
      
      // Check if user owns this session
      if (session.userId !== ctx.session?.user?.id) {
        throw new Error("Unauthorized");
      }
      
      await studySessionsService.deleteSession(input.sessionId);
      
      return {
        success: true,
        deletedSessionId: input.sessionId,
      };
    }),

  searchKnowledgeBase: protectedProcedure
    .input(z.object({
      sessionId: z.string(),
      discovery: z.any(), // Discovery data to search with
    }))
    .mutation(async ({ input, ctx }) => {
      const session = await studySessionsService.getSession(input.sessionId);
      
      if (!session) {
        throw new Error("Session not found");
      }
      
      // Check if session belongs to authenticated user
      if (session.userId !== ctx.session.user.id) {
        throw new Error("Unauthorized: Session does not belong to user");
      }
      
      // In production, this would call AWS Lambda/Bedrock
      // For now, use mock data
      const studies = generateMockStudies(15);
      
      // Store results in session
      const knowledgeBaseResults = {
        studies,
        selectedStudies: [],
        queryMetadata: {
          searchTime: Date.now(),
          totalResults: studies.length,
        },
      };
      
      await studySessionsService.updateSession(input.sessionId, {
        knowledgeBaseResults,
        status: "analyzing",
      });
      
      return {
        studies,
        metadata: knowledgeBaseResults.queryMetadata,
      };
    }),
  
  saveSelectedStudies: protectedProcedure
    .input(z.object({
      sessionId: z.string(),
      studyIds: z.array(z.string()),
    }))
    .mutation(async ({ input, ctx }) => {
      const session = await studySessionsService.getSession(input.sessionId);
      
      if (!session) {
        throw new Error("Session not found");
      }
      
      // Check if session belongs to authenticated user
      if (session.userId !== ctx.session.user.id) {
        throw new Error("Unauthorized: Session does not belong to user");
      }
      
      if (!session.knowledgeBaseResults) {
        throw new Error("No search results found");
      }
      
      // Update selected studies
      // Generate insights based on selected studies
      // In production, this would call AWS Lambda/Bedrock
      const insights = generateMockInsights(input.studyIds);
      
      await studySessionsService.updateSession(input.sessionId, {
        knowledgeBaseResults: {
          ...session.knowledgeBaseResults!,
          selectedStudies: input.studyIds,
        },
        insights,
        status: "designing",
      });
      
      return {
        success: true,
        insights,
      };
    }),
  
  getInsights: protectedProcedure
    .input(z.object({
      sessionId: z.string(),
    }))
    .query(async ({ input, ctx }) => {
      const session = await studySessionsService.getSession(input.sessionId);
      
      if (!session) {
        throw new Error("Session not found");
      }
      
      // Check if session belongs to authenticated user
      if (session.userId !== ctx.session.user.id) {
        throw new Error("Unauthorized: Session does not belong to user");
      }
      
      if (!session.insights) {
        // Generate mock insights if not available
        const studyIds = session.knowledgeBaseResults?.selectedStudies || [];
        const insights = generateMockInsights(studyIds);
        await studySessionsService.updateSession(input.sessionId, {
          insights,
        });
        return insights;
      }
      
      return session.insights;
    }),

  generateSynopsis: protectedProcedure
    .input(z.object({
      sessionId: z.string(),
    }))
    .mutation(async ({ input, ctx }) => {
      const session = await studySessionsService.getSession(input.sessionId);
      
      if (!session) {
        throw new Error("Session not found");
      }
      
      // Check if session belongs to authenticated user
      if (session.userId !== ctx.session.user.id) {
        throw new Error("Unauthorized: Session does not belong to user");
      }
      
      const discovery = session.discovery;
      const sections: Record<string, string> = {};
      
      // Generate sections progressively
      // Each section is generated independently and can be returned immediately
      
      try {
        // 1. Study Overview - Enhanced with protocol details
        sections.studyOverview = await generateStudyOverviewSection(discovery);
        
        // 2. Investigational Product Details (NEW)
        sections.investigationalProduct = generateInvestigationalProductSection(discovery);
        
        // 3. Study Design with Statistical Analysis
        sections.studyDesign = generateStudyDesignSection(discovery);
        
        // 4. Study Population - Enhanced with geographic details
        sections.studyPopulation = generateStudyPopulationSection(discovery);
        
        // 5. Endpoints and Objectives - Enhanced
        sections.endpoints = generateEndpointsSection(discovery);
        
        // 6. Safety Monitoring (NEW)
        sections.safetyMonitoring = generateSafetyMonitoringSection(discovery);
        
        // 7. Study Procedures and Timeline
        sections.procedures = generateProceduresSection(discovery);
        
        // 8. Laboratory and Biomarker Studies (NEW)
        sections.laboratory = generateLaboratorySection(discovery);
        
        // 9. Operational Considerations
        sections.operational = await generateOperationalSection(discovery);
        
        // 10. Regulatory and Financial Framework (NEW)
        sections.regulatory = generateRegulatorySection(discovery);
        
        // Store in session
        await studySessionsService.updateSession(input.sessionId, {
          synopsis: sections,
        });
        
        return {
          sections,
          fullDocument: Object.values(sections).join('\n\n'),
        };
      } catch (error) {
        console.error('Error generating synopsis:', error);
        throw new Error('Failed to generate synopsis');
      }
    }),

  getUserSessions: protectedProcedure
    .query(async ({ ctx }) => {
      const userId = ctx.session?.user?.id;
      
      if (!userId) {
        throw new Error("User not authenticated");
      }
      
      const sessions = await studySessionsService.getUserSessions(userId, 20);
      return sessions;
    }),

  updateSessionMetadata: protectedProcedure
    .input(z.object({
      sessionId: z.string(),
      title: z.string().optional(),
    }))
    .mutation(async ({ input, ctx }) => {
      const userId = ctx.session.user.id;
      
      // Verify the session belongs to the user
      const session = await studySessionsService.getSession(input.sessionId);
      
      if (!session) {
        throw new Error("Session not found");
      }
      
      if (session.userId !== userId) {
        throw new Error("Unauthorized: Session does not belong to user");
      }
      
      // For now, we'll store the title in the discovery condition field
      // In the future, we could add a dedicated title field to StudyDesignSession
      const updates: Partial<StudyDesignSession> = {};
      
      if (input.title) {
        updates.discovery = {
          ...session.discovery,
          condition: input.title,
        };
      }
      
      await studySessionsService.updateSession(input.sessionId, updates);
      
      return {
        success: true,
        sessionId: input.sessionId,
      };
    }),
});

// Utility function to format fields with TBD handling
function formatField(value: any, defaultText: string = "TBD"): string {
  if (!value || value === "" || value === "TBD") return defaultText;
  if (value === "Yes") return "Yes";
  if (value === "No") return "No";
  if (Array.isArray(value)) {
    if (value.length === 0) return defaultText;
    return value.join(", ");
  }
  return String(value);
}

// Comprehensive data extraction function
function extractAllFields(discovery: any): Record<string, any> {
  const fields: Record<string, any> = {};
  
  // Step 1: Study Overview (6 questions)
  fields.protocolAcronym = discovery.protocol?.protocolAcronym;
  fields.protocolFullTitle = discovery.protocol?.protocolFullTitle;
  fields.protocolIdNumber = discovery.protocol?.protocolIdNumber;
  fields.studyBackground = discovery.protocol?.studyBackground;
  fields.trialInterventionDetails = discovery.protocol?.trialInterventionDetails;
  fields.studyEventsAndActivities = discovery.protocol?.studyEventsAndActivities;
  
  // Basic info
  fields.studyType = discovery.studyType;
  fields.phase = discovery.phase;
  fields.condition = discovery.condition;
  
  // Step 2: Investigational Product (11 questions)
  fields.interventionName = discovery.intervention?.name;
  fields.interventionCategory = discovery.intervention?.category;
  fields.drugClass = discovery.intervention?.drugClass;
  fields.medicalProblem = discovery.intervention?.medicalProblem;
  fields.comparisonToExisting = discovery.intervention?.comparisonToExistingTreatments;
  fields.regulatoryStatus = discovery.intervention?.regulatoryStatus;
  fields.preclinicalStudies = discovery.intervention?.preclinicalStudies;
  fields.toxicityStudies = discovery.intervention?.toxicityStudies;
  fields.clinicalTrialsHistory = discovery.intervention?.clinicalTrialsHistory;
  fields.fdaApprovalStatus = discovery.intervention?.fdaApprovalStatus;
  fields.drugCompoundNumber = discovery.intervention?.drugCompoundNumber;
  
  // Step 3: Study Design (19 questions)
  fields.designDescriptors = discovery.design?.designDescriptors;
  fields.designType = discovery.design?.designType;
  fields.randomizationRatio = discovery.design?.randomizationRatio;
  fields.blinding = discovery.design?.blinding;
  fields.controlType = discovery.design?.controlType;
  fields.numberOfStudyArms = discovery.design?.numberOfStudyArms;
  fields.studyArmDescriptions = discovery.design?.studyArmDescriptions;
  fields.analysisPopulations = discovery.design?.analysisPopulations;
  fields.hasAdaptiveDesign = discovery.design?.hasAdaptiveDesign;
  fields.adaptiveDesignDetails = discovery.design?.adaptiveDesignDetails;
  
  // Statistical fields
  fields.sampleSizeDetermination = discovery.statistical?.sampleSizeDetermination;
  fields.statisticalModel = discovery.statistical?.statisticalModel;
  fields.hypothesisAndAnalysis = discovery.statistical?.hypothesisAndAnalysis;
  fields.interimAnalysisPlan = discovery.statistical?.interimAnalysisPlan;
  fields.power = discovery.statistical?.power;
  fields.alpha = discovery.statistical?.alpha;
  fields.multipleTesting = discovery.statistical?.multipleTesting;
  fields.missingDataStrategy = discovery.statistical?.missingDataStrategy;
  
  // Step 4: Study Population (8 questions)
  fields.ageMin = discovery.population?.ageMin;
  fields.ageMax = discovery.population?.ageMax;
  fields.gender = discovery.population?.gender;
  fields.targetEnrollment = discovery.population?.targetEnrollment;
  fields.inclusionCriteria = discovery.population?.inclusionCriteria;
  fields.exclusionCriteria = discovery.population?.exclusionCriteria;
  fields.geographicScope = discovery.population?.geographicScope;
  fields.countriesEngaged = discovery.population?.countriesEngaged;
  
  // Step 5: Objectives (6 questions)
  fields.primaryGoal = discovery.objectives?.primaryGoal;
  fields.keyOutcome = discovery.objectives?.keyOutcome;
  fields.primaryObjectiveStatement = discovery.objectives?.primaryObjectiveStatement;
  fields.secondaryGoals = discovery.objectives?.secondaryGoals;
  fields.exploratoryObjectives = discovery.objectives?.exploratoryObjectives;
  fields.exploratoryObjectiveAnalyses = discovery.objectives?.exploratoryObjectiveAnalyses;
  
  // Step 6: Safety (6 questions)
  fields.willCollectAESAE = discovery.safety?.willCollectAESAE;
  fields.likelySideEffects = discovery.safety?.likelySideEffects;
  fields.lessLikelySideEffects = discovery.safety?.lessLikelySideEffects;
  fields.rareButSeriousSideEffects = discovery.safety?.rareButSeriousSideEffects;
  fields.hasReproductiveRisks = discovery.safety?.hasReproductiveRisks;
  fields.reproductiveRiskDetails = discovery.safety?.reproductiveRiskDetails;
  
  // Step 7: Timeline & Procedures (5 questions)
  fields.screeningPeriod = discovery.timeline?.screeningPeriod;
  fields.baselinePeriod = discovery.timeline?.baselinePeriod;
  fields.treatmentPeriod = discovery.timeline?.treatmentPeriod;
  fields.followUpPeriod = discovery.timeline?.followUpPeriod;
  fields.visits = discovery.timeline?.visits;
  
  // Step 8: Laboratory (7 questions)
  fields.willCollectBiologicalSamples = discovery.laboratory?.willCollectBiologicalSamples;
  fields.biologicalSpecimens = discovery.laboratory?.biologicalSpecimens;
  fields.willConductPK = discovery.laboratory?.willConductPK;
  fields.willConductBiomarker = discovery.laboratory?.willConductBiomarker;
  fields.willConductImmunogenicity = discovery.laboratory?.willConductImmunogenicity;
  fields.willConductGeneticTesting = discovery.laboratory?.willConductGeneticTesting;
  fields.collectionAndProcessing = discovery.laboratory?.collectionAndProcessing;
  
  // Step 9: Operational (5 questions)
  fields.recruitmentRate = discovery.operational?.recruitmentRate;
  fields.screenFailureRate = discovery.operational?.screenFailureRate;
  fields.dropoutRate = discovery.operational?.dropoutRate;
  fields.dataManagementSystem = discovery.operational?.dataManagementSystem;
  fields.monitoringApproach = discovery.operational?.monitoringApproach;
  
  // Step 10: Regulatory & Financial (10 questions)
  fields.irbEthicsCommitteeName = discovery.regulatory?.irbEthicsCommitteeName;
  fields.sponsorName = discovery.regulatory?.sponsorName;
  fields.drugManufacturerName = discovery.regulatory?.drugManufacturerName;
  fields.willUseCRO = discovery.regulatory?.willUseCRO;
  fields.croNameAndAddress = discovery.regulatory?.croNameAndAddress;
  fields.willCompensateParticipants = discovery.regulatory?.willCompensateParticipants;
  fields.perVisitCompensation = discovery.regulatory?.perVisitCompensation;
  fields.completionBonus = discovery.regulatory?.completionBonus;
  fields.sponsorPaysNonStandardCare = discovery.regulatory?.sponsorPaysNonStandardCare;
  fields.freeDrugDeviceProvided = discovery.regulatory?.freeDrugDeviceProvided;
  
  return fields;
}

// Section Generator Functions
function generateStudyTitle(discovery: any): string {
  const phase = discovery.phase || 'Phase 2';
  const design = discovery.design?.designType || 'Randomized';
  const intervention = discovery.intervention?.name || 'Investigational Drug';
  const condition = discovery.condition || 'Target Condition';
  
  return `${phase} ${design.charAt(0).toUpperCase() + design.slice(1)} Study of ${intervention} in ${condition}`;
}

async function generateStudyOverviewSection(discovery: any): Promise<string> {
  const fields = extractAllFields(discovery);
  const title = generateStudyTitle(discovery);
  
  // Format protocol details if available
  const protocolInfo = fields.protocolAcronym || fields.protocolFullTitle || fields.protocolIdNumber
    ? `\n\n**Protocol Information:**\n` +
      `${fields.protocolAcronym ? `- Acronym: ${fields.protocolAcronym}\n` : ''}` +
      `${fields.protocolFullTitle ? `- Full Title: ${fields.protocolFullTitle}\n` : ''}` +
      `${fields.protocolIdNumber ? `- Protocol ID: ${fields.protocolIdNumber}\n` : ''}`
    : '';
  
  const studyBackground = fields.studyBackground 
    ? `\n\n**Study Background:**\n${fields.studyBackground}`
    : '';
    
  const trialDetails = fields.trialInterventionDetails
    ? `\n\n**Trial Intervention Details:**\n${fields.trialInterventionDetails}`
    : '';
  
  const overview = `# STUDY OVERVIEW

**${title}**

This ${formatField(fields.phase, 'Phase 2')} clinical study is designed to evaluate the ${formatField(fields.primaryGoal, 'efficacy and safety')} of ${formatField(fields.interventionName, 'the investigational drug')} in patients with ${formatField(fields.condition, 'the target condition')}. The study will enroll ${formatField(fields.targetEnrollment, 'targeted number of')} participants across multiple clinical sites.${protocolInfo}${studyBackground}

**Primary Objective:**
${fields.primaryObjectiveStatement || fields.primaryGoal || 'To evaluate the efficacy and safety of the investigational product'}

**Key Outcome Measure:**
${fields.keyOutcome || 'Primary endpoint assessment at end of treatment'}${trialDetails}`;
  
  return overview;
}

// NEW: Investigational Product Section
function generateInvestigationalProductSection(discovery: any): string {
  const fields = extractAllFields(discovery);
  
  // Skip section if no intervention details
  if (!fields.interventionName && !fields.drugClass) {
    return '';
  }
  
  const section = `# INVESTIGATIONAL PRODUCT

**Product Name:** ${formatField(fields.interventionName)}  
**Drug Class:** ${formatField(fields.drugClass)}  
**FDA Status:** ${formatField(fields.fdaApprovalStatus)}  
${fields.drugCompoundNumber ? `**Compound Number:** ${fields.drugCompoundNumber}  \n` : ''}

**Medical Problem Addressed:**
${formatField(fields.medicalProblem, 'The investigational product addresses a significant unmet medical need')}

**Comparison to Existing Treatments:**
${formatField(fields.comparisonToExisting, 'This product offers potential advantages over current standard of care')}

**Regulatory Status:**
${formatField(fields.regulatoryStatus, 'Investigational New Drug (IND) application submitted')}

**Preclinical Studies:**
${formatField(fields.preclinicalStudies, 'Preclinical studies have demonstrated safety and efficacy in animal models')}

**Toxicity Studies:**
${formatField(fields.toxicityStudies, 'Comprehensive toxicology studies completed per regulatory requirements')}

**Clinical Trial History:**
${formatField(fields.clinicalTrialsHistory, 'Previous clinical studies have shown promising results')}`;

  return section;
}

function generateStudyDesignSection(discovery: any): string {
  const fields = extractAllFields(discovery);
  
  // Design descriptors
  const descriptors = fields.designDescriptors && fields.designDescriptors.length > 0
    ? `**Design Descriptors:** ${fields.designDescriptors.join(', ')}\n`
    : '';
    
  // Study arms
  const armsSection = fields.numberOfStudyArms
    ? `\n**Number of Study Arms:** ${fields.numberOfStudyArms}\n`
    : '';
  const armDescriptions = fields.studyArmDescriptions && fields.studyArmDescriptions.length > 0
    ? `**Study Arms:** ${fields.studyArmDescriptions.join('; ')}\n`
    : '';
    
  // Statistical section  
  const statisticalSection = `

## Statistical Considerations

**Sample Size:** ${formatField(fields.sampleSizeDetermination, 'Sample size calculation pending')}  
**Statistical Power:** ${fields.power ? `${fields.power}%` : 'TBD'}  
**Significance Level (α):** ${fields.alpha ? fields.alpha : 'TBD'}  
**Statistical Model:** ${formatField(fields.statisticalModel, 'Primary analysis model to be determined')}  
**Hypothesis Testing:** ${formatField(fields.hypothesisAndAnalysis, 'Hypothesis and analysis plan to be finalized')}  
**Interim Analysis:** ${formatField(fields.interimAnalysisPlan, 'No interim analysis planned')}  
**Multiple Testing Adjustment:** ${formatField(fields.multipleTesting, 'Appropriate adjustments will be applied')}  
**Missing Data Strategy:** ${formatField(fields.missingDataStrategy, 'Missing data will be handled per protocol')}`;

  // Adaptive design details
  const adaptiveSection = fields.hasAdaptiveDesign === true
    ? `\n**Adaptive Design Elements:** ${formatField(fields.adaptiveDesignDetails)}\n`
    : '';
  
  return `# STUDY DESIGN AND METHODOLOGY

${descriptors}**Design Type:** ${formatField(fields.designType, 'parallel-group')}  
**Blinding:** ${formatField(fields.blinding, 'double-blind')}  
**Control Type:** ${formatField(fields.controlType, 'placebo')}  
**Randomization Ratio:** ${formatField(fields.randomizationRatio, '1:1')}  
${armsSection}${armDescriptions}${adaptiveSection}

This is a ${formatField(fields.blinding, 'double-blind')}, ${formatField(fields.controlType, 'placebo')}-controlled, ${formatField(fields.designType, 'parallel-group')} study with ${formatField(fields.randomizationRatio, '1:1')} randomization. Participants will be randomly assigned to receive either ${formatField(fields.interventionName, 'the investigational drug')} or ${formatField(fields.controlType, 'placebo')} for a treatment period of ${formatField(fields.treatmentPeriod, '12 weeks')}.

The study consists of a ${formatField(fields.screeningPeriod, '2-week')} screening period, a ${formatField(fields.baselinePeriod, '1-week')} baseline assessment period, the ${formatField(fields.treatmentPeriod, '12 weeks')} treatment period, and a ${formatField(fields.followUpPeriod, '4 weeks')} safety follow-up period.

**Analysis Populations:** ${formatField(fields.analysisPopulations, 'Intent-to-treat and per-protocol populations')}
${statisticalSection}`;
}

function generateStudyPopulationSection(discovery: any): string {
  const fields = extractAllFields(discovery);
  
  // Format gender properly
  const gender = fields.gender === 'all' ? 'all genders' : fields.gender || 'all genders';
  
  // Geographic details
  const geographicDetails = fields.countriesEngaged && fields.countriesEngaged.length > 0
    ? `\n\n**Geographic Distribution:**\nThe study will be conducted in the following countries: ${fields.countriesEngaged.join(', ')}`
    : '';
  
  // Inclusion criteria
  const inclusionText = fields.inclusionCriteria && fields.inclusionCriteria.length > 0
    ? fields.inclusionCriteria.map((c: string, i: number) => `${i + 1}. ${c}`).join('\n')
    : 'Standard diagnostic criteria for the condition';
    
  // Exclusion criteria
  const exclusionText = fields.exclusionCriteria && fields.exclusionCriteria.length > 0
    ? fields.exclusionCriteria.map((c: string, i: number) => `${i + 1}. ${c}`).join('\n')
    : 'Standard exclusion criteria will apply';
  
  return `# STUDY POPULATION

**Target Enrollment:** ${formatField(fields.targetEnrollment, '100 participants')}  
**Age Range:** ${fields.ageMin || 18} to ${fields.ageMax || 65} years  
**Gender:** ${gender}  
**Geographic Scope:** ${formatField(fields.geographicScope, 'Regional')}

## Inclusion Criteria
${inclusionText}

## Exclusion Criteria  
${exclusionText}${geographicDetails}

The study population will be recruited across ${formatField(fields.geographicScope, 'the region')}, ensuring a diverse and representative sample that reflects the target patient population for this indication.`;
}

async function generateInterventionsSection(discovery: any): Promise<string> {
  const intervention = discovery.intervention?.name || 'Investigational Drug';
  const drugClass = discovery.intervention?.drugClass || 'therapeutic agent';
  const control = discovery.design?.controlType || 'placebo';
  const duration = discovery.timeline?.treatmentPeriod || '12 weeks';
  
  // In a real implementation, this would query KB for dosing
  // For now, use placeholder text
  return `# INTERVENTIONS AND TREATMENTS

Participants will receive either ${intervention}, a ${drugClass}, or ${control} for ${duration}. ${intervention} will be administered according to the protocol-specified dosing regimen, with dose adjustments permitted based on tolerability and safety assessments.

The ${control} will be identical in appearance to ensure maintenance of the blind. No rescue medications or concomitant treatments for the primary condition will be permitted during the treatment period, except as specified in the protocol for safety reasons.

Treatment compliance will be assessed at each study visit through drug accountability and participant diary review. Participants must maintain at least 80% compliance to remain in the per-protocol analysis population.`;
}

function generateEndpointsSection(discovery: any): string {
  const fields = extractAllFields(discovery);
  
  // Format secondary endpoints
  const secondaryEndpoints = fields.secondaryGoals && fields.secondaryGoals.length > 0
    ? fields.secondaryGoals.map((g: string, i: number) => `${i + 1}. ${g}`).join('\n')
    : 'Safety and tolerability measures';
    
  // Format exploratory objectives
  const exploratorySection = fields.exploratoryObjectives && fields.exploratoryObjectives.length > 0
    ? `\n\n**Exploratory Objectives:**\n${fields.exploratoryObjectives.map((o: string, i: number) => `${i + 1}. ${o}`).join('\n')}`
    : '';
    
  const exploratoryAnalyses = fields.exploratoryObjectiveAnalyses && fields.exploratoryObjectiveAnalyses.length > 0
    ? `\n\n**Exploratory Analyses:**\n${fields.exploratoryObjectiveAnalyses.map((a: string, i: number) => `${i + 1}. ${a}`).join('\n')}`
    : '';
  
  return `# STUDY ENDPOINTS AND OUTCOMES

## Primary Endpoint
**Objective:** ${formatField(fields.primaryObjectiveStatement, fields.primaryGoal || 'To evaluate efficacy')}  
**Outcome Measure:** ${formatField(fields.keyOutcome, 'Primary endpoint assessment')}  

## Secondary Endpoints
${secondaryEndpoints}${exploratorySection}${exploratoryAnalyses}`;
}

// NEW: Safety Monitoring Section
function generateSafetyMonitoringSection(discovery: any): string {
  const fields = extractAllFields(discovery);
  
  // Check if we have safety data
  const hasAECollection = fields.willCollectAESAE === true || fields.willCollectAESAE === 'Yes';
  
  // Format side effects lists
  const likelySideEffects = fields.likelySideEffects && fields.likelySideEffects.length > 0
    ? fields.likelySideEffects.map((se: string) => `• ${se}`).join('\n')
    : '• Common side effects to be monitored';
    
  const lessLikelySideEffects = fields.lessLikelySideEffects && fields.lessLikelySideEffects.length > 0
    ? `\n\n**Less Likely Side Effects:**\n${fields.lessLikelySideEffects.map((se: string) => `• ${se}`).join('\n')}`
    : '';
    
  const seriousSideEffects = fields.rareButSeriousSideEffects && fields.rareButSeriousSideEffects.length > 0
    ? `\n\n**Rare but Serious Adverse Events:**\n${fields.rareButSeriousSideEffects.map((se: string) => `• ${se}`).join('\n')}`
    : '';
    
  // Reproductive risks
  const reproductiveSection = fields.hasReproductiveRisks === true || fields.hasReproductiveRisks === 'Yes'
    ? `\n\n**Reproductive Risks:**\n${formatField(fields.reproductiveRiskDetails, 'Appropriate contraception required during study participation')}`
    : '';
  
  return `# SAFETY MONITORING

**Adverse Event Collection:** ${hasAECollection ? 'Yes - All AEs and SAEs will be collected and reported' : 'Standard safety monitoring'}

## Expected Side Effects
${likelySideEffects}${lessLikelySideEffects}${seriousSideEffects}${reproductiveSection}

## Safety Assessments
Safety will be evaluated through:
- Regular monitoring of adverse events and serious adverse events
- Vital signs assessments at each study visit
- Laboratory parameters including hematology, chemistry, and urinalysis
- Electrocardiogram monitoring as per protocol
- Physical examinations

All safety assessments will be conducted according to Good Clinical Practice guidelines and regulatory requirements.`;
}

function generateProceduresSection(discovery: any): string {
  const visits = discovery.timeline?.visits || [];
  const visitCount = visits.length || 6;
  const criticalVisits = visits.filter((v: any) => v.critical).map((v: any) => v.name).join(', ') || 'screening, baseline, and end of treatment';
  
  return `# STUDY PROCEDURES AND ASSESSMENTS

The study includes ${visitCount} scheduled visits over the course of participation. Critical visits include ${criticalVisits}, where primary endpoint assessments will be conducted.

**Screening procedures** include informed consent, medical history, physical examination, laboratory assessments, and confirmation of eligibility criteria.

**Treatment period assessments** will be conducted at regular intervals and include efficacy measurements, safety monitoring, adverse event collection, and medication compliance checks.

**End of study procedures** include final efficacy assessments, safety evaluations, and arrangements for post-study care if applicable. Participants will be followed for safety for ${discovery.timeline?.followUpPeriod || '4 weeks'} after the last dose of study medication.`;
}

async function generateOperationalSection(discovery: any): Promise<string> {
  const sites = discovery.operational?.numberOfSites || 'multiple';
  const recruitmentRate = discovery.operational?.recruitmentRate || '2-3 patients/site/month';
  const dataManagement = discovery.operational?.dataManagement === 'edc' ? 'electronic data capture (EDC)' : 
                         discovery.operational?.dataManagement || 'electronic data capture';
  const monitoring = discovery.operational?.monitoringApproach || 'risk-based monitoring';
  
  // In a real implementation, would query KB for regulatory requirements
  return `# OPERATIONAL AND REGULATORY CONSIDERATIONS

The study will be conducted at ${sites} clinical sites with an expected recruitment rate of ${recruitmentRate}. Screen failure rate is estimated at ${discovery.operational?.screenFailureRate || '20%'} with an expected dropout rate of ${discovery.operational?.dropoutRate || '15%'}.

Data will be collected using ${dataManagement} with ${monitoring} approach to ensure data quality and participant safety. Regular monitoring visits will be conducted according to the monitoring plan, with increased frequency for sites with higher enrollment or quality concerns.

The study will be conducted in accordance with Good Clinical Practice (GCP) guidelines, the Declaration of Helsinki, and all applicable regulatory requirements. Ethics committee approval will be obtained before study initiation at each site, and all participants will provide written informed consent before any study procedures are performed.

Safety oversight will be provided by the study team with regular review of safety data. Stopping rules and discontinuation criteria are defined in the protocol to ensure participant safety throughout the study.`;
}

// NEW: Laboratory and Biomarker Studies Section
function generateLaboratorySection(discovery: any): string {
  const fields = extractAllFields(discovery);
  
  // Check if biological samples will be collected
  const collectSamples = fields.willCollectBiologicalSamples === true || fields.willCollectBiologicalSamples === 'Yes';
  
  if (!collectSamples) {
    return `# LABORATORY AND BIOMARKER STUDIES

No biological samples will be collected for this study. Standard clinical laboratory assessments will be performed for safety monitoring only.`;
  }
  
  // Format specimen types
  const specimens = fields.biologicalSpecimens && fields.biologicalSpecimens.length > 0
    ? fields.biologicalSpecimens.map((s: string) => `• ${s}`).join('\n')
    : '• Standard clinical specimens';
    
  // Specialized studies
  const specializedStudies = [];
  if (fields.willConductPK === true || fields.willConductPK === 'Yes') {
    specializedStudies.push('Pharmacokinetic (PK) analysis');
  }
  if (fields.willConductBiomarker === true || fields.willConductBiomarker === 'Yes') {
    specializedStudies.push('Biomarker studies');
  }
  if (fields.willConductImmunogenicity === true || fields.willConductImmunogenicity === 'Yes') {
    specializedStudies.push('Immunogenicity testing');
  }
  if (fields.willConductGeneticTesting === true || fields.willConductGeneticTesting === 'Yes') {
    specializedStudies.push('Genetic/genomic analysis');
  }
  
  const specializedSection = specializedStudies.length > 0
    ? `\n\n## Specialized Laboratory Studies\n${specializedStudies.map(s => `• ${s}`).join('\n')}`
    : '';
  
  return `# LABORATORY AND BIOMARKER STUDIES

## Biological Sample Collection
**Sample Collection:** Yes - Biological samples will be collected for analysis

**Specimen Types:**
${specimens}

**Collection and Processing:**
${formatField(fields.collectionAndProcessing, 'Samples will be collected, processed, and stored according to protocol specifications')}${specializedSection}

## Laboratory Assessments
Standard safety laboratory assessments will include:
• Hematology panel with complete blood count
• Clinical chemistry panel including liver and kidney function
• Urinalysis
• Coagulation studies if clinically indicated

All laboratory samples will be analyzed at a central laboratory to ensure consistency and quality.`;
}

// NEW: Regulatory and Financial Framework Section  
function generateRegulatorySection(discovery: any): string {
  const fields = extractAllFields(discovery);
  
  // Governance section
  const governanceSection = `## Regulatory Oversight
**IRB/Ethics Committee:** ${formatField(fields.irbEthicsCommitteeName, 'Institutional Review Board approval pending')}  
**Study Sponsor:** ${formatField(fields.sponsorName, 'To be determined')}  
${fields.drugManufacturerName ? `**Drug Manufacturer:** ${fields.drugManufacturerName}  ` : ''}`;
  
  // CRO section if applicable
  const croSection = (fields.willUseCRO === true || fields.willUseCRO === 'Yes') && fields.croNameAndAddress
    ? `\n## Contract Research Organization\n${fields.croNameAndAddress}`
    : '';
    
  // Financial section
  const compensationSection = fields.willCompensateParticipants === true || fields.willCompensateParticipants === 'Yes'
    ? `\n## Participant Compensation\n**Compensation Provided:** Yes  \n**Per Visit Compensation:** ${formatField(fields.perVisitCompensation, 'As per protocol')}  \n**Completion Bonus:** ${formatField(fields.completionBonus, 'May be provided')}`
    : '\n## Participant Compensation\nNo compensation will be provided to study participants.';
    
  // Billing scenarios
  const billingSection = `

## Billing and Payment Structure
**Non-standard Care:** ${formatField(fields.sponsorPaysNonStandardCare, 'Sponsor covers study-specific procedures')}  
**Study Drug/Device:** ${formatField(fields.freeDrugDeviceProvided, 'Provided at no cost to participants')}`;
  
  return `# REGULATORY AND FINANCIAL FRAMEWORK
${governanceSection}${croSection}${compensationSection}${billingSection}

## Compliance and Quality Assurance
The study will be conducted in full compliance with:
• Good Clinical Practice (GCP) guidelines
• Declaration of Helsinki principles
• All applicable local and international regulations
• 21 CFR Part 11 for electronic records

Regular monitoring and auditing will ensure protocol compliance and data integrity throughout the study.`;
}