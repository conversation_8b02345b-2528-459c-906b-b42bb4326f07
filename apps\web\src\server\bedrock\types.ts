// Core domain types for TriaLynx Insights - ECS Environment

// Study Types
export type StudyType = 'drug' | 'device' | 'behavioral' | 'diagnostic' | 'other';
export type StudyPhase = 'phase1' | 'phase2' | 'phase3' | 'phase4' | 'unknown';
export type StudyStatus = 'completed' | 'active' | 'recruiting' | 'terminated' | 'withdrawn' | 'suspended';
export type Gender = 'all' | 'male' | 'female';

// Knowledge Base Types
export interface BedrockQueryRequest {
  query: string;
  field?: string;
  context?: {
    studyType?: string;
    condition?: string;
    drugClass?: string;
    isNewCompound?: boolean;
    mechanism?: string;
    phase?: string;
    primaryEndpoint?: string;
    designType?: string;
  };
  filters?: {
    startDate?: string;
    endDate?: string;
    studyType?: StudyType;
    phase?: StudyPhase;
    status?: StudyStatus[];
    minEnrollment?: number;
    maxEnrollment?: number;
  };
  maxResults?: number;
  includeTerminated?: boolean;
  retrieveOnly?: boolean;
  maxTokens?: number;
  temperature?: number;
}

export interface SourceDocument {
  nctId: string;
  title: string;
  s3Uri: string;
  excerpt: string;
  score?: number;
}

export interface InsightSection {
  title: string;
  content: string;
  type: 'recommendation' | 'information' | 'details' | 'calculation' | 'references' | 'considerations' | 'alternatives' | 'rationale';
  actionable?: boolean;
  actionableData?: {
    field: string;
    value: any;
    [key: string]: any;
  };
  citations: Array<{
    id: string;
    title: string;
    url: string;
    relevance: number;
  }>;
  confidence: number;
  endpoints?: Array<{
    endpoint: string;
    type: string;
    timeframe: string;
    method?: string;
  }>;
}

export interface BedrockQueryResponse {
  output?: string;
  sections?: InsightSection[];
  sources?: SourceDocument[];
  citations?: any[];
  sessionId?: string;
  metadata?: {
    totalResults?: number;
    queryTime?: number;
    searchTerms?: string[];
    appliedFilters?: Record<string, any>;
    modelUsed?: string;
    nextToken?: string;
  };
}