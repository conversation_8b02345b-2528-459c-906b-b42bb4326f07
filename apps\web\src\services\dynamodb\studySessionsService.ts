import { DynamoDBClient } from "@aws-sdk/client-dynamodb";
import { DynamoDBDocument } from "@aws-sdk/lib-dynamodb";
import type { StudyDesignSession } from "~/types/trial-design";
import { env } from "~/env";

// DynamoDB client configuration
const dynamoDBClient = DynamoDBDocument.from(
  new DynamoDBClient({
    region: env.AWS_REGION || "us-west-2",
    // In production, credentials would come from IAM roles or environment variables
  })
);

const TABLE_NAME = env.STUDY_SESSIONS_TABLE_NAME || "trialynx-insights-dev-study-design-sessions";

export class StudySessionsService {
  async createSession(session: StudyDesignSession): Promise<void> {
    try {
      await dynamoDBClient.put({
        TableName: TABLE_NAME,
        Item: {
          pk: session.id,
          ...session,
          createdAt: session.createdAt.toISOString(),
          updatedAt: session.updatedAt.toISOString(),
          expiresAt: Math.floor(session.expiresAt.getTime() / 1000), // Unix timestamp for TTL
        },
      });
    } catch (error) {
      console.error('Error creating session:', error);
      throw new Error('Failed to create session');
    }
  }

  async getSession(sessionId: string): Promise<StudyDesignSession | null> {
    try {
      const result = await dynamoDBClient.get({
        TableName: TABLE_NAME,
        Key: { pk: sessionId },
      });

      if (!result.Item) {
        return null;
      }

      // Convert date strings back to Date objects
      const item = result.Item;
      return {
        ...item,
        createdAt: new Date(item.createdAt),
        updatedAt: new Date(item.updatedAt),
        expiresAt: new Date(item.expiresAt * 1000), // Convert Unix timestamp back to Date
      } as StudyDesignSession;
    } catch (error) {
      console.error('Error getting session:', error);
      return null;
    }
  }

  async updateSession(sessionId: string, updates: Partial<StudyDesignSession>): Promise<void> {
    try {
      const updateExpression: string[] = [];
      const expressionAttributeNames: Record<string, string> = {};
      const expressionAttributeValues: Record<string, any> = {};

      // Build update expression dynamically
      Object.entries(updates).forEach(([key, value]) => {
        if (key !== 'id' && value !== undefined) {
          const attrName = `#${key}`;
          const attrValue = `:${key}`;
          
          updateExpression.push(`${attrName} = ${attrValue}`);
          expressionAttributeNames[attrName] = key;
          
          // Handle date objects
          if (value instanceof Date) {
            expressionAttributeValues[attrValue] = key === 'expiresAt' 
              ? Math.floor(value.getTime() / 1000) 
              : value.toISOString();
          } else {
            expressionAttributeValues[attrValue] = value;
          }
        }
      });

      // Always update the updatedAt timestamp
      updateExpression.push('#updatedAt = :updatedAt');
      expressionAttributeNames['#updatedAt'] = 'updatedAt';
      expressionAttributeValues[':updatedAt'] = new Date().toISOString();

      await dynamoDBClient.update({
        TableName: TABLE_NAME,
        Key: { pk: sessionId },
        UpdateExpression: `SET ${updateExpression.join(', ')}`,
        ExpressionAttributeNames: expressionAttributeNames,
        ExpressionAttributeValues: expressionAttributeValues,
      });
    } catch (error) {
      console.error('Error updating session:', error);
      throw new Error('Failed to update session');
    }
  }

  async deleteSession(sessionId: string): Promise<void> {
    try {
      await dynamoDBClient.delete({
        TableName: TABLE_NAME,
        Key: { pk: sessionId },
      });
    } catch (error) {
      console.error('Error deleting session:', error);
      throw new Error('Failed to delete session');
    }
  }

  async getUserSessions(userId: string, limit: number = 20): Promise<StudyDesignSession[]> {
    try {
      const result = await dynamoDBClient.query({
        TableName: TABLE_NAME,
        IndexName: 'UserIdIndex',
        KeyConditionExpression: 'userId = :userId',
        ExpressionAttributeValues: {
          ':userId': userId,
        },
        ScanIndexForward: false, // Most recent first
        Limit: limit,
      });

      return (result.Items || []).map(item => ({
        ...item,
        createdAt: new Date(item.createdAt),
        updatedAt: new Date(item.updatedAt),
        expiresAt: new Date(item.expiresAt * 1000),
      })) as StudyDesignSession[];
    } catch (error) {
      console.error('Error getting user sessions:', error);
      return [];
    }
  }
}

// Export a singleton instance
export const studySessionsService = new StudySessionsService();