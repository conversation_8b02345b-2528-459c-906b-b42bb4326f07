import { create } from "zustand";
import { persist } from "zustand/middleware";
import type { 
  DiscoveryData, 
  StudyResult, 
  GeneratedInsights,
  StudyType,
  StudyPhase,
  Gender,
  StudyDesignSession
} from "~/types/trial-design";

interface CompletedStudy {
  id: string;
  title: string;
  completedAt: string;
  synopsis: string;
  discovery: TrialDesignState["discovery"];
  phase?: StudyPhase | null;
}

interface TrialDesignState {
  // Session Management
  sessionId: string | null;
  currentStep: number;
  completedSteps: string[];
  
  // Discovery Data - using comprehensive types
  discovery: {
    studyType: StudyType | null;
    phase: StudyPhase | null;
    condition: string;
    
    // Step 2: Investigational Product (11 questions)
    intervention: {
      // Basic info
      name?: string;
      category?: string;
      mechanism?: string;
      class?: string;
      drugClass?: string;
      isNewCompound?: boolean;
      deviceClass?: string;
      
      // Enhanced drug product information (new)
      medicalProblem?: string;
      comparisonToExistingTreatments?: string;
      regulatoryStatus?: string;
      activeIngredients?: string[];
      inactiveIngredients?: string[];
      ingredientRationale?: string;
      preclinicalStudies?: string;
      toxicityStudies?: string;
      clinicalTrialsHistory?: string;
      keyFindings?: string;
      pregnancySafety?: 'safe' | 'unsafe' | 'unknown' | 'contraindicated';
      pregnancySafetyDetails?: string;
      fdaApprovalStatus?: 'approved' | 'investigational' | 'compassionate' | 'off-label';
      drugCompoundNumber?: string;
      indApplicationNumber?: string;
    };
    
    // Step 1: Study Overview & Background (6 questions)
    protocol?: {
      protocolAcronym?: string;
      protocolFullTitle?: string;
      studyBackground?: string;
      studyDetailsForAI?: string;
      protocolIdNumber?: string;
      trialInterventionDetails?: string;
      studyEventsAndActivities?: string;
      durationWithDates?: string;
    };
    
    // Step 3: Study Design & Statistical Analysis (19 questions)
    design?: {
      // Design descriptors
      designDescriptors?: string[];
      designType?: string;
      randomizationRatio?: string;
      blinding?: string;
      controlType?: string;
      
      // Enhanced design details
      interventionModels?: string[];
      hasActiveComparator?: boolean;
      controlMethods?: string[];
      numberOfStudyArms?: number;
      studyArmDescriptions?: string[];
      analysisPopulations?: string[];
      hasAdaptiveDesign?: boolean;
      adaptiveDesignDetails?: string;
    };
    
    statistical?: {
      sampleSizeDetermination?: string;
      statisticalModel?: string;
      hypothesisAndAnalysis?: string;
      interimAnalysisPlan?: string;
      power?: number;
      alpha?: number;
      multipleTesting?: string;
      missingDataStrategy?: string;
    };
    
    // Step 4: Study Population (8 questions)  
    population: {
      // Basic demographics
      ageMin?: number;
      ageMax?: number;
      gender: Gender;
      specificPopulation?: string;
      inclusionCriteria: string[];
      exclusionCriteria: string[];
      healthyVolunteers?: boolean;
      
      // Enhanced population details
      targetEnrollment?: string;
      geographicScope?: "local" | "national" | "international";
      trialAssignmentMethod?: string;
      numberOfSites?: string;
      siteDistribution?: string;
      countriesEngaged?: string[];
      sitesPerCountry?: Record<string, number>;
    };
    
    // Step 5: Safety Assessment (6 questions)
    safety?: {
      willCollectAESAE?: boolean;
      likelySideEffects?: string[];
      lessLikelySideEffects?: string[];
      rareButSeriousSideEffects?: string[];
      hasReproductiveRisks?: boolean;
      reproductiveRiskDetails?: string;
    };
    
    // Step 6a: Study Procedures & Timeline
    timeline?: {
      screeningPeriod?: string;
      baselinePeriod?: string;
      treatmentPeriod?: string;
      followUpPeriod?: string;
      totalDuration?: string;
      visits?: Array<{
        name: string;
        timepoint: string;
        procedures: string[];
        critical?: boolean;
      }>;
    };
    
    // Step 6b: Laboratory & Biomarker Studies (7 questions)
    laboratory?: {
      willCollectBiologicalSamples?: boolean;
      biologicalSpecimens?: string[];
      collectionAndProcessing?: string;
      willConductPK?: boolean;
      willConductBiomarker?: boolean;
      willConductImmunogenicity?: boolean;
      willConductGeneticTesting?: boolean;
    };
    
    // Step 6c: Operational Details
    operational?: {
      numberOfSites?: string;
      sitesPerCountry?: Record<string, number>;
      recruitmentRate?: string;
      screenFailureRate?: string;
      dropoutRate?: string;
      dataManagementSystem?: "edc" | "paper" | "hybrid";
      edcCTMSName?: string;
      monitoringApproach?: "on-site" | "remote" | "risk-based" | "hybrid";
    };
    
    // Enhanced objectives with formal statements
    objectives: {
      primaryGoal: string;
      keyOutcome: string;
      secondaryGoals?: string[];
      studyDuration?: string;
      followUpPeriod?: string;
      primaryAspectAssessing?: string[];
      primaryObjectiveStatement?: string;
      secondaryObjectiveStatement?: string;
      exploratoryObjectives?: string[];
      exploratoryObjectiveAnalyses?: string[];
    };
    
    // Step 7: Regulatory, Financial & Legal (10 questions)
    regulatory?: {
      // Regulatory
      irbName?: string;
      irbEthicsCommitteeName?: string;
      sponsorName?: string;
      drugManufacturerName?: string;
      drugManufacturerAddress?: string;
      deviceManufacturerName?: string;
      willUseCRO?: boolean;
      croName?: string;
      croNameAndAddress?: string;
      croAddress?: string;
      croContact?: string;
      dataEvaluationCommittees?: string[];
      independentCommittees?: string[];
      
      // Financial
      willParticipantsBeCompensated?: boolean;
      willCompensateParticipants?: boolean;
      compensationDetails?: string;
      perVisitCompensation?: string;
      completionBonus?: string;
      paymentSchedule?: string;
      mileageRate?: string;
      parkingReimbursement?: string;
      publicTransportCap?: string;
      hotelCap?: string;
      mealAllowance?: string;
      
      // Billing scenarios - individual fields
      sponsorPaysNonStandardCare?: string;
      freeDrugDeviceProvided?: string;
      standardCareChargedToParticipant?: string;
      noBillableProcedures?: string;
      
      // Legacy billing scenarios array
      whoWillPay?: string[];
      billingScenarios?: string[];
    };
  };
  
  // Knowledge Base Results
  similarStudies: StudyResult[];
  selectedStudies: string[];
  isLoadingStudies: boolean;
  
  // Insights
  insights: GeneratedInsights | null;
  isGeneratingInsights: boolean;
  
  // Synopsis
  synopsis: string | null;
  
  // Completed Studies
  completedStudies: CompletedStudy[];
  
  // In-Progress Sessions
  inProgressSessions: StudyDesignSession[];
  
  // Insights Cache
  insightsCache: Record<string, any>;
  documentCache: Record<string, any>;
  
  // Insights Tracking
  appliedInsightsCount: number;
  
  // Reference Tracking
  supportingStudies: Map<string, Array<{ studyId: string; citation: any; field: string }>>;
  sourceStudies: Map<string, any>;
  studyCitations: Map<string, any>;
  
  // Actions
  setSessionId: (id: string) => void;
  setCurrentStep: (step: number) => void;
  markStepCompleted: (step: string) => void;
  updateDiscovery: (data: Partial<TrialDesignState["discovery"]>) => void;
  setSimilarStudies: (studies: StudyResult[]) => void;
  toggleStudySelection: (studyId: string) => void;
  setInsights: (insights: GeneratedInsights) => void;
  setSynopsis: (synopsis: string) => void;
  saveCompletedStudy: (title: string, synopsis: string) => void;
  setInProgressSessions: (sessions: StudyDesignSession[]) => void;
  addInProgressSession: (session: StudyDesignSession) => void;
  removeInProgressSession: (sessionId: string) => void;
  loadFromSession: (session: StudyDesignSession) => void;
  cacheInsights: (key: string, data: any) => void;
  cacheDocument: (url: string, data: any) => void;
  incrementInsightsCounter: () => void;
  addSupportingStudy: (field: string, studyId: string, citation: any) => void;
  addSourceStudy: (studyId: string, citation: any) => void;
  getSupportingStudies: () => Array<{ studyId: string; citation: any; field: string }>;
  getSourceStudies: () => any[];
  resetStore: () => void;
  resetSession: () => void;
}

const initialState = {
  sessionId: null,
  currentStep: 0,
  completedSteps: [],
  discovery: {
    studyType: null,
    phase: null,
    condition: "",
    intervention: {},
    protocol: {},
    design: {},
    statistical: {},
    population: {
      gender: "all" as const,
      inclusionCriteria: [],
      exclusionCriteria: [],
      countriesEngaged: [],
    },
    safety: {
      likelySideEffects: [],
      lessLikelySideEffects: [],
      rareButSeriousSideEffects: [],
    },
    timeline: {
      visits: [],
    },
    laboratory: {
      biologicalSpecimens: [],
    },
    operational: {},
    objectives: {
      primaryGoal: "",
      keyOutcome: "",
      primaryAspectAssessing: [],
      exploratoryObjectives: [],
      exploratoryObjectiveAnalyses: [],
    },
    regulatory: {
      dataEvaluationCommittees: [],
      independentCommittees: [],
      whoWillPay: [],
      billingScenarios: [],
    },
  },
  similarStudies: [],
  selectedStudies: [],
  isLoadingStudies: false,
  insights: null,
  isGeneratingInsights: false,
  synopsis: null,
  completedStudies: [],
  inProgressSessions: [],
  insightsCache: {},
  documentCache: {},
  appliedInsightsCount: 0,
  supportingStudies: new Map(),
  sourceStudies: new Map(),
  studyCitations: new Map(),
};

export const useTrialDesignStore = create<TrialDesignState>()(
  persist(
    (set) => ({
      ...initialState,
      
      setSessionId: (id) => set({ sessionId: id }),
      
      setCurrentStep: (step) => set({ currentStep: step }),
      
      markStepCompleted: (step) =>
        set((state) => ({
          completedSteps: [...new Set([...state.completedSteps, step])],
        })),
      
      updateDiscovery: (data) =>
        set((state) => ({
          discovery: {
            ...state.discovery,
            ...data,
          },
        })),
      
      setSimilarStudies: (studies) =>
        set({
          similarStudies: studies,
          isLoadingStudies: false,
        }),
      
      toggleStudySelection: (studyId) =>
        set((state) => ({
          selectedStudies: state.selectedStudies.includes(studyId)
            ? state.selectedStudies.filter((id) => id !== studyId)
            : [...state.selectedStudies, studyId],
        })),
      
      setInsights: (insights) =>
        set({
          insights,
          isGeneratingInsights: false,
        }),
      
      setSynopsis: (synopsis) => set({ synopsis }),
      
      saveCompletedStudy: (title, synopsis) =>
        set((state) => ({
          completedStudies: [
            ...state.completedStudies,
            {
              id: `study-${Date.now()}`,
              title,
              completedAt: new Date().toISOString(),
              synopsis,
              discovery: state.discovery,
              phase: state.discovery.phase,
            },
          ],
        })),
      
      setInProgressSessions: (sessions) => set({ inProgressSessions: sessions }),
      
      addInProgressSession: (session) =>
        set((state) => ({
          inProgressSessions: [
            ...state.inProgressSessions.filter(s => s.id !== session.id),
            session,
          ],
        })),
      
      removeInProgressSession: (sessionId) =>
        set((state) => ({
          inProgressSessions: state.inProgressSessions.filter(s => s.id !== sessionId),
        })),
      
      loadFromSession: (session) =>
        set({
          sessionId: session.id,
          currentStep: parseInt(session.currentStep) || 0,
          completedSteps: session.completedSteps || [],
          discovery: session.discovery,
        }),
      
      cacheInsights: (key, data) =>
        set((state) => ({
          insightsCache: {
            ...state.insightsCache,
            [key]: data,
          },
        })),
      
      cacheDocument: (url, data) =>
        set((state) => ({
          documentCache: {
            ...state.documentCache,
            [url]: data,
          },
        })),
      
      incrementInsightsCounter: () =>
        set((state) => ({
          appliedInsightsCount: state.appliedInsightsCount + 1,
        })),
      
      addSupportingStudy: (field, studyId, citation) =>
        set((state) => {
          const supportingStudies = new Map(state.supportingStudies);
          const key = `${field}-${studyId}`;
          const existing = supportingStudies.get(key) || [];
          supportingStudies.set(key, [...existing, { studyId, citation, field }]);
          
          // Also add to studyCitations for easy lookup
          const studyCitations = new Map(state.studyCitations);
          studyCitations.set(studyId, citation);
          
          return { supportingStudies, studyCitations };
        }),
      
      addSourceStudy: (studyId, citation) =>
        set((state) => {
          const sourceStudies = new Map(state.sourceStudies);
          sourceStudies.set(studyId, citation);
          
          // Also add to studyCitations for easy lookup
          const studyCitations = new Map(state.studyCitations);
          studyCitations.set(studyId, citation);
          
          return { sourceStudies, studyCitations };
        }),
      
      getSupportingStudies: () => {
        const state = useTrialDesignStore.getState();
        const allStudies: Array<{ studyId: string; citation: any; field: string }> = [];
        state.supportingStudies.forEach((studies: Array<{ studyId: string; citation: any; field: string }>) => {
          allStudies.push(...studies);
        });
        return allStudies;
      },
      
      getSourceStudies: (): Array<{ studyId: string; citation: any; field: string }> => {
        const state: TrialDesignState = useTrialDesignStore.getState();
        return Array.from(state.sourceStudies.values());
      },
      
      resetStore: () => set(initialState),
      
      resetSession: () => set({
        sessionId: null,
        currentStep: 0,
        completedSteps: [],
        selectedStudies: [],
        insights: null,
        discovery: initialState.discovery,
        synopsis: null,
      }),
    }),
    {
      name: "trial-design-storage",
      partialize: (state) => ({
        sessionId: state.sessionId,
        currentStep: state.currentStep,
        completedSteps: state.completedSteps,
        discovery: state.discovery,
        selectedStudies: state.selectedStudies,
        synopsis: state.synopsis,
        completedStudies: state.completedStudies,
        inProgressSessions: state.inProgressSessions,
        appliedInsightsCount: state.appliedInsightsCount,
      }),
    }
  )
);