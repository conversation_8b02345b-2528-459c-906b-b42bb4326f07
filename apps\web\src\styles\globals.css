@import "tailwindcss";
@import "./safari-fixes.css";

@theme {
  --font-sans: var(--font-inter), ui-sans-serif, system-ui, sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  
  /* TriaLynx Brand Colors */
  --color-primary: #5A32FA;
  --color-secondary: #00C4CC;
  --color-accent: #7D2AE8;
  
  /* Gradient Colors for Magic UI animations */
  --color-1: #5A32FA;
  --color-2: #7D2AE8;
  --color-3: #00C4CC;
  --color-4: #4CC9F0;
  --color-5: #8B5CF6;
}

/* Custom CSS Variables for Light/Dark Mode */
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 251 91% 58%; /* TriaLynx Purple */
    --primary-foreground: 0 0% 100%;
    --secondary: 182 100% 40%; /* TriaLynx Teal */
    --secondary-foreground: 0 0% 100%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 270 75% 60%; /* TriaLynx Accent */
    --accent-foreground: 0 0% 100%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 251 91% 58%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 251 91% 58%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 182 100% 40%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 270 75% 60%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 251 91% 58%;
  }
}

/* Utility classes for gradients */
@layer utilities {
  .gradient-primary {
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-accent) 100%);
  }
  
  .gradient-secondary {
    background: linear-gradient(135deg, var(--color-secondary) 0%, var(--color-4) 100%);
  }
  
  .text-gradient {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-[#5A32FA] to-[#00C4CC];
  }
  
  /* Animation for Magic UI components */
  @keyframes rainbow {
    0% {
      background-position: 0% 50%;
    }
    100% {
      background-position: 200% 50%;
    }
  }
  
  .animate-rainbow {
    animation: rainbow 3s linear infinite;
    background-size: 200% auto;
  }

  @keyframes shimmer-slide {
    to {
      transform: translate(calc(100cqw - 100%), 0);
    }
  }

  .animate-shimmer-slide {
    animation: shimmer-slide var(--speed, 3s) ease-in-out infinite alternate;
  }

  @keyframes spin-around {
    0% {
      transform: translateZ(0) rotate(0);
    }
    15%, 35% {
      transform: translateZ(0) rotate(90deg);
    }
    65%, 85% {
      transform: translateZ(0) rotate(270deg);
    }
    100% {
      transform: translateZ(0) rotate(360deg);
    }
  }

  .animate-spin-around {
    animation: spin-around calc(var(--speed, 3s) * 2) ease-in-out infinite;
  }

  @keyframes rippling {
    0% {
      transform: scale(0);
      opacity: 1;
    }
    100% {
      transform: scale(2);
      opacity: 0;
    }
  }

  .animate-rippling {
    animation: rippling 600ms ease-out;
  }

  /* Force solid backgrounds for dropdown and dialog components */
  [data-radix-dropdown-menu-content],
  [data-radix-popper-content-wrapper] [data-side],
  .radix-dropdown-menu-content {
    background: white !important;
    background-color: white !important;
    border: 1px solid rgb(229 231 235) !important;
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1) !important;
  }

  [data-radix-alert-dialog-content],
  .radix-alert-dialog-content {
    background: white !important;
    background-color: white !important;
    border: 1px solid rgb(229 231 235) !important;
    box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25) !important;
  }

  [data-radix-alert-dialog-overlay],
  .radix-alert-dialog-overlay {
    background: rgba(0, 0, 0, 0.5) !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
  }

  /* Ultra-specific targeting for shadcn/ui components */
  [data-slot="dropdown-menu-content"] {
    background: white !important;
    background-color: white !important;
    border: 1px solid rgb(229 231 235) !important;
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1) !important;
    z-index: 100 !important;
  }

  [data-slot="alert-dialog-content"] {
    background: white !important;
    background-color: white !important;
    border: 1px solid rgb(229 231 235) !important;
    box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25) !important;
    z-index: 60 !important;
  }

  [data-slot="alert-dialog-overlay"] {
    background: rgba(0, 0, 0, 0.5) !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
    z-index: 55 !important;
  }

  /* Ensure overlay cleanup and prevent persistent overlays */
  [data-radix-alert-dialog-overlay] {
    pointer-events: auto !important;
  }

  /* Force cleanup of any stuck overlays */
  body[data-overlay-cleanup] [data-radix-alert-dialog-overlay] {
    display: none !important;
  }

  /* CRITICAL FIX: Prevent HTML element from capturing pointer events */
  /* This fixes the issue where Radix UI AlertDialog scroll lock */
  /* modifies the HTML element and doesn't properly clean up */
  html {
    pointer-events: none !important;
  }
  
  /* Allow pointer events on body and all its children */
  body, body * {
    pointer-events: auto !important;
  }
  
  /* Specific override for Radix UI overlay elements */
  [data-radix-alert-dialog-overlay] {
    pointer-events: auto !important;
  }
  
  /* Emergency cleanup class for HTML element CSS modifications */
  html.radix-cleanup {
    pointer-events: none !important;
    overflow: visible !important;
    position: static !important;
    z-index: auto !important;
  }
  
  html.radix-cleanup body, 
  html.radix-cleanup body * {
    pointer-events: auto !important;
  }
}
