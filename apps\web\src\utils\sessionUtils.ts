import type { StudyDesignSession } from "~/types/trial-design";

/**
 * Get a display title for a study design session
 */
export function getSessionTitle(session: StudyDesignSession): string {
  const condition = session.discovery?.condition?.trim();
  const studyType = session.discovery?.studyType;
  const phase = session.discovery?.phase;
  
  // If we have a condition, check if it's already a formatted title
  if (condition) {
    // Check if the condition already contains "Study" at the end - if so, it's likely a custom title
    if (condition.toLowerCase().endsWith(' study') || condition.toLowerCase().includes('study')) {
      // This is likely a custom renamed title, return as-is
      return condition;
    }
    
    // Otherwise, it's a raw condition, format it with study type and phase
    const typeText = studyType ? ` ${studyType.charAt(0).toUpperCase() + studyType.slice(1)}` : '';
    const phaseText = phase ? ` Phase ${phase.toUpperCase()}` : '';
    return `${condition}${typeText}${phaseText} Study`;
  }
  
  // Fallback to study type and phase
  if (studyType && phase) {
    return `${studyType.charAt(0).toUpperCase() + studyType.slice(1)} Phase ${phase.toUpperCase()} Study`;
  }
  
  // Fallback to study type only
  if (studyType) {
    return `${studyType.charAt(0).toUpperCase() + studyType.slice(1)} Study`;
  }
  
  // Final fallback
  return "New Clinical Trial";
}

/**
 * Calculate progress percentage based on completed steps
 */
export function getSessionProgress(session: StudyDesignSession): number {
  const totalSteps = 8; // Based on the wizard steps
  const completedCount = session.completedSteps?.length || 0;
  return Math.round((completedCount / totalSteps) * 100);
}

/**
 * Get a human-readable status for the session
 */
export function getSessionDisplayStatus(session: StudyDesignSession): string {
  const progress = getSessionProgress(session);
  
  if (progress === 0) return "Just started";
  if (progress < 25) return "Getting started";
  if (progress < 50) return "Making progress";
  if (progress < 75) return "Well underway";
  if (progress < 100) return "Nearly complete";
  return "Ready for review";
}

/**
 * Format the last updated time in a human-readable way
 */
export function formatLastUpdated(updatedAt: Date): string {
  const now = new Date();
  const diffMs = now.getTime() - updatedAt.getTime();
  const diffMinutes = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (diffMinutes < 1) return "Just now";
  if (diffMinutes < 60) return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
  if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
  if (diffDays < 7) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
  
  // For older sessions, show the actual date
  return updatedAt.toLocaleDateString('en-US', { 
    month: 'short', 
    day: 'numeric',
    year: updatedAt.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
  });
}

/**
 * Get step names for progress display
 */
export function getStepNames(): string[] {
  return [
    "Study Overview",
    "Investigational Product", 
    "Study Design",
    "Study Population",
    "Safety Assessment",
    "Study Procedures",
    "Regulatory & Financial",
    "Review & Synopsis"
  ];
}

/**
 * Get the current step name
 */
export function getCurrentStepName(session: StudyDesignSession): string {
  const stepNames = getStepNames();
  const currentStepIndex = Math.max(0, (session.completedSteps?.length || 0));
  return stepNames[currentStepIndex] || "Review & Synopsis";
}