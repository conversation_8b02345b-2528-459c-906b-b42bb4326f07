# Outputs for prod account DNS setup

output "certificate_validation_records" {
  description = "Certificate validation records created"
  value = {
    for k, v in aws_route53_record.cert_validation : k => {
      name    = v.name
      type    = v.type
      records = v.records
    }
  }
}

output "app_domain_records" {
  description = "Application domain records created"
  value = {
    a_record = {
      name  = aws_route53_record.app_alias_a.name
      type  = aws_route53_record.app_alias_a.type
      alias = aws_route53_record.app_alias_a.alias
    }
    aaaa_record = {
      name  = aws_route53_record.app_alias_aaaa.name
      type  = aws_route53_record.app_alias_aaaa.type
      alias = aws_route53_record.app_alias_aaaa.alias
    }
  }
}

output "ses_verification_record" {
  description = "SES verification record created"
  value = {
    name    = aws_route53_record.ses_verification.name
    type    = aws_route53_record.ses_verification.type
    records = aws_route53_record.ses_verification.records
  }
}

output "ses_dkim_records" {
  description = "SES DKIM records created"
  value = {
    for i, record in aws_route53_record.ses_dkim : i => {
      name    = record.name
      type    = record.type
      records = record.records
    }
  }
}

output "route53_zone_id" {
  description = "Route 53 zone ID"
  value       = data.aws_route53_zone.main.zone_id
}

output "domain_name" {
  description = "Domain name configured"
  value       = var.app_domain_name
}