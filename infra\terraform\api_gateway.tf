# API Gateway resources - DISABLED for ECS-only deployment
# Commented out to eliminate API Gateway from infrastructure

# # API Gateway REST API
# resource "aws_api_gateway_rest_api" "main" {
#   name        = local.api_gateway_name
#   description = "API Gateway for TriaLynx Insights"
#   
#   endpoint_configuration {
#     types = ["REGIONAL"]
#   }
#   
#   tags = local.common_tags
# }

# # API Gateway Resource
# resource "aws_api_gateway_resource" "query" {
#   rest_api_id = aws_api_gateway_rest_api.main.id
#   parent_id   = aws_api_gateway_rest_api.main.root_resource_id
#   path_part   = "query"
# }

# # API Gateway Method
# resource "aws_api_gateway_method" "query_post" {
#   rest_api_id   = aws_api_gateway_rest_api.main.id
#   resource_id   = aws_api_gateway_resource.query.id
#   http_method   = "POST"
#   authorization = "CUSTOM"
#   authorizer_id = aws_api_gateway_authorizer.jwt.id
# }

# # API Gateway Integration
# resource "aws_api_gateway_integration" "lambda" {
#   rest_api_id = aws_api_gateway_rest_api.main.id
#   resource_id = aws_api_gateway_resource.query.id
#   http_method = aws_api_gateway_method.query_post.http_method
#   
#   integration_http_method = "POST"
#   type                    = "AWS_PROXY"
#   uri                     = aws_lambda_function.query_knowledge_base.invoke_arn
# }

# # API Gateway Authorizer (JWT)
# resource "aws_api_gateway_authorizer" "jwt" {
#   name                   = "${local.project_name}-${var.environment}-jwt-authorizer"
#   rest_api_id            = aws_api_gateway_rest_api.main.id
#   type                   = "TOKEN"
#   authorizer_uri         = aws_lambda_function.jwt_authorizer.invoke_arn
#   authorizer_credentials = aws_iam_role.api_gateway_authorizer_role.arn
#   identity_source        = "method.request.header.Authorization"
#   
#   authorizer_result_ttl_in_seconds = 300
# }

# # OPTIONS method for CORS
# resource "aws_api_gateway_method" "query_options" {
#   rest_api_id   = aws_api_gateway_rest_api.main.id
#   resource_id   = aws_api_gateway_resource.query.id
#   http_method   = "OPTIONS"
#   authorization = "NONE"
# }

# resource "aws_api_gateway_integration" "query_options" {
#   rest_api_id = aws_api_gateway_rest_api.main.id
#   resource_id = aws_api_gateway_resource.query.id
#   http_method = aws_api_gateway_method.query_options.http_method
#   type        = "MOCK"
#   
#   request_templates = {
#     "application/json" = jsonencode({
#       statusCode = 200
#     })
#   }
# }

# resource "aws_api_gateway_method_response" "query_options" {
#   rest_api_id = aws_api_gateway_rest_api.main.id
#   resource_id = aws_api_gateway_resource.query.id
#   http_method = aws_api_gateway_method.query_options.http_method
#   status_code = "200"
#   
#   response_parameters = {
#     "method.response.header.Access-Control-Allow-Headers" = true
#     "method.response.header.Access-Control-Allow-Methods" = true
#     "method.response.header.Access-Control-Allow-Origin"  = true
#   }
#   
#   response_models = {
#     "application/json" = "Empty"
#   }
# }

# resource "aws_api_gateway_integration_response" "query_options" {
#   rest_api_id = aws_api_gateway_rest_api.main.id
#   resource_id = aws_api_gateway_resource.query.id
#   http_method = aws_api_gateway_method.query_options.http_method
#   status_code = aws_api_gateway_method_response.query_options.status_code
#   
#   response_parameters = {
#     "method.response.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
#     "method.response.header.Access-Control-Allow-Methods" = "'POST,OPTIONS'"
#     "method.response.header.Access-Control-Allow-Origin"  = "'${join(",", var.cors_allowed_origins)}'"
#   }
# }

# # API Gateway Deployment
# resource "aws_api_gateway_deployment" "main" {
#   depends_on = [
#     aws_api_gateway_integration.lambda,
#     aws_api_gateway_integration.query_options
#   ]
#   
#   rest_api_id = aws_api_gateway_rest_api.main.id
#   
#   triggers = {
#     redeployment = sha1(jsonencode([
#       aws_api_gateway_resource.query.id,
#       aws_api_gateway_method.query_post.id,
#       aws_api_gateway_integration.lambda.id,
#     ]))
#   }
#   
#   lifecycle {
#     create_before_destroy = true
#   }
# }

# # API Gateway Stage
# resource "aws_api_gateway_stage" "main" {
#   deployment_id = aws_api_gateway_deployment.main.id
#   rest_api_id   = aws_api_gateway_rest_api.main.id
#   stage_name    = var.environment
#   
#   tags = local.common_tags
# }

# # IAM Role for API Gateway Authorizer
# resource "aws_iam_role" "api_gateway_authorizer_role" {
#   name = "${local.project_name}-${var.environment}-api-gateway-authorizer-role"
#   
#   assume_role_policy = jsonencode({
#     Version = "2012-10-17"
#     Statement = [
#       {
#         Action = "sts:AssumeRole"
#         Effect = "Allow"
#         Principal = {
#           Service = "apigateway.amazonaws.com"
#         }
#       }
#     ]
#   })
#   
#   tags = local.common_tags
# }

# # IAM policy for API Gateway Authorizer to invoke Lambda
# resource "aws_iam_role_policy" "api_gateway_authorizer_lambda_invoke" {
#   name = "${local.project_name}-${var.environment}-api-gateway-authorizer-lambda-invoke"
#   role = aws_iam_role.api_gateway_authorizer_role.id

#   policy = jsonencode({
#     Version = "2012-10-17"
#     Statement = [
#       {
#         Effect = "Allow"
#         Action = "lambda:InvokeFunction"
#         Resource = aws_lambda_function.jwt_authorizer.arn
#       }
#     ]
#   })
# }