# Terraform Backend Configuration
# This stores the Terraform state remotely in S3 for team collaboration and safety

terraform {
  backend "s3" {
    # Replace with your actual bucket name after creating it
    bucket = "trialynx-insights-terraform-state-dev"
    key    = "insights/dev/terraform.tfstate"
    region = "us-west-2"
    
    # Enable state locking with DynamoDB (recommended)
    dynamodb_table = "trialynx-terraform-locks"
    
    # Enable encryption
    encrypt = true
  }
}