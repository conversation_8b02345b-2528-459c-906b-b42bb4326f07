# DynamoDB table for NextAuth.js session storage
resource "aws_dynamodb_table" "next_auth" {
  name           = "${local.name_prefix}-next-auth"
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "pk"
  range_key      = "sk"

  attribute {
    name = "pk"
    type = "S"
  }

  attribute {
    name = "sk"
    type = "S"
  }

  attribute {
    name = "GSI1PK"
    type = "S"
  }

  attribute {
    name = "GSI1SK"
    type = "S"
  }

  global_secondary_index {
    name            = "GSI1"
    hash_key        = "GSI1PK"
    range_key       = "GSI1SK"
    projection_type = "ALL"
  }

  ttl {
    attribute_name = "expires"
    enabled        = true
  }

  point_in_time_recovery {
    enabled = var.environment == "prod"
  }

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-next-auth"
    Purpose = "NextAuth.js session storage"
  })
}

# DynamoDB table for allowlist (approved users)
resource "aws_dynamodb_table" "allowlist" {
  name         = "${local.name_prefix}-allowlist"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "pk"

  attribute {
    name = "pk"
    type = "S"
  }

  attribute {
    name = "email"
    type = "S"
  }

  attribute {
    name = "status"
    type = "S"
  }

  global_secondary_index {
    name            = "EmailIndex"
    hash_key        = "email"
    projection_type = "ALL"
  }

  global_secondary_index {
    name            = "StatusIndex"
    hash_key        = "status"
    projection_type = "ALL"
  }

  point_in_time_recovery {
    enabled = var.environment == "prod"
  }

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-allowlist"
    Purpose = "User access control allowlist"
  })
}

# DynamoDB table for study design sessions
resource "aws_dynamodb_table" "study_design_sessions" {
  name         = "${local.name_prefix}-study-design-sessions"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "pk"

  attribute {
    name = "pk"
    type = "S"
  }

  attribute {
    name = "userId"
    type = "S"
  }

  attribute {
    name = "status"
    type = "S"
  }

  attribute {
    name = "createdAt"
    type = "S"
  }

  global_secondary_index {
    name            = "UserIdIndex"
    hash_key        = "userId"
    range_key       = "createdAt"
    projection_type = "ALL"
  }

  global_secondary_index {
    name            = "StatusIndex"
    hash_key        = "status"
    range_key       = "createdAt"
    projection_type = "ALL"
  }

  ttl {
    attribute_name = "expiresAt"
    enabled        = true
  }

  point_in_time_recovery {
    enabled = var.environment == "prod"
  }

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-study-design-sessions"
    Purpose = "Study design wizard session storage"
  })
}

# DynamoDB table for waitlist (pending users)
resource "aws_dynamodb_table" "waitlist" {
  name         = "${local.name_prefix}-waitlist"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "pk"

  attribute {
    name = "pk"
    type = "S"
  }

  attribute {
    name = "email"
    type = "S"
  }

  attribute {
    name = "createdAt"
    type = "S"
  }

  global_secondary_index {
    name            = "EmailIndex"
    hash_key        = "email"
    projection_type = "ALL"
  }

  global_secondary_index {
    name            = "CreatedAtIndex"
    hash_key        = "createdAt"
    projection_type = "ALL"
  }

  point_in_time_recovery {
    enabled = var.environment == "prod"
  }

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-waitlist"
    Purpose = "User access control waitlist"
  })
}

# IAM policy for DynamoDB access
resource "aws_iam_policy" "dynamodb_access" {
  name        = "${local.name_prefix}-dynamodb-access"
  description = "IAM policy for DynamoDB access for NextAuth and user management"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "dynamodb:GetItem",
          "dynamodb:PutItem",
          "dynamodb:UpdateItem",
          "dynamodb:DeleteItem",
          "dynamodb:Query",
          "dynamodb:Scan",
          "dynamodb:BatchGetItem",
          "dynamodb:BatchWriteItem",
          "dynamodb:DescribeTable"
        ]
        Resource = [
          aws_dynamodb_table.next_auth.arn,
          "${aws_dynamodb_table.next_auth.arn}/index/*",
          aws_dynamodb_table.allowlist.arn,
          "${aws_dynamodb_table.allowlist.arn}/index/*",
          aws_dynamodb_table.waitlist.arn,
          "${aws_dynamodb_table.waitlist.arn}/index/*",
          aws_dynamodb_table.study_design_sessions.arn,
          "${aws_dynamodb_table.study_design_sessions.arn}/index/*"
        ]
      }
    ]
  })

  tags = local.common_tags
}