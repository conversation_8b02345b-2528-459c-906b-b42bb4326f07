# Route 53 Hosted Zone for subdomain delegation
# This creates a hosted zone for insights.trialynx.io in the dev account

resource "aws_route53_zone" "insights_subdomain" {
  name = "insights.trialynx.io"

  tags = merge(local.common_tags, {
    Name = "insights.trialynx.io"
    Purpose = "Subdomain delegation for Trialynx Insights"
  })
}

# DNS records for the application
resource "aws_route53_record" "app_a" {
  zone_id = aws_route53_zone.insights_subdomain.zone_id
  name    = var.domain_name
  type    = "A"

  alias {
    name                   = aws_lb.main.dns_name
    zone_id                = aws_lb.main.zone_id
    evaluate_target_health = false
  }
}

resource "aws_route53_record" "app_aaaa" {
  zone_id = aws_route53_zone.insights_subdomain.zone_id
  name    = var.domain_name
  type    = "AAAA"

  alias {
    name                   = aws_lb.main.dns_name
    zone_id                = aws_lb.main.zone_id
    evaluate_target_health = false
  }
}

# Certificate validation records (automatic)
resource "aws_route53_record" "cert_validation" {
  for_each = {
    for dvo in aws_acm_certificate.app_cert.domain_validation_options : dvo.domain_name => {
      name   = dvo.resource_record_name
      record = dvo.resource_record_value
      type   = dvo.resource_record_type
    }
  }

  allow_overwrite = true
  name            = each.value.name
  records         = [each.value.record]
  ttl             = 60
  type            = each.value.type
  zone_id         = aws_route53_zone.insights_subdomain.zone_id
}

# Update the certificate validation to use the local zone - TEMPORARILY DISABLED
# resource "aws_acm_certificate_validation" "app_cert_local" {
#   certificate_arn         = aws_acm_certificate.app_cert.arn
#   validation_record_fqdns = [for record in aws_route53_record.cert_validation : record.fqdn]

#   timeouts {
#     create = "5m"
#   }
# }