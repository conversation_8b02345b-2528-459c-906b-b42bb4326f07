# SES Domain Identity for sending emails
# Note: DNS records for domain verification need to be created in the prod account

resource "aws_ses_domain_identity" "main" {
  domain = var.ses_domain
}

# SES DKIM configuration
resource "aws_ses_domain_dkim" "main" {
  domain = aws_ses_domain_identity.main.domain
}

# SES Identity Policy for sending emails
resource "aws_ses_identity_policy" "main" {
  identity = aws_ses_domain_identity.main.arn
  name     = "${local.name_prefix}-ses-policy"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
        }
        Action = [
          "ses:SendEmail",
          "ses:SendRawEmail"
        ]
        Resource = aws_ses_domain_identity.main.arn
        Condition = {
          StringEquals = {
            "ses:FromAddress" = var.email_from_address
          }
        }
      }
    ]
  })
}

# Email from address identity
resource "aws_ses_email_identity" "from_address" {
  email = var.email_from_address
}

# SES Configuration Set for tracking
resource "aws_ses_configuration_set" "main" {
  name = "${local.name_prefix}-config-set"

  delivery_options {
    tls_policy = "Require"
  }
}

# Event destination for tracking bounces and complaints
resource "aws_ses_event_destination" "cloudwatch" {
  name                   = "cloudwatch"
  configuration_set_name = aws_ses_configuration_set.main.name
  enabled                = true
  matching_types         = ["bounce", "complaint", "delivery", "send", "reject"]

  cloudwatch_destination {
    default_value  = "default"
    dimension_name = "MessageTag"
    value_source   = "messageTag"
  }
}

# IAM policy for SES access
resource "aws_iam_policy" "ses_access" {
  name        = "${local.name_prefix}-ses-access"
  description = "IAM policy for SES email sending"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ses:SendEmail",
          "ses:SendRawEmail",
          "ses:GetSendQuota",
          "ses:GetSendStatistics",
          "ses:GetIdentityVerificationAttributes",
          "ses:GetIdentityDkimAttributes"
        ]
        Resource = [
          aws_ses_domain_identity.main.arn,
          aws_ses_email_identity.from_address.arn,
          "arn:aws:ses:${var.aws_region}:${data.aws_caller_identity.current.account_id}:configuration-set/${aws_ses_configuration_set.main.name}"
        ]
      }
    ]
  })

  tags = local.common_tags
}