# Example Terraform variables for Trialynx Insights deployment
# Copy this file to terraform.tfvars and fill in your actual values

# Basic Configuration
aws_region     = "us-west-2"
environment    = "dev"
project_owner  = "<EMAIL>"

# Domain Configuration
domain_name         = "dev.insights.trialynx.io"
route53_zone_id     = "Z1D633PJN98FT9"  # Route 53 zone ID from prod account

# Bedrock Configuration
bedrock_knowledge_base_id = "M0KKCP0UI9"  # Your Bedrock Knowledge Base ID
bedrock_model_id         = "us.anthropic.claude-sonnet-4-********-v1:0"

# Container Configuration
container_image = "*********.dkr.ecr.us-west-2.amazonaws.com/trialynx-insights-dev-app:latest"
container_port  = 3000

# Scaling Configuration
desired_count = 1
min_capacity  = 1
max_capacity  = 3

# Email Configuration
ses_domain          = "trialynx.io"
email_from_address  = "<EMAIL>"

# CORS Configuration (for development)
cors_allowed_origins = [
  "https://dev.insights.trialynx.io",
  "http://localhost:3000"
]

# JWT Configuration (legacy)
jwt_secret_name = "trialynx-jwt-secret"

# GitHub Configuration for CI/CD
github_repository = "ezresearchsolutions/trialynx-insights"