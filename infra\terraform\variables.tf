variable "aws_region" {
  description = "AWS region to deploy resources"
  type        = string
  default     = "us-east-1"
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
  default     = "dev"
}

variable "project_owner" {
  description = "Email of the project owner"
  type        = string
  default     = "<EMAIL>"
}

variable "bedrock_knowledge_base_id" {
  description = "ID of the Bedrock knowledge base"
  type        = string
  sensitive   = true
}

variable "bedrock_model_id" {
  description = "Model ID for Bedrock inference"
  type        = string
  default     = "anthropic.claude-v2"
}

variable "cors_allowed_origins" {
  description = "Allowed origins for CORS"
  type        = list(string)
  default     = ["http://localhost:3000"]
}

variable "jwt_secret_name" {
  description = "Name of the JWT secret in AWS Secrets Manager"
  type        = string
  default     = "trialynx-jwt-secret"
}

variable "github_repository" {
  description = "GitHub repository in the format 'owner/repo' for OIDC trust relationship"
  type        = string
  default     = "EZ-Research/trialynx-insights"
}

# Domain and DNS variables
variable "domain_name" {
  description = "Domain name for the application"
  type        = string
  default     = "dev.insights.trialynx.io"
}

variable "route53_zone_id" {
  description = "Route 53 zone ID for the domain (in prod account) - optional when using subdomain delegation"
  type        = string
  default     = null
  sensitive   = true
}

# ECS and container variables
variable "container_image" {
  description = "Container image URI"
  type        = string
  default     = "nginx:latest" # Will be replaced with actual ECR URI
}

variable "container_port" {
  description = "Port the container listens on"
  type        = number
  default     = 3000
}

variable "desired_count" {
  description = "Desired number of ECS tasks"
  type        = number
  default     = 1
}

variable "min_capacity" {
  description = "Minimum number of ECS tasks"
  type        = number
  default     = 1
}

variable "max_capacity" {
  description = "Maximum number of ECS tasks"
  type        = number
  default     = 3
}

# SES variables
variable "ses_domain" {
  description = "Domain for SES email sending"
  type        = string
  default     = "trialynx.io"
}

variable "email_from_address" {
  description = "From email address for authentication emails"
  type        = string
  default     = "<EMAIL>"
}