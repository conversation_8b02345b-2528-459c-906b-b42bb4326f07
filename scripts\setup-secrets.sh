#!/bin/bash

# Setup Secrets Manager secrets for Trialynx Insights

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

ENVIRONMENT=${ENVIRONMENT:-dev}
AWS_REGION=${AWS_REGION:-us-west-2}

echo -e "${BLUE}🔐 Setting up Secrets Manager secrets for $ENVIRONMENT environment${NC}"

# Function to check if secret exists
secret_exists() {
    aws secretsmanager describe-secret --secret-id "$1" >/dev/null 2>&1
}

# Function to update or create secret
update_secret() {
    local secret_id="$1"
    local secret_value="$2"
    
    if secret_exists "$secret_id"; then
        echo -e "${YELLOW}Updating existing secret: $secret_id${NC}"
        aws secretsmanager put-secret-value \
            --secret-id "$secret_id" \
            --secret-string "$secret_value"
    else
        echo -e "${RED}Secret not found: $secret_id${NC}"
        echo -e "${RED}Please deploy infrastructure first${NC}"
        exit 1
    fi
}

# Generate Auth.js secret
echo -e "${GREEN}1. Setting up Auth.js secret...${NC}"
AUTH_SECRET=$(openssl rand -base64 32)
update_secret "trialynx-insights-$ENVIRONMENT-auth-secret" "$AUTH_SECRET"

# OAuth secrets
echo -e "${GREEN}2. Setting up OAuth secrets...${NC}"
echo -e "${YELLOW}Please provide OAuth credentials:${NC}"

read -p "Google Client ID: " GOOGLE_ID
read -p "Google Client Secret: " GOOGLE_SECRET
read -p "Azure AD Application ID: " AZURE_ID
read -p "Azure AD Application Secret: " AZURE_SECRET
read -p "Azure AD Tenant ID: " AZURE_TENANT

OAUTH_SECRETS=$(cat <<EOF
{
  "AUTH_GOOGLE_ID": "$GOOGLE_ID",
  "AUTH_GOOGLE_SECRET": "$GOOGLE_SECRET",
  "AUTH_AZURE_AD_ID": "$AZURE_ID",
  "AUTH_AZURE_AD_SECRET": "$AZURE_SECRET",
  "AUTH_AZURE_AD_TENANT_ID": "$AZURE_TENANT"
}
EOF
)

update_secret "trialynx-insights-$ENVIRONMENT-oauth-secrets" "$OAUTH_SECRETS"

# Email configuration
echo -e "${GREEN}3. Setting up email configuration...${NC}"
echo -e "${YELLOW}SES SMTP configuration:${NC}"

read -p "SES SMTP Username: " SES_USERNAME
read -p "SES SMTP Password: " SES_PASSWORD
read -p "SES SMTP Region (default: us-west-2): " SES_REGION
SES_REGION=${SES_REGION:-us-west-2}

EMAIL_SERVER="smtps://$SES_USERNAME:$SES_PASSWORD@email-smtp.$SES_REGION.amazonaws.com:465"
update_secret "trialynx-insights-$ENVIRONMENT-email-secret" "$EMAIL_SERVER"

echo -e "${GREEN}✅ All secrets configured successfully!${NC}"
echo -e "${BLUE}Next steps:${NC}"
echo -e "${BLUE}1. Restart ECS service to pick up new secrets${NC}"
echo -e "${BLUE}2. Test authentication flows${NC}"